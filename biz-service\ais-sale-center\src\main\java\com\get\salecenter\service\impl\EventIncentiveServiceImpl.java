package com.get.salecenter.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.UserInfo;
import com.get.core.secure.utils.GetAuthInfo;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.core.tool.utils.RequestContextUtil;
import com.get.financecenter.feign.IFinanceCenterClient;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.permissioncenter.vo.StaffVo;
import com.get.remindercenter.dto.RemindTaskDto;
import com.get.remindercenter.enums.EmailTemplateEnum;
import com.get.remindercenter.feign.IReminderCenterClient;
import com.get.salecenter.dao.sale.EventIncentiveMapper;
import com.get.salecenter.dto.CommentDto;
import com.get.salecenter.dto.EventIncentiveDistributeDto;
import com.get.salecenter.dto.EventIncentiveListDto;
import com.get.salecenter.dto.EventIncentiveReminderDto;
import com.get.salecenter.dto.EventIncentiveUpdateDto;
import com.get.salecenter.dto.MediaAndAttachedDto;
import com.get.salecenter.entity.EventIncentive;
import com.get.salecenter.entity.EventIncentiveAreaCountry;
import com.get.salecenter.entity.EventIncentiveCost;
import com.get.salecenter.entity.SaleComment;
import com.get.salecenter.entity.SaleMediaAndAttached;
import com.get.salecenter.service.AsyncReminderService;
import com.get.salecenter.service.ICommentService;
import com.get.salecenter.service.IEventIncentiveAreaCountryService;
import com.get.salecenter.service.IEventIncentiveCostService;
import com.get.salecenter.service.IEventIncentiveService;
import com.get.salecenter.service.IMediaAndAttachedService;
import com.get.salecenter.utils.MyStringUtils;
import com.get.salecenter.vo.CommentVo;
import com.get.salecenter.vo.EventIncentiveListVo;
import com.get.salecenter.vo.EventIncentiveVo;
import com.get.salecenter.vo.MediaAndAttachedVo;
import com.google.common.collect.Maps;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.StringJoiner;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * @author: Hardy
 * @create: 2022/7/19 14:49
 * @verison: 1.0
 * @description:
 */
@Service
public class EventIncentiveServiceImpl extends ServiceImpl<EventIncentiveMapper, EventIncentive> implements IEventIncentiveService {

    private final static String NUM_PREFIX = "IEVT";
    @Resource
    private EventIncentiveMapper eventIncentiveMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private IEventIncentiveAreaCountryService eventIncentiveAreaCountryService;
    @Resource
    private IInstitutionCenterClient institutionCenterClient;
    @Resource
    private IFinanceCenterClient financeCenterClient;
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Resource
    private IEventIncentiveCostService eventIncentiveCostService;
    @Resource
    private IMediaAndAttachedService attachedService;
    @Resource
    private ICommentService commentService;
    @Resource
    private AsyncReminderService asyncReminderService;
    @Resource
    private IMediaAndAttachedService mediaAndAttachedService;
    @Resource
    private IReminderCenterClient reminderCenterClient;


    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long addEventIncentive(EventIncentiveUpdateDto eventIncentiveUpdateDto) {
        if (GeneralTool.isEmpty(eventIncentiveUpdateDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        EventIncentive eventIncentive = new EventIncentive();
        BeanCopyUtils.copyProperties(eventIncentiveUpdateDto,eventIncentive);
        //默认设置有效
        eventIncentive.setStatus(0);
        utilService.setCreateInfo(eventIncentive);
        int i = eventIncentiveMapper.insert(eventIncentive);

        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
        }
        //获取自动生成编号
        eventIncentive.setNum(MyStringUtils.getNumCommon(NUM_PREFIX,eventIncentive.getId()));
        eventIncentiveMapper.updateById(eventIncentive);

        //绑定业务国家
        if (GeneralTool.isNotEmpty(eventIncentiveUpdateDto.getFkAreaCountryIdList())){
            List<EventIncentiveAreaCountry> eventIncentiveAreaCountries = new ArrayList<>();
            for (Long fkAreaCountryId : eventIncentiveUpdateDto.getFkAreaCountryIdList()) {
                EventIncentiveAreaCountry eventIncentiveAreaCountry = new EventIncentiveAreaCountry();
                eventIncentiveAreaCountry.setFkEventIncentiveId(eventIncentive.getId());
                eventIncentiveAreaCountry.setFkAreaCountryId(fkAreaCountryId);
                utilService.updateUserInfoToEntity(eventIncentiveAreaCountry);
                eventIncentiveAreaCountries.add(eventIncentiveAreaCountry);
            }
            eventIncentiveAreaCountryService.saveBatch(eventIncentiveAreaCountries);
        }


        if (GeneralTool.isNotEmpty(eventIncentiveUpdateDto.getSuggestCheckTime())){
            // TODO: 2022/7/19 异步发送提醒
            Map<String, String> headerMap = RequestContextUtil.getHeaderMap();
            UserInfo user = GetAuthInfo.getUser();
            EventIncentiveReminderDto eventIncentiveReminderVo = new EventIncentiveReminderDto();
            BeanUtils.copyProperties(eventIncentiveUpdateDto,eventIncentiveReminderVo);
            eventIncentiveReminderVo.setFkEventIncentiveId(eventIncentive.getId());
            //TODO EVENT_INCENTIVE_NOTICE
            String title = "【奖励推广活动核对提醒】" + eventIncentive.getEventTitle();
            asyncReminderService.doAddEventIncentiveReminders(headerMap,user,eventIncentiveReminderVo, title);
        }

        return eventIncentive.getId();
    }



    @Override
    public EventIncentiveVo findEventIncentiveById(Long id) {
        if (GeneralTool.isEmpty(id)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        EventIncentive eventIncentive = eventIncentiveMapper.selectById(id);
        EventIncentiveVo eventIncentiveVo = new EventIncentiveVo();
        BeanCopyUtils.copyProperties(eventIncentive, eventIncentiveVo);

        List<Long> list = new ArrayList<>();
        list.add(GetAuthInfo.getStaffId());
        Result<List<Long>> result = permissionCenterClient.getStaffFollowerIds(GetAuthInfo.getStaffId());
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            List<Long> staffFollowerIds = result.getData();
            if (GeneralTool.isNotEmpty(staffFollowerIds)) {
                list.addAll(staffFollowerIds);
                if (GeneralTool.isNotEmpty(eventIncentiveVo.getGmtCreateUser())) {
                    Result<StaffVo> staffDtoResult = permissionCenterClient.getStaffByCreateUser(eventIncentiveVo.getGmtCreateUser());
                    if (staffDtoResult.isSuccess() && staffDtoResult.getData() != null) {
                        StaffVo staffByCreateUser = staffDtoResult.getData();
                        if (GeneralTool.isNotEmpty(staffByCreateUser)) {
                            eventIncentiveVo.setVisitStatus(list.contains(staffByCreateUser.getId()));
                        }
                    }

                }
            }
        }

        //设置属性的名称
        setEventIncentiveDtoName(eventIncentiveVo);
        return eventIncentiveVo;
    }



    @Override
    public List<EventIncentiveListVo> getEventIncentives(EventIncentiveListDto eventIncentiveListDto, Page page) {
        if (GeneralTool.isEmpty(eventIncentiveListDto)) {
            if (GeneralTool.isEmpty(eventIncentiveListDto.getFkCompanyId())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
            }
        }
        if (GeneralTool.isNotEmpty(eventIncentiveListDto.getEventTitle())) {
            eventIncentiveListDto.setEventTitle(eventIncentiveListDto.getEventTitle().trim());
        }
        if (GeneralTool.isNotEmpty(eventIncentiveListDto.getNum())) {
            eventIncentiveListDto.setNum(eventIncentiveListDto.getNum().trim());
        }

        IPage<EventIncentiveListVo> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<EventIncentiveListVo> eventIncentiveListVos = eventIncentiveMapper.getEventIncentives(iPage, eventIncentiveListDto);
        page.setAll((int) iPage.getTotal());

        if (GeneralTool.isEmpty(eventIncentiveListVos)){
            return Collections.emptyList();
        }
        List<Long> ids = eventIncentiveListVos.stream().map(EventIncentiveListVo::getId).filter(Objects::nonNull).collect(Collectors.toList());



        List<SaleMediaAndAttached> saleMediaAndAttacheds = attachedService.list(Wrappers.<SaleMediaAndAttached>lambdaQuery().in(SaleMediaAndAttached::getFkTableId, ids)
                .eq(SaleMediaAndAttached::getFkTableName, TableEnum.SALE_EVENT_INCENTIVE.key));

        Map<Long, List<MediaAndAttachedVo>> mediaAndAttachedDtoMap = null;
        if (GeneralTool.isNotEmpty(saleMediaAndAttacheds)){
            List<MediaAndAttachedVo> fileMedias = attachedService.getFileMedia(saleMediaAndAttacheds);
            if (GeneralTool.isNotEmpty(fileMedias)){
                mediaAndAttachedDtoMap = fileMedias.stream().collect(Collectors.groupingBy(MediaAndAttachedVo::getFkTableId));
            }
        }


        Set<String> createUsers = eventIncentiveListVos.stream().map(EventIncentiveListVo::getGmtCreateUser).filter(Objects::nonNull).collect(Collectors.toSet());
        List<Long> staffFollowerIds = new ArrayList<>();
        Result<List<Long>> result = permissionCenterClient.getStaffFollowerIds(GetAuthInfo.getStaffId());
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            staffFollowerIds = result.getData();
        }

        List<StaffVo> staffVos = permissionCenterClient.getStaffByCreateUsers(createUsers).getData();
        Map<String, List<StaffVo>> usersMap = null;
        if (GeneralTool.isNotEmpty(staffVos)){
            usersMap = staffVos.stream().collect(Collectors.groupingBy(StaffVo::getGmtCreateUser));
        }

        List<Long> list = new ArrayList<>();
        list.add(GetAuthInfo.getStaffId());
        for (EventIncentiveListVo eventIncentiveListVo : eventIncentiveListVos) {
            if (GeneralTool.isNotEmpty(mediaAndAttachedDtoMap)&&GeneralTool.isNotEmpty(mediaAndAttachedDtoMap.get(eventIncentiveListVo.getId()))){
                eventIncentiveListVo.setMediaAndAttachedDtos(mediaAndAttachedDtoMap.get(eventIncentiveListVo.getId()));
            }
            if (GeneralTool.isNotEmpty(usersMap)&&GeneralTool.isNotEmpty(usersMap.get(eventIncentiveListVo.getGmtCreateUser()))){
                List<StaffVo> staffVoList = usersMap.get(eventIncentiveListVo.getGmtCreateUser());
                if (GeneralTool.isNotEmpty(staffVoList)){
                    eventIncentiveListVo.setVisitStatus(list.contains(staffVoList.get(0).getId()));
                }
            }

            if (GeneralTool.isNotEmpty(eventIncentiveListVo.getPublicLevel())){
                StringJoiner stringJoiner = new StringJoiner(" ");
                String[] publicLevelString = eventIncentiveListVo.getPublicLevel().split(",");
                for (String name : publicLevelString) {
                    stringJoiner.add(ProjectExtraEnum.getValueByKey(Integer.valueOf(name), ProjectExtraEnum.EVENT_PUBLIC_LEVEL));
                }
                eventIncentiveListVo.setPublicLevelName(stringJoiner.toString());
            }

        }

        //设置列表属性的名称
        setEventIncentiveDtoListName(eventIncentiveListVos);
        return eventIncentiveListVos;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public EventIncentiveVo updateEventIncentive(EventIncentiveUpdateDto eventIncentiveUpdateDto) {

        if (GeneralTool.isEmpty(eventIncentiveUpdateDto.getId())){
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }

        EventIncentive eventIncentive = eventIncentiveMapper.selectById(eventIncentiveUpdateDto.getId());
        if (GeneralTool.isEmpty(eventIncentive)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }

        boolean sendMailFlag = false;
        if (!Objects.equals(eventIncentiveUpdateDto.getSuggestCheckTime(),eventIncentive.getSuggestCheckTime())
                &&GeneralTool.isNotEmpty(eventIncentiveUpdateDto.getSuggestCheckTime())){
            sendMailFlag = true;
        }
        validateUpdate(eventIncentiveUpdateDto);
        BeanCopyUtils.copyProperties(eventIncentiveUpdateDto,eventIncentive);
        utilService.setUpdateInfo(eventIncentive);
        eventIncentiveMapper.updateByIdWithNull(eventIncentive);

        //删除关系表
        eventIncentiveAreaCountryService.remove(Wrappers.<EventIncentiveAreaCountry>lambdaQuery().eq(EventIncentiveAreaCountry::getFkEventIncentiveId, eventIncentiveUpdateDto.getId()));

        //绑定业务国家
        if (GeneralTool.isNotEmpty(eventIncentiveUpdateDto.getFkAreaCountryIdList())){
            List<EventIncentiveAreaCountry> eventIncentiveAreaCountries = new ArrayList<>();
            for (Long fkAreaCountryId : eventIncentiveUpdateDto.getFkAreaCountryIdList()) {
                EventIncentiveAreaCountry eventIncentiveAreaCountry = new EventIncentiveAreaCountry();
                eventIncentiveAreaCountry.setFkEventIncentiveId(eventIncentive.getId());
                eventIncentiveAreaCountry.setFkAreaCountryId(fkAreaCountryId);
                utilService.setCreateInfo(eventIncentiveAreaCountry);
                eventIncentiveAreaCountries.add(eventIncentiveAreaCountry);
            }
            eventIncentiveAreaCountryService.saveBatch(eventIncentiveAreaCountries);
        }

        if (sendMailFlag){
            // TODO: 2022/7/20 异步发送邮件 需要自定义模板
            Map<String, String> headerMap = RequestContextUtil.getHeaderMap();
            UserInfo user = GetAuthInfo.getUser();
            EventIncentiveReminderDto eventIncentiveReminderVo = new EventIncentiveReminderDto();
            BeanUtils.copyProperties(eventIncentiveUpdateDto,eventIncentiveReminderVo);
            eventIncentiveReminderVo.setFkEventIncentiveId(eventIncentive.getId());
            //TODO EVENT_INCENTIVE_NOTICE
            String title = "【奖励推广活动核对提醒】" + eventIncentive.getEventTitle();
            List<RemindTaskDto> remindTaskVos = new ArrayList<>();
            RemindTaskDto remindTaskVo = new RemindTaskDto();
            //默认背景颜色
            remindTaskVo.setFkRemindEventTypeKey(EmailTemplateEnum.REWARD_PROMOTION_ACTIVITY_REMINDER.getEmailTemplateKey());
            remindTaskVo.setFkTableName(TableEnum.SALE_EVENT_INCENTIVE.key);
            remindTaskVo.setFkTableId(eventIncentiveReminderVo.getFkEventIncentiveId());
            remindTaskVo.setFkDbName(ProjectKeyEnum.SALE_CENTER.key);
            remindTaskVos.add(remindTaskVo);
            try {
                reminderCenterClient.batchDeleteTaskNew(remindTaskVos);
            }catch (Exception e){
                log.error("奖励活动推广提醒任务删除失败",e);
            }

            asyncReminderService.doAddEventIncentiveReminders(headerMap,user,eventIncentiveReminderVo,title);
        }
//        else {
//            try {
//                reminderCenterClient.batchUpdate(new ArrayList<>(),TableEnum.SALE_EVENT_INCENTIVE.key, eventIncentiveUpdateDto.getId());
//            }catch (Exception e){
//                log.error("奖励活动推广提醒任务删除失败",e);
//            }
//        }

        return findEventIncentiveById(eventIncentive.getId());
    }

    @Override
    public List<MediaAndAttachedVo> getItemMedia(MediaAndAttachedDto attachedVo, Page page) {
        if (GeneralTool.isEmpty(attachedVo.getFkTableId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        attachedVo.setFkTableName(TableEnum.SALE_EVENT_INCENTIVE.key);
        return attachedService.getMediaAndAttachedDto(attachedVo, page);
    }

    @Override
    public List<MediaAndAttachedVo> addItemMedia(ValidList<MediaAndAttachedDto> mediaAttachedVos) {
        if (GeneralTool.isEmpty(mediaAttachedVos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_vo_null"));
        }
        List<MediaAndAttachedVo> mediaAndAttachedVos = new ArrayList<>();
        for (MediaAndAttachedDto mediaAndAttachedDto : mediaAttachedVos) {
            //设置插入的表
            mediaAndAttachedDto.setFkTableName(TableEnum.SALE_EVENT_INCENTIVE.key);
            mediaAndAttachedVos.add(attachedService.addMediaAndAttached(mediaAndAttachedDto));
        }
        return mediaAndAttachedVos;

    }

    @Override
    public Long editComment(CommentDto commentDto) {
        SaleComment comment = BeanCopyUtils.objClone(commentDto, SaleComment::new);
        if (GeneralTool.isNotEmpty(commentDto)) {
            if (GeneralTool.isNotEmpty(commentDto.getId())) {
                comment.setFkTableName(TableEnum.SALE_EVENT_INCENTIVE.key);
                commentService.updateComment(comment);
            } else {
                comment.setFkTableName(TableEnum.SALE_EVENT_INCENTIVE.key);
                commentService.addComment(comment);
            }
        }
        return comment.getId();

    }

    @Override
    public List<CommentVo> getComments(CommentDto commentDto, Page page) {
        commentDto.setFkTableName(TableEnum.SALE_EVENT_INCENTIVE.key);
        return commentService.datas(commentDto, page);

    }

    @Override
    public List<EventIncentiveListVo> getEventIncentiveList(EventIncentiveDistributeDto eventIncentiveDistributeDto, Page page) {
        if (GeneralTool.isEmpty(eventIncentiveDistributeDto)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        if (GeneralTool.isEmpty(eventIncentiveDistributeDto.getFkEventBillId())){
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }

        IPage<EventIncentiveListVo> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<EventIncentiveListVo> eventIncentiveListVos = eventIncentiveMapper.getEventIncentiveList(iPage, eventIncentiveDistributeDto);
        page.setAll((int) iPage.getTotal());
        if (GeneralTool.isEmpty(eventIncentiveListVos)){
            return Collections.emptyList();
        }

        //设置名称
        setEventIncentiveDtoListName(eventIncentiveListVos);

        return eventIncentiveListVos;
    }

    //0计划/1结束/2取消/3延期
    @Override
    public void end(Long eventIncentiveId) {
        if (GeneralTool.isEmpty(eventIncentiveId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        EventIncentive eventIncentive = eventIncentiveMapper.selectById(eventIncentiveId);
        eventIncentive.setId(eventIncentiveId);
        eventIncentive.setStatus(1);
        utilService.setUpdateInfo(eventIncentive);
        int i = eventIncentiveMapper.updateById(eventIncentive);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }
    }

    @Override
    public void plan(Long eventIncentiveId) {
        if (GeneralTool.isEmpty(eventIncentiveId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        EventIncentive eventIncentive = eventIncentiveMapper.selectById(eventIncentiveId);
        eventIncentive.setId(eventIncentiveId);
        eventIncentive.setStatus(0);
        utilService.setUpdateInfo(eventIncentive);
        int i = eventIncentiveMapper.updateById(eventIncentive);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }
    }

    @Override
    public void postpone(Long eventIncentiveId) {
        if (GeneralTool.isEmpty(eventIncentiveId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        EventIncentive eventIncentive = eventIncentiveMapper.selectById(eventIncentiveId);
        eventIncentive.setId(eventIncentiveId);
        eventIncentive.setStatus(3);
        utilService.setUpdateInfo(eventIncentive);
        int i = eventIncentiveMapper.updateById(eventIncentive);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }
    }

    @Override
    public void cancel(Long eventIncentiveId) {
        if (GeneralTool.isEmpty(eventIncentiveId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        EventIncentive eventIncentive = eventIncentiveMapper.selectById(eventIncentiveId);
        eventIncentive.setId(eventIncentiveId);
        eventIncentive.setStatus(2);
        utilService.setUpdateInfo(eventIncentive);
        int i = eventIncentiveMapper.updateById(eventIncentive);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        if (eventIncentiveMapper.selectById(id) == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }

        //验证删除
        List<EventIncentiveCost> eventIncentiveCosts = eventIncentiveCostService.list(Wrappers.<EventIncentiveCost>lambdaQuery().eq(EventIncentiveCost::getFkEventIncentiveId, id));
        if (GeneralTool.isNotEmpty(eventIncentiveCosts)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("event_cost_data_association"));
        }

        List<SaleMediaAndAttached> saleMediaAndAttacheds = mediaAndAttachedService.list(Wrappers.<SaleMediaAndAttached>lambdaQuery()
                .eq(SaleMediaAndAttached::getFkTableName, TableEnum.SALE_EVENT_INCENTIVE.key)
                .eq(SaleMediaAndAttached::getFkTableId, id));

        //删除附件
        if (GeneralTool.isNotEmpty(saleMediaAndAttacheds)){
            for (SaleMediaAndAttached saleMediaAndAttached : saleMediaAndAttacheds) {
                mediaAndAttachedService.deleteMediaAttached(saleMediaAndAttached.getId());
            }
        }

        //删除关系表
        eventIncentiveAreaCountryService.remove(Wrappers.<EventIncentiveAreaCountry>lambdaQuery()
                .eq(EventIncentiveAreaCountry::getFkEventIncentiveId,id));

        eventIncentiveMapper.deleteById(id);
    }

    private void validateUpdate(EventIncentiveUpdateDto eventIncentiveUpdateDto) {
        List<EventIncentiveCost> eventIncentiveCosts = eventIncentiveCostService.list(Wrappers.<EventIncentiveCost>lambdaQuery().eq(EventIncentiveCost::getFkEventIncentiveId, eventIncentiveUpdateDto.getId()));
        if (GeneralTool.isNotEmpty(eventIncentiveCosts)){
            if (!eventIncentiveCosts.get(0).getFkCurrencyTypeNum().equals(eventIncentiveUpdateDto.getFkCurrencyTypeNum())){
                throw new GetServiceException(LocaleMessageUtils.getMessage("event_incentive_currency_not_same"));
            }
        }
    }


    private void setEventIncentiveDtoListName(List<EventIncentiveListVo> eventIncentiveListVos) {
        //ids
        List<Long> ids = eventIncentiveListVos.stream().map(EventIncentiveListVo::getId).collect(Collectors.toList());

        List<EventIncentiveListVo> eventIncentiveListDtoList = eventIncentiveMapper.getAmountReceivableCurrencyNumbyEventIncentiveIds(ids);
        eventIncentiveListDtoList.removeIf(Objects::isNull);
        Set<String> currencyNum = new HashSet<>();
        Map<Long, String> amountReceivableCurrencyNumMap = Maps.newHashMap();
        if (GeneralTool.isNotEmpty(eventIncentiveListDtoList)){
            currencyNum = eventIncentiveListDtoList.stream().map(EventIncentiveListVo::getAmountReceivableCurrencyNum).collect(Collectors.toSet());
            amountReceivableCurrencyNumMap = eventIncentiveListDtoList.stream().filter(e->GeneralTool.isNotEmpty(e.getFkEventIncentiveCostId())).collect(HashMap::new, (m, v) -> m.put(v.getFkEventIncentiveCostId(), v.getAmountReceivableCurrencyNum()), HashMap::putAll);
        }


        //公司id
        Set<Long> companyIds = eventIncentiveListVos.stream().map(EventIncentiveListVo::getFkCompanyId).collect(Collectors.toSet());
        Set<String> currencyTypeNums = eventIncentiveListVos.stream().map(EventIncentiveListVo::getFkCurrencyTypeNum).filter(Objects::nonNull).collect(Collectors.toSet());
        currencyTypeNums.addAll(currencyNum);

        List<EventIncentiveAreaCountry> eventIncentiveAreaCountries = eventIncentiveAreaCountryService.list(Wrappers.<EventIncentiveAreaCountry>lambdaQuery()
                .in(EventIncentiveAreaCountry::getFkEventIncentiveId, ids));

        Map<Long, List<EventIncentiveAreaCountry>> eventIncentiveAreaCountryMap = null;
        Set<Long> countryIds = new HashSet<>();
        if (GeneralTool.isNotEmpty(eventIncentiveAreaCountries)){
            eventIncentiveAreaCountryMap = eventIncentiveAreaCountries.stream().collect(Collectors.groupingBy(EventIncentiveAreaCountry::getFkEventIncentiveId));
            countryIds = eventIncentiveAreaCountries.stream().map(EventIncentiveAreaCountry::getFkAreaCountryId).collect(Collectors.toSet());
        }
        //国家
        Map<Long, String> countryNameMap = institutionCenterClient.getCountryFullNamesByIds(countryIds).getData();
        //公司
        Map<Long, String> companyNameMap = permissionCenterClient.getCompanyNamesByIds(companyIds).getData();
        //币种
        Map<String, String> currencyNameMap = financeCenterClient.getCurrencyNamesByNums(currencyTypeNums).getData();




        for (EventIncentiveListVo eventIncentiveListVo : eventIncentiveListVos) {
            //状态名称
            if (GeneralTool.isNotEmpty(eventIncentiveListVo.getStatus())){
                ProjectExtraEnum[] eventStatusArray = ProjectExtraEnum.EVENT_STATUS;
                ProjectExtraEnum eventStatus = null;
                for (ProjectExtraEnum status : eventStatusArray) {
                    if (status.key.equals(eventIncentiveListVo.getStatus())){
                        eventStatus = status;
                        break;
                    }
                }
                if (GeneralTool.isNotEmpty(eventStatus)){
                    eventIncentiveListVo.setStatusName(LocaleMessageUtils.getMessage(eventStatus.name()));
                }
            }

            //国家名称
            if (GeneralTool.isNotEmpty(eventIncentiveAreaCountryMap)&&GeneralTool.isNotEmpty(eventIncentiveAreaCountryMap.get(eventIncentiveListVo.getId()))){
                List<EventIncentiveAreaCountry> eventIncentiveAreaCountryList = eventIncentiveAreaCountryMap.get(eventIncentiveListVo.getId());
                if (GeneralTool.isNotEmpty(eventIncentiveAreaCountryList)){
                    StringJoiner stringJoiner = new StringJoiner(",");
                    for (EventIncentiveAreaCountry eventIncentiveAreaCountry : eventIncentiveAreaCountryList) {
                        if (GeneralTool.isNotEmpty(countryNameMap)&&GeneralTool.isNotEmpty(countryNameMap.get(eventIncentiveAreaCountry.getFkAreaCountryId()))){
                            stringJoiner.add(countryNameMap.get(eventIncentiveAreaCountry.getFkAreaCountryId()));
                        }
                    }
                    eventIncentiveListVo.setFkAreaCountryName(stringJoiner.toString());
                }
            }

            //公司名称
            if (GeneralTool.isNotEmpty(companyNameMap)){
                eventIncentiveListVo.setFkCompanyName(companyNameMap.get(eventIncentiveListVo.getFkCompanyId()));
            }

            //设置币种名称
            if (GeneralTool.isNotEmpty(currencyNameMap)&&GeneralTool.isNotEmpty(eventIncentiveListVo.getFkCurrencyTypeNum())){
                String currencyName = currencyNameMap.get(eventIncentiveListVo.getFkCurrencyTypeNum());
                eventIncentiveListVo.setFkCurrencyTypeNumName(currencyName);
                if (GeneralTool.isNotEmpty(eventIncentiveListVo.getActualPayAmount())){
                    eventIncentiveListVo.setActualPayAmountCurrency(eventIncentiveListVo.getActualPayAmount().toString()+currencyName);
                }
                if (GeneralTool.isNotEmpty(eventIncentiveListVo.getActualReceivableAmount())){
                    eventIncentiveListVo.setActualReceivableAmountCurrency(eventIncentiveListVo.getActualReceivableAmount().toString()+currencyName);
                }
                if (GeneralTool.isNotEmpty(eventIncentiveListVo.getBalanceAmount())){
                    eventIncentiveListVo.setBalanceAmountCurrency(eventIncentiveListVo.getBalanceAmount().toString()+currencyName);
                }
                if (GeneralTool.isNotEmpty(eventIncentiveListVo.getAmount())){
                    eventIncentiveListVo.setAmountCurrency(eventIncentiveListVo.getAmount().toString()+currencyName);
                }

            }

            if(GeneralTool.isNotEmpty(amountReceivableCurrencyNumMap)&&GeneralTool.isNotEmpty(amountReceivableCurrencyNumMap.get(eventIncentiveListVo.getFkEventIncentiveCostId()))){
                String currencyName = "";
                String currencyType = amountReceivableCurrencyNumMap.get(eventIncentiveListVo.getFkEventIncentiveCostId());
                if (GeneralTool.isNotEmpty(currencyNameMap)&&GeneralTool.isNotEmpty(currencyNameMap.get(currencyType))){
                    currencyName = currencyNameMap.get(currencyType);
                    //折合金额的币种
                    if (GeneralTool.isNotEmpty(eventIncentiveListVo.getAmountReceivable())){
                        eventIncentiveListVo.setAmountReceivableCurrency(eventIncentiveListVo.getAmountReceivable().toString()+currencyName);
                    }

                }
            }
        }
    }


    private void setEventIncentiveDtoName(EventIncentiveVo eventIncentiveVo) {
        //状态名称
//        if (GeneralTool.isNotEmpty(eventIncentiveVo.getStatus())){
//            eventIncentiveVo.setStatusName(LocaleMessageUtils.getMessage(ProjectExtraEnum.getValueByKey(eventIncentiveVo.getStatus(),ProjectExtraEnum.EVENT_STATUS)));
//        }
        if (GeneralTool.isNotEmpty(eventIncentiveVo.getFkCompanyId())){
            String fkCompanyName = permissionCenterClient.getCompanyNameById(eventIncentiveVo.getFkCompanyId()).getData();
            eventIncentiveVo.setFkCompanyName(fkCompanyName);
        }

        if (GeneralTool.isNotEmpty(eventIncentiveVo.getStatus())){
            ProjectExtraEnum[] eventStatusArray = ProjectExtraEnum.EVENT_STATUS;
            ProjectExtraEnum eventStatus = null;
            for (ProjectExtraEnum status : eventStatusArray) {
                if (status.key.equals(eventIncentiveVo.getStatus())){
                    eventStatus = status;
                    break;
                }
            }
            if (GeneralTool.isNotEmpty(eventStatus)){
                eventIncentiveVo.setStatusName(LocaleMessageUtils.getMessage(eventStatus.name()));
            }
        }


        //业务国家
        List<EventIncentiveAreaCountry> eventIncentiveAreaCountries = eventIncentiveAreaCountryService.list(Wrappers.<EventIncentiveAreaCountry>lambdaQuery().eq(EventIncentiveAreaCountry::getFkEventIncentiveId, eventIncentiveVo.getId()));
        if (GeneralTool.isNotEmpty(eventIncentiveAreaCountries)){
            List<Long> countryList = eventIncentiveAreaCountries.stream().map(EventIncentiveAreaCountry::getFkAreaCountryId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
            eventIncentiveVo.setFkAreaCountryIdList(countryList);
            if (GeneralTool.isNotEmpty(countryList)){
                Result<Map<Long, String>> countryNamesByIdsResult = institutionCenterClient.getCountryFullNamesByIds(new HashSet<>(countryList));
                if (countryNamesByIdsResult.isSuccess()&&GeneralTool.isNotEmpty(countryNamesByIdsResult.getData())){
                    Map<Long, String> data = countryNamesByIdsResult.getData();
                    StringJoiner sj = new StringJoiner(",");
                    for (Long aLong : countryList) {
                        if (GeneralTool.isNotEmpty(data.get(aLong))){
                            sj.add(data.get(aLong));
                        }
                    }
                    eventIncentiveVo.setFkAreaCountryName(sj.toString());
                }
            }
        }

        //提供商名称
        String institutionProviderName = "";
        Result<String> institutionProviderNameResult = institutionCenterClient.getInstitutionProviderName(eventIncentiveVo.getFkInstitutionProviderId());
        if (institutionProviderNameResult.isSuccess() && GeneralTool.isNotEmpty(institutionProviderNameResult.getData())) {
            institutionProviderName = institutionProviderName+institutionProviderNameResult.getData();
            eventIncentiveVo.setFkInstitutionProviderName(institutionProviderName);
        }

        //币种名称
        if (GeneralTool.isNotEmpty(eventIncentiveVo.getFkCurrencyTypeNum())) {
            Result<String> currencyNameByNum = financeCenterClient.getCurrencyNameByNum(eventIncentiveVo.getFkCurrencyTypeNum());
            String data = currencyNameByNum.getData();
            eventIncentiveVo.setFkCurrencyTypeNumName(data);
        }
        if (GeneralTool.isNotEmpty(eventIncentiveVo.getPublicLevel())) {
            StringJoiner stringJoiner = new StringJoiner(" ");
            String[] publicLevelString = eventIncentiveVo.getPublicLevel().split(",");
            for (String name : publicLevelString) {
                stringJoiner.add(ProjectExtraEnum.getValueByKey(Integer.valueOf(name), ProjectExtraEnum.EVENT_PUBLIC_LEVEL));
            }
            eventIncentiveVo.setPublicLevelName(stringJoiner.toString());
        }
    }
}
