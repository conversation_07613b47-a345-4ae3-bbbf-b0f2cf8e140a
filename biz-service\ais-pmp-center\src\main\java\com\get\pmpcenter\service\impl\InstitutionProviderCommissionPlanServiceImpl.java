package com.get.pmpcenter.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.alibaba.nacos.common.utils.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.secure.UserInfo;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.utils.DateUtil;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.permissioncenter.vo.workbench.WorkbenchApprovalVo;
import com.get.pmpcenter.dto.agent.PmpWorkbenchApprovalDto;
import com.get.pmpcenter.dto.common.*;
import com.get.pmpcenter.dto.institution.*;
import com.get.pmpcenter.entity.*;
import com.get.pmpcenter.enums.*;
import com.get.pmpcenter.event.publisher.CommissionChangedEventPublisher;
import com.get.pmpcenter.mapper.*;
import com.get.pmpcenter.service.*;
import com.get.pmpcenter.strategy.factory.WorkbenchApprovalStrategyFactory;
import com.get.pmpcenter.utils.EntityCompareUtil;
import com.get.pmpcenter.utils.GeneratorLogsUtil;
import com.get.pmpcenter.vo.common.CountryVo;
import com.get.pmpcenter.vo.common.InstitutionVo;
import com.get.pmpcenter.vo.common.MediaVo;
import com.get.pmpcenter.vo.common.RegionVo;
import com.get.pmpcenter.vo.institution.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author:Oliver
 * @Date: 2025/2/10  15:26
 * @Version 1.0
 */
@Service
@Slf4j
public class InstitutionProviderCommissionPlanServiceImpl extends ServiceImpl<InstitutionProviderCommissionPlanMapper, InstitutionProviderCommissionPlan> implements InstitutionProviderCommissionPlanService {

    @Autowired
    private InstitutionProviderCommissionPlanMapper institutionProviderCommissionPlanMapper;
    @Autowired
    private PmpMediaAndAttachedService pmpMediaAndAttachedService;
    @Autowired
    private InstitutionCenterMapper institutionCenterMapper;
    @Autowired
    private InstitutionProviderCommissionPlanTerritoryService commissionPlanTerritoryService;
    @Autowired
    private InstitutionProviderCommissionPlanTerritoryMapper commissionPlanTerritoryMapper;
    @Autowired
    private InstitutionProviderCommissionPlanInstitutionMapper commissionPlanInstitutionMapper;
    @Autowired
    private InstitutionProviderCommissionService providerCommissionService;
    @Autowired
    private PmpMediaAndAttachedMapper pmpMediaAndAttachedMapper;
    @Autowired
    private CommissionChangedEventPublisher commissionChangedEventPublisher;
    @Autowired
    private AgentCommissionPlanMapper agentCommissionPlanMapper;
    @Autowired
    private LogOperationMapper logOperationMapper;
    @Autowired
    private InstitutionProviderCommissionService commissionService;
    @Autowired
    private InstitutionProviderCommissionPlanInstitutionMapper planInstitutionMapper;
    @Autowired
    private AgentCommissionPlanInstitutionMapper agentCommissionPlanInstitutionMapper;
    @Autowired
    private AgentCommissionPlanService agentCommissionPlanService;
    @Autowired
    private AgentCommissionService agentCommissionService;
    @Autowired
    private InstitutionProviderCommissionPlanApprovalMapper commissionPlanApprovalMapper;
    @Autowired
    private InstitutionProviderCommissionPlanInstitutionService providerCommissionPlanInstitutionService;
    @Autowired
    private AgentCommissionPlanInstitutionService agentCommissionPlanInstitutionService;
    @Autowired
    private PermissionService permissionService;
    @Autowired
    private WorkbenchApprovalStrategyFactory approvalStrategyFactory;
    @Autowired
    private InstitutionProviderContractService providerContractService;
    @Autowired
    private IInstitutionCenterClient institutionCenterClient;


    @Override
    public ProviderCommissionPlanListVo getProviderCommissionPlanList(Long providerContractId) {
        ProviderCommissionPlanListVo result = new ProviderCommissionPlanListVo();
        List<Long> userPlanIds = getUserPermissionPlanIds(providerContractId);
        if (CollectionUtils.isEmpty(userPlanIds)) {
            return result;
        }
        List<Long> companyIds = SecureUtil.getCompanyIds();
        if (CollectionUtils.isEmpty(companyIds)) {
            log.error("登录用户无companyIds,当前登录人:{}", SecureUtil.getLoginId());
        }
        List<ProviderCommissionPlanVo> planList = institutionProviderCommissionPlanMapper.selectContractCommissionPlanList(providerContractId, userPlanIds);
        Long providerId = planList.stream()
                .findFirst()
                .map(ProviderCommissionPlanVo::getFkInstitutionProviderId)
                .orElse(null);
        List<Long> currentInstitutionIds = institutionCenterMapper.getInstitutionListBycompanyIdsAndCountryIds(providerId, null, SecureUtil.getCountryIds())
                .stream().map(InstitutionVo::getInstitutionId).distinct().collect(Collectors.toList());
        planList.stream().forEach(plan -> {
            plan.setCurrentLoginId(SecureUtil.getLoginId());
            plan.setInstitutionCount(0);
            //填充学校数量
            List<InstitutionProviderCommissionPlanInstitution> planInstitutionList = planInstitutionMapper.selectList(new LambdaQueryWrapper<InstitutionProviderCommissionPlanInstitution>()
                    .eq(InstitutionProviderCommissionPlanInstitution::getFkInstitutionProviderCommissionPlanId, plan.getId()));
            if (CollectionUtils.isNotEmpty(planInstitutionList) && CollectionUtils.isNotEmpty(currentInstitutionIds)) {
                List<Long> totalInstitutionCount = planInstitutionList.stream().map(InstitutionProviderCommissionPlanInstitution::getFkInstitutionId).distinct()
                        .collect(Collectors.toList())
                        .stream().filter(institutionId -> currentInstitutionIds.contains(institutionId))
                        .distinct().collect(Collectors.toList());
                plan.setInstitutionCount(totalInstitutionCount.size());
            }
        });
        if (CollectionUtils.isNotEmpty(planList)) {
            List<Long> planIds = planList.stream().map(ProviderCommissionPlanVo::getId).collect(Collectors.toList());
            Set<Long> existPlanIdSet = providerCommissionService.list(new LambdaQueryWrapper<InstitutionProviderCommission>()
                            .in(InstitutionProviderCommission::getFkInstitutionProviderCommissionPlanId, planIds)).stream()
                    .map(InstitutionProviderCommission::getFkInstitutionProviderCommissionPlanId)
                    .collect(Collectors.toSet());
            Map<Long, Boolean> hasCommissionMap = planIds.stream()
                    .collect(Collectors.toMap(
                            Function.identity(),
                            planId -> existPlanIdSet.contains(planId)
                    ));
            Map<Long, List<PlanTerritoryVo>> planTerritories = getPlanTerritories(planIds);
            planList.stream().forEach(plan -> {
                if (Objects.nonNull(planTerritories.get(plan.getId()))) {
                    plan.setPlanTerritoryList(planTerritories.get(plan.getId()));
                } else {
                    plan.setPlanTerritoryList(Arrays.asList(PlanTerritoryVo.createGlobalTerritory()));
                }
                plan.setHasCommission(hasCommissionMap.getOrDefault(plan.getId(), Boolean.FALSE));
            });
        }
        result.setCommissionPlanList(planList);
        List<Long> ids = planList.stream().map(ProviderCommissionPlanVo::getId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(ids)) {
            List<MediaVo> mediaList = pmpMediaAndAttachedService.getMediaList(MediaTableEnum.PROVIDER_COMMISSION_PLAN.getCode(), ids);
            result.setCommissionPlanMediaList(mediaList);
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveProviderCommissionPlan(SaveProviderCommissionPlanDto providerCommissionPlanDto, Boolean saveLog) {
        checkProviderCommissionPlan(providerCommissionPlanDto);
        List<LogDto> logs = new ArrayList<>();
        UserInfo user = SecureUtil.getUser();
        if (Objects.nonNull(providerCommissionPlanDto.getId()) && providerCommissionPlanDto.getId() > 0) {
            InstitutionProviderCommissionPlan commissionPlan = institutionProviderCommissionPlanMapper.selectById(providerCommissionPlanDto.getId());
            if (Objects.isNull(commissionPlan)) {
                log.error("更新学校提供商合同佣金方案计划,佣金方案不存在,参数:{}", JSONObject.toJSONString(providerCommissionPlanDto));
                throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_PLAN_NOT_FOUND", "方案不存在"));
            }
            //校验权限
            if (commissionPlan.getIsLocked().equals(1) && !SecureUtil.getLoginId().equals(commissionPlan.getGmtCreateUser())) {
                log.error("更新合同失败,没有权限,当前操作人:{},合同创建人:{}", user.getLoginId(), commissionPlan.getGmtCreateUser());
                throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_NO_PERMISSION", "您没有权限操作"));
            }

            InstitutionProviderCommissionPlan original = new InstitutionProviderCommissionPlan();
            BeanCopyUtils.copyProperties(commissionPlan, original);

            BeanCopyUtils.copyProperties(providerCommissionPlanDto, commissionPlan);
            commissionPlan.setFkInstitutionProviderId(providerCommissionPlanDto.getInstitutionProviderId());
            commissionPlan.setFkInstitutionProviderContractId(providerCommissionPlanDto.getInstitutionProviderContractId());
            commissionPlan.setGmtModified(new Date());
            commissionPlan.setGmtModifiedUser(user.getLoginId());

            boolean change = EntityCompareUtil.compareFields(original, commissionPlan);
            if (change && saveLog) {
                logs.add(GeneratorLogsUtil.generatorLog(LogTypeEnum.UPDATE_PLAN, LogTableEnum.PROVIDER_PLAN,
                        providerCommissionPlanDto.getId(), LogEventEnum.UPDATE_PLAN, user.getLoginId(),
                        commissionPlan.getName(), commissionPlan.getFkInstitutionProviderContractId()));
            }

            //如果方案处于审核中,自动上锁
            if (commissionPlan.getApprovalStatus().equals(ApprovalStatusEnum.PENDING_APPROVAL.getCode())) {
                commissionPlan.setIsLocked(1);
            }
            institutionProviderCommissionPlanMapper.updateById(commissionPlan);
            pmpMediaAndAttachedService.saveMedia(MediaTableEnum.PROVIDER_COMMISSION_PLAN.getCode(),
                    providerCommissionPlanDto.getId(), providerCommissionPlanDto.getMediaList());
            List<LogDto> territoryLogs = commissionPlanTerritoryService.saveProviderCommissionPlanTerritory(commissionPlan.getFkInstitutionProviderContractId(), providerCommissionPlanDto.getId(),
                    providerCommissionPlanDto.getTerritoryList());

            //修改代理方案名称
            if (!original.getName().equals(commissionPlan.getName())) {
                agentCommissionPlanService.updateAgentCommissionPlanName(commissionPlan.getId(), commissionPlan.getName());
            }

            if (saveLog) {
                logs.addAll(territoryLogs);
                commissionChangedEventPublisher.publishCommissionChangedEvent(logs);
            }
            return providerCommissionPlanDto.getId();
        }
        InstitutionProviderCommissionPlan plan = new InstitutionProviderCommissionPlan();
        BeanCopyUtils.copyProperties(providerCommissionPlanDto, plan);
        plan.setFkInstitutionProviderId(providerCommissionPlanDto.getInstitutionProviderId());
        plan.setFkInstitutionProviderContractId(providerCommissionPlanDto.getInstitutionProviderContractId());
        plan.setGmtCreate(new Date());
        plan.setGmtCreateUser(user.getLoginId());
        plan.setGmtModified(new Date());
        plan.setGmtModifiedUser(user.getLoginId());
        plan.setIsLocked(0);
        plan.setApprovalStatus(ApprovalStatusEnum.UN_COMMITTED.getCode());
        plan.setIsRenewal(0);
        institutionProviderCommissionPlanMapper.insert(plan);
        pmpMediaAndAttachedService.saveMedia(MediaTableEnum.PROVIDER_COMMISSION_PLAN.getCode(),
                plan.getId(), providerCommissionPlanDto.getMediaList());
        commissionPlanTerritoryService.saveProviderCommissionPlanTerritory(plan.getFkInstitutionProviderContractId(), plan.getId(),
                providerCommissionPlanDto.getTerritoryList());
        if (saveLog) {
            logs.add(GeneratorLogsUtil.generatorLog(LogTypeEnum.INSERT_PLAN, LogTableEnum.PROVIDER_PLAN,
                    plan.getId(), LogEventEnum.ADD_PLAN, user.getLoginId(), "", plan.getFkInstitutionProviderContractId()));
            commissionChangedEventPublisher.publishCommissionChangedEvent(logs);
        }
        return plan.getId();
    }

    @Override
    public ProviderCommissionPlanDetailVo getProviderCommissionPlanDetail(Long id) {
        ProviderCommissionPlanDetailVo result = new ProviderCommissionPlanDetailVo();
        InstitutionProviderCommissionPlan commissionPlan = institutionProviderCommissionPlanMapper.selectById(id);
        if (Objects.isNull(commissionPlan)) {
            return null;
        }
        BeanCopyUtils.copyProperties(commissionPlan, result);
        //设置提供商名称
        institutionCenterMapper.selectProviderByIds(Arrays.asList(commissionPlan.getFkInstitutionProviderId()))
                .stream().findFirst().ifPresent(provider -> result.setInstitutionProviderName(provider.getName()));
        //附件列表
        List<MediaVo> mediaList = pmpMediaAndAttachedService.getMediaList(MediaTableEnum.PROVIDER_COMMISSION_PLAN.getCode(), Arrays.asList(id));
        result.setCommissionPlanMediaList(mediaList);
        result.setTerritoryList(new ArrayList<>());
        //适用国家列表
        List<InstitutionProviderCommissionPlanTerritory> commissionPlanTerritories = commissionPlanTerritoryMapper.selectList(new LambdaQueryWrapper<InstitutionProviderCommissionPlanTerritory>()
                .eq(InstitutionProviderCommissionPlanTerritory::getFkInstitutionProviderCommissionPlanId, id));
        if (CollectionUtils.isNotEmpty(commissionPlanTerritories)) {
//            List<PlanTerritoryDto> territoryList = commissionPlanTerritories.stream()
//                    // 按 isInclude 值分组，键为 isInclude，值为对应国家ID列表
//                    .collect(Collectors.groupingBy(
//                            InstitutionProviderCommissionPlanTerritory::getIsInclude,
//                            Collectors.mapping(
//                                    e -> e.getFkAreaCountryId(),
//                                    Collectors.toList())))
//                    // 遍历分组结果，构建 DTO 列表
//                    .entrySet().stream().map(entry -> {
//                        PlanTerritoryDto dto = new PlanTerritoryDto();
//                        dto.setIsInclude(entry.getKey());
//                        dto.setCountryIds(entry.getValue());
//                        return dto;
//                    }).collect(Collectors.toList());
            List<PlanTerritoryDto> territoryList = commissionPlanTerritories.stream()
                    // 按 isInclude 分组
                    .collect(Collectors.groupingBy(InstitutionProviderCommissionPlanTerritory::getIsInclude))
                    .entrySet()
                    .stream()
                    .map(entry -> {
                        Integer isInclude = entry.getKey();
                        List<InstitutionProviderCommissionPlanTerritory> items = entry.getValue();

                        // 筛选出国家ID（非空）
                        List<Long> countryIds = items.stream()
                                .map(InstitutionProviderCommissionPlanTerritory::getFkAreaCountryId)
                                .filter(Objects::nonNull)
                                .distinct()
                                .collect(Collectors.toList());

                        // 筛选出大区ID（非空）
                        List<Long> regionIds = items.stream()
                                .map(InstitutionProviderCommissionPlanTerritory::getFkAreaRegionId)
                                .filter(Objects::nonNull)
                                .distinct()
                                .collect(Collectors.toList());
                        // 构造 DTO
                        PlanTerritoryDto dto = new PlanTerritoryDto();
                        dto.setIsInclude(isInclude);
                        dto.setCountryIds(countryIds);
                        dto.setRegionIds(regionIds);
                        return dto;
                    })
                    .collect(Collectors.toList());
            result.setTerritoryList(territoryList);
        }
        //适用国家列表-展示
        Map<Long, List<PlanTerritoryVo>> planTerritories = getPlanTerritories(Arrays.asList(id));
        if (Objects.nonNull(planTerritories.get(id))) {
            result.setTerritoryShowList(planTerritories.get(id));
        } else {
            result.setTerritoryShowList(Arrays.asList(PlanTerritoryVo.createGlobalTerritory()));
        }
        ProviderCommissionPlanVo planPermission = providerContractService.checkPlanPermission(result);
        result.setPendingApprovalType(planPermission.getPendingApprovalType());
        return result;
    }

    @Override
    public List<InstitutionVo> getInstitutionList(Long institutionProviderId) {
//        List<Long> companyIds = SecureUtil.getCompanyIds();
//        if (CollectionUtils.isEmpty(companyIds)) {
//            log.error("登录用户无companyIds:{}", JSONObject.toJSONString(SecureUtil.getUser()));
//            return Collections.EMPTY_LIST;
//        }
        List<InstitutionVo> institutionList = institutionCenterMapper.getInstitutionListBycompanyIdsAndCountryIds(institutionProviderId, null, SecureUtil.getCountryIds());
        //查询方案数量
        List<Long> institutionIds = institutionList.stream().map(InstitutionVo::getInstitutionId).distinct().collect(Collectors.toList());
        Map<Long, Integer> planCountMap = institutionPlanCount(institutionIds);
        //填充学校标签数据
        institutionList.stream().forEach(item -> {
            item.setInstitutionLabel(providerCommissionPlanInstitutionService.getInstitutionLabel(item.getInstitutionId()));
            item.setPlanCount(planCountMap.getOrDefault(item.getInstitutionId(), 0));
        });
        return institutionList;
    }

    @Override
    public List<CountryVo> getCountryList() {
        return institutionCenterMapper.countryList();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteProviderCommissionPlan(Long id, Boolean saveLog) {
        InstitutionProviderCommissionPlan commissionPlan = institutionProviderCommissionPlanMapper.selectById(id);
        if (Objects.isNull(commissionPlan)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_PLAN_NOT_FOUND", "方案不存在"));
        }
        if (commissionPlan.getIsLocked().equals(1) && !SecureUtil.getLoginId().equals(commissionPlan.getGmtCreateUser())) {
            log.error("删除方案失败,没有权限,当前操作人:{},合同创建人:{},id:{}", SecureUtil.getLoginId(), commissionPlan.getGmtCreateUser(), id);
            throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_PLAN_LOCKED", "方案已锁定,没有权限操作"));
        }
        checkDelPlan(id);
        UserInfo user = SecureUtil.getUser();
        List<LogDto> logs = new ArrayList<>();
        //1:佣金计划与学校的关联
        commissionPlanInstitutionMapper.delete(new LambdaQueryWrapper<InstitutionProviderCommissionPlanInstitution>()
                .eq(InstitutionProviderCommissionPlanInstitution::getFkInstitutionProviderCommissionPlanId, id));
        //2:佣金计划与国家,适用地区的关联
        commissionPlanTerritoryMapper.delete(new LambdaQueryWrapper<InstitutionProviderCommissionPlanTerritory>()
                .eq(InstitutionProviderCommissionPlanTerritory::getFkInstitutionProviderCommissionPlanId, id));
        //3:附件
        pmpMediaAndAttachedMapper.delete(new LambdaQueryWrapper<PmpMediaAndAttached>()
                .eq(PmpMediaAndAttached::getFkTableName, MediaTableEnum.PROVIDER_COMMISSION_PLAN.getCode())
                .eq(PmpMediaAndAttached::getFkTableId, id));
        //4:佣金计划明细表
        providerCommissionService.delCommissionByPlanId(id);
        //6:删除日志
        logOperationMapper.delete(new LambdaQueryWrapper<LogOperation>()
                .eq(LogOperation::getFkTableName, LogTableEnum.PROVIDER_PLAN.getCode())
                .eq(LogOperation::getFkTableId, id));
        //5:佣金计划
        institutionProviderCommissionPlanMapper.deleteById(id);
        //6:删除代理方案
        agentCommissionPlanService.delAgentCommissionPlanByProviderPlanId(id);
        //7:删除审核记录-单个审批的
        commissionPlanApprovalMapper.delete(new LambdaQueryWrapper<InstitutionProviderCommissionPlanApproval>()
                .eq(InstitutionProviderCommissionPlanApproval::getTypeKey, ProviderCommissionPlanApprovalTypeEnum.SINGLE.getCode())
                .eq(InstitutionProviderCommissionPlanApproval::getFkInstitutionProviderCommissionPlanIds, id));
        if (saveLog) {
            logs.add(GeneratorLogsUtil.generatorLog(LogTypeEnum.DELETE_PLAN, LogTableEnum.PROVIDER_PLAN,
                    id, LogEventEnum.DEL_PLAN, user.getLoginId(), commissionPlan.getName(), commissionPlan.getFkInstitutionProviderContractId()));
            commissionChangedEventPublisher.publishCommissionChangedEvent(logs);
        }

    }

    @Override
    public List<PlanTerritoryDto> getLastPlanTerritory(Long contractId) {
        List<InstitutionProviderCommissionPlan> planList = institutionProviderCommissionPlanMapper.selectList(new LambdaQueryWrapper<InstitutionProviderCommissionPlan>()
                .eq(InstitutionProviderCommissionPlan::getFkInstitutionProviderContractId, contractId)
                .orderByDesc(InstitutionProviderCommissionPlan::getId));
        if (CollectionUtils.isEmpty(planList)) {
            return Collections.emptyList();
        }
        InstitutionProviderCommissionPlan plan = planList.get(0);
        // 1. 查询指定方案下的所有国家/大区记录
        List<InstitutionProviderCommissionPlanTerritory> territoryList = commissionPlanTerritoryMapper.selectList(
                new LambdaQueryWrapper<InstitutionProviderCommissionPlanTerritory>()
                        .eq(InstitutionProviderCommissionPlanTerritory::getFkInstitutionProviderCommissionPlanId, plan.getId()));

        // 2. 按 isInclude 分组（去除 null 值）
        Map<Integer, List<InstitutionProviderCommissionPlanTerritory>> groupedByIsInclude = territoryList.stream()
                .filter(t -> Objects.nonNull(t.getIsInclude()))
                .collect(Collectors.groupingBy(InstitutionProviderCommissionPlanTerritory::getIsInclude));

        // 3. 遍历每组，封装成 PlanTerritoryDto 列表
        return groupedByIsInclude.entrySet().stream()
                .map(entry -> {
                    Integer isInclude = entry.getKey();
                    List<InstitutionProviderCommissionPlanTerritory> territories = entry.getValue();

                    PlanTerritoryDto dto = new PlanTerritoryDto();
                    dto.setIsInclude(isInclude);

                    // 特殊规则：isInclude == 4（UK_SHORE）时，国家和大区都设置为 0
                    if (isInclude.equals(TerritoryRuleEnum.ON_SHORE.getCode()) || isInclude.equals(TerritoryRuleEnum.UK_SHORE.getCode())) {
                        dto.setCountryIds(Collections.singletonList(0L));
                        dto.setRegionIds(Collections.singletonList(0L));
                    } else {
                        // 提取非空国家ID
                        List<Long> countryIds = territories.stream()
                                .map(InstitutionProviderCommissionPlanTerritory::getFkAreaCountryId)
                                .filter(Objects::nonNull)
                                .distinct()
                                .collect(Collectors.toList());
                        dto.setCountryIds(countryIds);

                        // 提取非空大区ID
                        List<Long> regionIds = territories.stream()
                                .map(InstitutionProviderCommissionPlanTerritory::getFkAreaRegionId)
                                .filter(Objects::nonNull)
                                .distinct()
                                .collect(Collectors.toList());
                        dto.setRegionIds(regionIds);
                    }
                    return dto;
                }).collect(Collectors.toList());
    }
//        List<InstitutionProviderCommissionPlanTerritory> territoryList = commissionPlanTerritoryMapper.selectList(new LambdaQueryWrapper<InstitutionProviderCommissionPlanTerritory>()
//                .eq(InstitutionProviderCommissionPlanTerritory::getFkInstitutionProviderCommissionPlanId, plan.getId()));
//        Map<Integer, List<InstitutionProviderCommissionPlanTerritory>> groupedByIsInclude = territoryList.stream()
//                .filter(t -> Objects.nonNull(t.getIsInclude()))
//                .collect(Collectors.groupingBy(InstitutionProviderCommissionPlanTerritory::getIsInclude));
//
//        return groupedByIsInclude.entrySet().stream()
//                .map(entry -> {
//                    Integer isInclude = entry.getKey();
//                    List<InstitutionProviderCommissionPlanTerritory> territories = entry.getValue();
//
//                    PlanTerritoryDto dto = new PlanTerritoryDto();
//                    dto.setIsInclude(isInclude);
//
//                    // 特殊规则：isInclude=4 时，countryIds 强制为 [0]
//                    if (isInclude.equals(4)) {
//                        dto.setCountryIds(Collections.singletonList(0L));
//                    } else {
//                        // 收集非空的国家ID
//                        List<Long> countryIds = territories.stream()
//                                .map(InstitutionProviderCommissionPlanTerritory::getFkAreaCountryId)
//                                .filter(Objects::nonNull)
//                                .collect(Collectors.toList());
//                        dto.setCountryIds(countryIds);
//                    }
//                    return dto;
//                })
//                .collect(Collectors.toList());
//    }

    @Override
    public Map<Long, List<PlanTerritoryVo>> getPlanTerritories(List<Long> planIds) {
        // 1. 查询当前这些方案下的所有国家/大区数据
        List<InstitutionProviderCommissionPlanTerritory> territories = commissionPlanTerritoryMapper.selectList(
                new LambdaQueryWrapper<InstitutionProviderCommissionPlanTerritory>()
                        .in(InstitutionProviderCommissionPlanTerritory::getFkInstitutionProviderCommissionPlanId, planIds)
        );

        // 2. 如果没有记录，直接返回空 Map
        if (CollectionUtils.isEmpty(territories)) {
            return new HashMap<>();
        }

        // 3. 提取所有国家ID（去重）
        List<Long> allCountryIds = territories.stream()
                .map(InstitutionProviderCommissionPlanTerritory::getFkAreaCountryId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        // 4. 提取所有大区ID（去重）
        List<Long> allRegionIds = territories.stream()
                .map(InstitutionProviderCommissionPlanTerritory::getFkAreaRegionId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        // 5. 批量查询国家信息（英文、中文名称）
        List<CountryVo> countryVos = institutionCenterMapper.queryCountryListByIds(CollectionUtils.isNotEmpty(allCountryIds) ? allCountryIds : Collections.singletonList(0L));

        // 6. 批量查询大区信息（英文、中文名称）
        List<RegionVo> regionVos = institutionCenterMapper.queryRegionListByIds(CollectionUtils.isNotEmpty(allRegionIds) ? allRegionIds : Collections.singletonList(0L));

        // 7. 构造国家ID → 英文名称映射
        Map<Long, String> countryNameMap = countryVos.stream()
                .collect(Collectors.toMap(
                        CountryVo::getCountryId,
                        CountryVo::getName,
                        (oldValue, newValue) -> newValue
                ));

        // 8. 构造国家ID → 中文名称映射
        Map<Long, String> countryNameChnMap = countryVos.stream()
                .collect(Collectors.toMap(
                        CountryVo::getCountryId,
                        CountryVo::getNameChn,
                        (oldValue, newValue) -> newValue
                ));

        // 9. 构造大区ID → 英文名称映射
        Map<Long, String> regionNameMap = regionVos.stream()
                .collect(Collectors.toMap(
                        RegionVo::getRegionId,
                        RegionVo::getName,
                        (oldValue, newValue) -> newValue
                ));

        // 10. 构造大区ID → 中文名称映射
        Map<Long, String> regionNameChnMap = regionVos.stream()
                .collect(Collectors.toMap(
                        RegionVo::getRegionId,
                        RegionVo::getNameChn,
                        (oldValue, newValue) -> newValue
                ));

        // 11. 分组返回：Map<方案ID, List<PlanTerritoryVo>>
        Map<Long, List<PlanTerritoryVo>> result = territories.stream()
                .collect(Collectors.groupingBy(
                        // 11.1 外层按方案ID分组
                        InstitutionProviderCommissionPlanTerritory::getFkInstitutionProviderCommissionPlanId,
                        Collectors.collectingAndThen(
                                // 11.2 内层按 isInclude 类型进一步分组
                                Collectors.groupingBy(InstitutionProviderCommissionPlanTerritory::getIsInclude),
                                innerMap -> innerMap.entrySet().stream()
                                        .sorted(Map.Entry.comparingByKey()) // 按 isInclude 升序
                                        .map(entry -> {
                                            Integer isInclude = entry.getKey(); // 当前规则类型
                                            List<InstitutionProviderCommissionPlanTerritory> items = entry.getValue();

                                            PlanTerritoryVo vo = new PlanTerritoryVo();
                                            vo.setIsInclude(isInclude);

                                            // 11.3 设置规则描述（中英文）
                                            TerritoryRuleEnum ruleEnum = TerritoryRuleEnum.getEnumByCode(isInclude);
                                            vo.setDescription(ruleEnum != null ? ruleEnum.getMsg() : "");
                                            vo.setDescriptionChn(ruleEnum != null ? ruleEnum.getMsgChn() : "");

                                            // 11.4 提取国家ID列表（非空）
                                            List<Long> countryIds = items.stream()
                                                    .map(InstitutionProviderCommissionPlanTerritory::getFkAreaCountryId)
                                                    .filter(Objects::nonNull)
                                                    .distinct()
                                                    .collect(Collectors.toList());
                                            vo.setTerritoryIds(countryIds);

                                            // 11.5 设置国家名称（中英文，逗号拼接）
                                            if (CollectionUtils.isNotEmpty(countryIds)) {
                                                String countryNames = countryIds.stream()
                                                        .map(countryNameMap::get)
                                                        .filter(Objects::nonNull)
                                                        .collect(Collectors.joining(", "));
                                                vo.setTerritories(countryNames);

                                                String countryNamesChn = countryIds.stream()
                                                        .map(countryNameChnMap::get)
                                                        .filter(Objects::nonNull)
                                                        .collect(Collectors.joining(", "));
                                                vo.setTerritoriesChn(countryNamesChn);
                                            }

                                            // 11.6 提取大区ID列表（非空）
                                            List<Long> regionIds = items.stream()
                                                    .map(InstitutionProviderCommissionPlanTerritory::getFkAreaRegionId)
                                                    .filter(Objects::nonNull)
                                                    .distinct()
                                                    .collect(Collectors.toList());

                                            // 11.7 设置大区名称（中英文，逗号拼接）
                                            if (CollectionUtils.isNotEmpty(regionIds)) {
                                                String regionNames = regionIds.stream()
                                                        .map(regionNameMap::get)
                                                        .filter(Objects::nonNull)
                                                        .collect(Collectors.joining(", "));
                                                vo.setRegions(regionNames);

                                                String regionNamesChn = regionIds.stream()
                                                        .map(regionNameChnMap::get)
                                                        .filter(Objects::nonNull)
                                                        .collect(Collectors.joining(", "));
                                                vo.setRegionsChn(regionNamesChn);
                                            }

                                            return vo;
                                        })
                                        .collect(Collectors.toList())
                        )
                ));

        return result;
    }
//    @Override
//    public Map<Long, List<PlanTerritoryVo>> getPlanTerritories(List<Long> planIds) {
//        List<InstitutionProviderCommissionPlanTerritory> territories = commissionPlanTerritoryMapper.selectList(new LambdaQueryWrapper<InstitutionProviderCommissionPlanTerritory>()
//                .in(InstitutionProviderCommissionPlanTerritory::getFkInstitutionProviderCommissionPlanId, planIds));
//        if (CollectionUtils.isEmpty(territories)) {
//            return new HashMap<>();
//        }
//        List<Long> allCountryIds = territories.stream().map(InstitutionProviderCommissionPlanTerritory::getFkAreaCountryId).distinct().collect(Collectors.toList());
//        List<Long> allRegionIds = territories.stream().map(InstitutionProviderCommissionPlanTerritory::getFkAreaRegionId).distinct().collect(Collectors.toList());
//        List<CountryVo> countryVos = institutionCenterMapper.queryCountryListByIds(allCountryIds);
//        List<RegionVo> regionVos = institutionCenterMapper.queryRegionListByIds(allRegionIds);
//        Map<Long, String> territoryNameMap = countryVos.stream()
//                .collect(Collectors.toMap(
//                        CountryVo::getCountryId,
//                        CountryVo::getName,
//                        (oldValue, newValue) -> newValue));
//        Map<Long, String> territoryNameChnMap = countryVos.stream()
//                .collect(Collectors.toMap(
//                        CountryVo::getCountryId,
//                        CountryVo::getNameChn,
//                        (oldValue, newValue) -> newValue));
//        Map<Long, List<PlanTerritoryVo>> result = territories.stream()
//                .collect(Collectors.groupingBy(
//                        // 外层分组：按方案ID
//                        InstitutionProviderCommissionPlanTerritory::getFkInstitutionProviderCommissionPlanId,
//                        Collectors.collectingAndThen(
//                                // 内层分组：按is_include值分组
//                                Collectors.groupingBy(InstitutionProviderCommissionPlanTerritory::getIsInclude),
//                                innerMap -> innerMap.entrySet().stream()
//                                        .sorted(Map.Entry.comparingByKey()).map(entry -> {
//                                            // 构造PlanTerritoryVo对象
//                                            PlanTerritoryVo vo = new PlanTerritoryVo();
//                                            vo.setIsInclude(entry.getKey());
//                                            vo.setDescription(Objects.nonNull(TerritoryRuleEnum.getEnumByCode(entry.getKey()))
//                                                    ? TerritoryRuleEnum.getEnumByCode(entry.getKey()).getMsg() : "");
//                                            vo.setDescriptionChn(Objects.nonNull(TerritoryRuleEnum.getEnumByCode(entry.getKey()))
//                                                    ? TerritoryRuleEnum.getEnumByCode(entry.getKey()).getMsgChn() : "");
//                                            // 提取国家ID列表（过滤null值）
//                                            List<Long> territoryIds = entry.getValue().stream()
//                                                    .map(InstitutionProviderCommissionPlanTerritory::getFkAreaCountryId)
//                                                    .filter(Objects::nonNull)
//                                                    .collect(Collectors.toList());
//                                            vo.setTerritoryIds(territoryIds);
//                                            if (CollectionUtils.isNotEmpty(territoryIds)) {
//                                                String countryNames = territoryIds.stream()
//                                                        .filter(territoryNameMap::containsKey)
//                                                        .map(territoryNameMap::get)
//                                                        .collect(Collectors.joining(", "));
//                                                vo.setTerritories(countryNames);
//                                                String countryNamesChn = territoryIds.stream()
//                                                        .filter(territoryNameChnMap::containsKey)
//                                                        .map(territoryNameChnMap::get)
//                                                        .collect(Collectors.joining(", "));
//                                                vo.setTerritoriesChn(countryNamesChn);
//                                            }
//                                            return vo;
//                                        }).collect(Collectors.toList())
//                        )
//                ));
//        return result;
//    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long copyProviderCommissionPlan(SaveProviderCommissionPlanDto saveProviderCommissionPlanDto) {
        ProviderCommissionListVo providerCommissionList = commissionService.getProviderCommissionList(saveProviderCommissionPlanDto.getId());
        saveProviderCommissionPlanDto.setId(null);
        saveProviderCommissionPlanDto.setMediaList(saveProviderCommissionPlanDto.getMediaList());
        Long commissionPlanId = saveProviderCommissionPlan(saveProviderCommissionPlanDto, true);
        SaveProviderCommissionDto commissionDto = new SaveProviderCommissionDto();
        BeanCopyUtils.copyProperties(providerCommissionList, commissionDto);
        commissionDto.setCommissionPlanId(commissionPlanId);

        //初始化数据
        providerCommissionList.getCommissionList().stream().forEach(commission -> {
            commission.setId(null);
            commission.setFkInstitutionProviderCommissionPlanId(commissionPlanId);
        });
        commissionDto.getCombinationList().stream().forEach(commission -> {
            commission.setPackageKey(null);
            commission.getCommissionInfoList().stream().forEach(commissionInfo -> {
                commissionInfo.setId(null);
                commissionInfo.setFkInstitutionProviderCommissionPlanId(commissionPlanId);
            });
        });
        commissionDto.getBonusList().stream().forEach(commission -> {
            commission.setId(null);
            commission.setFkInstitutionProviderCommissionPlanId(commissionPlanId);

        });
        commissionService.saveProviderCommission(commissionDto, Boolean.FALSE);
        return commissionPlanId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean unbindProviderInstitution(UnbindProviderInstitutionDto unbindProviderInstitutionDto) {
        log.info("解绑学校提供商关联的学校:{}", JSONObject.toJSONString(unbindProviderInstitutionDto));
        List<InstitutionProviderCommissionPlan> planList = institutionProviderCommissionPlanMapper.selectList(new LambdaQueryWrapper<InstitutionProviderCommissionPlan>()
                .eq(InstitutionProviderCommissionPlan::getFkInstitutionProviderId, unbindProviderInstitutionDto.getProviderId()));
        if (CollectionUtils.isNotEmpty(planList)) {
            List<Long> planIds = planList.stream().map(InstitutionProviderCommissionPlan::getId).distinct().collect(Collectors.toList());
            planInstitutionMapper.delete(new LambdaQueryWrapper<InstitutionProviderCommissionPlanInstitution>()
                    .in(InstitutionProviderCommissionPlanInstitution::getFkInstitutionProviderCommissionPlanId, planIds)
                    .in(InstitutionProviderCommissionPlanInstitution::getFkInstitutionId, unbindProviderInstitutionDto.getInstitutionIds())
            );
        }
        List<AgentCommissionPlan> agentCommissionPlans = agentCommissionPlanMapper.selectList(new LambdaQueryWrapper<AgentCommissionPlan>()
                .eq(AgentCommissionPlan::getFkInstitutionProviderId, unbindProviderInstitutionDto.getProviderId()));
        if (CollectionUtils.isNotEmpty(agentCommissionPlans)) {
            List<Long> agentPlanIds = agentCommissionPlans.stream().map(AgentCommissionPlan::getId).distinct().collect(Collectors.toList());
            agentCommissionPlanInstitutionMapper.delete(new LambdaQueryWrapper<AgentCommissionPlanInstitution>()
                    .in(AgentCommissionPlanInstitution::getFkAgentCommissionPlanId, agentPlanIds)
                    .in(AgentCommissionPlanInstitution::getFkInstitutionId, unbindProviderInstitutionDto.getInstitutionIds())
            );
        }
        return Boolean.TRUE;
    }

    @Override
    public ProviderCommissionPlanInfoVo getProviderCommissionPlanInfo(Long planId) {
        ProviderCommissionPlanInfoVo result = new ProviderCommissionPlanInfoVo();
        ProviderCommissionPlanDetailVo detail = getProviderCommissionPlanDetail(planId);
        if (Objects.isNull(detail)) {
            return result;
        }
        BeanCopyUtils.copyProperties(detail, result);

        //填充适用国家
        if (Objects.nonNull(detail.getId())) {
            Map<Long, List<PlanTerritoryVo>> planTerritories = getPlanTerritories(Arrays.asList(detail.getId()));
            if (Objects.nonNull(planTerritories.get(detail.getId()))) {
                result.setPlanTerritoryList(planTerritories.get(detail.getId()));
            } else {
                result.setPlanTerritoryList(Arrays.asList(PlanTerritoryVo.createGlobalTerritory()));
            }
        }

        ProviderCommissionListVo providerCommissionList = providerCommissionService.getProviderCommissionList(planId);
        result.setCommissionListVo(providerCommissionList);
        return result;
    }

    @Override
    public void lockPlan(LockPlanDto lockPlanDto) {
        InstitutionProviderCommissionPlan plan = institutionProviderCommissionPlanMapper.selectById(lockPlanDto.getPlanId());
        if (Objects.isNull(plan)) {
//            throw new GetServiceException("佣金方案不存在!");
            throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_PLAN_NOT_FOUND", "方案不存在"));
        }
//        if (plan.getApprovalStatus().equals(ApprovalStatusEnum.PENDING_APPROVAL.getCode())) {
//            log.error("操作失败,方案正在审批中,方案id:{}", lockPlanDto.getPlanId());
//            throw new GetServiceException("操作失败,方案正在审批中");
//            throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_PLAN_IN_APPROVAL", "操作失败,方案正在审批中"));
//        }
//        if (!plan.getGmtCreateUser().equals(SecureUtil.getLoginId())) {
//            log.error("锁定/解锁方案失败,没有权限,当前操作人:{},合同id:{},合同创建人:{}", SecureUtil.getLoginId(), SecureUtil.getLoginId(), plan.getGmtCreateUser());
////            throw new GetServiceException("您没有权限操作!");
//            throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_NO_PERMISSION", "您没有权限操作"));
//        }
        plan.setIsLocked(lockPlanDto.getIsLocked());
        plan.setGmtModified(new Date());
        plan.setGmtModifiedUser(SecureUtil.getLoginId());
        institutionProviderCommissionPlanMapper.updateById(plan);
    }

    @Override
    public void submitProviderPlanApproval(SubmitPlanApprovalDto submitPlanApprovalDto) {
        InstitutionProviderCommissionPlan plan = institutionProviderCommissionPlanMapper.selectById(submitPlanApprovalDto.getPlanId());
        if (Objects.isNull(plan)) {
            log.error("提交审核失败,佣金方案不存在，合同id：{}", submitPlanApprovalDto.getPlanId());
//            throw new GetServiceException("佣金方案不存在");
            throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_PLAN_NOT_FOUND", "方案不存在"));
        }
        if (Objects.nonNull(plan.getApprovalStatus()) && plan.getApprovalStatus().equals(ApprovalStatusEnum.PENDING_APPROVAL.getCode())) {
            log.error("提交审核失败,方案已提交审核，合同id：{}", submitPlanApprovalDto.getPlanId());
//            throw new GetServiceException("佣金方案已提交审核");
            throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_PLAN_SUBMITTED", "方案已提交审核"));
        }
        permissionService.verifyCurrentUserCountry(plan.getId(), submitPlanApprovalDto.getStaffId(), 1);
        InstitutionProviderCommissionPlanApproval planApproval = new InstitutionProviderCommissionPlanApproval();
//        planApproval.setFkInstitutionProviderCommissionPlanId(submitPlanApprovalDto.getPlanId());
        planApproval.setFkInstitutionProviderCommissionPlanIds(submitPlanApprovalDto.getPlanId().toString());
        planApproval.setApprovalStatus(ApprovalStatusEnum.PENDING_APPROVAL.getCode());
        planApproval.setFkStaffId(submitPlanApprovalDto.getStaffId());
        planApproval.setGmtCreate(new Date());
        planApproval.setGmtModified(new Date());
        planApproval.setGmtModified(new Date());
        planApproval.setGmtCreateUser(SecureUtil.getLoginId());
        planApproval.setGmtModifiedUser(SecureUtil.getLoginId());
        planApproval.setCommissionPlanName(plan.getName());
        planApproval.setSubmitNote(submitPlanApprovalDto.getSubmitNote());
        planApproval.setTypeKey(ProviderCommissionPlanApprovalTypeEnum.SINGLE.getCode());
        commissionPlanApprovalMapper.insert(planApproval);
        //更新合同状态
        plan.setApprovalStatus(ApprovalStatusEnum.PENDING_APPROVAL.getCode());
        plan.setGmtModified(new Date());
        plan.setGmtModifiedUser(SecureUtil.getLoginId());
        //有权限提交审核的人，点击提交审核后，同时会将此合同更改为锁定状态，不管审批是否通过，都不会自动解锁，需要合同的创建人手动解锁。
        plan.setIsLocked(1);
        institutionProviderCommissionPlanMapper.updateById(plan);
        //保存审核附件
        if (CollectionUtils.isNotEmpty(submitPlanApprovalDto.getMediaList())) {
            pmpMediaAndAttachedService.saveMedia(MediaTableEnum.PROVIDER_COMMISSION_PLAN_APPROVAL.getCode(),
                    planApproval.getId(), submitPlanApprovalDto.getMediaList());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void approvalProviderPlan(ApprovalPlanDto approvalPlanDto) {
        InstitutionProviderCommissionPlan plan = institutionProviderCommissionPlanMapper.selectById(approvalPlanDto.getPlanId());
        if (Objects.isNull(plan)) {
            log.error("审核失败,佣金方案不存在，方案id：{}", approvalPlanDto.getPlanId());
            throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_PLAN_NOT_FOUND", "方案不存在"));
        }
        List<InstitutionProviderCommissionPlanApproval> planApprovals = commissionPlanApprovalMapper.selectList(new LambdaQueryWrapper<InstitutionProviderCommissionPlanApproval>()
                .eq(InstitutionProviderCommissionPlanApproval::getFkInstitutionProviderCommissionPlanIds, approvalPlanDto.getPlanId())
                .eq(InstitutionProviderCommissionPlanApproval::getApprovalStatus, ApprovalStatusEnum.PENDING_APPROVAL.getCode())
                .eq(InstitutionProviderCommissionPlanApproval::getTypeKey, ProviderCommissionPlanApprovalTypeEnum.SINGLE.getCode())
                .orderByDesc(InstitutionProviderCommissionPlanApproval::getGmtCreate));
        if (Objects.isNull(planApprovals) || planApprovals.isEmpty()) {
            log.error("审核失败,佣金方案审批记录不存在，方案id：{}", approvalPlanDto.getPlanId());
            throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_PLAN_RECORD_NOT_FOUND", "方案审批记录不存在"));
        }
        if (!planApprovals.get(0).getFkStaffId().equals(SecureUtil.getStaffId())) {
            log.error("审批失败,非方案指定审批人，合同id：{},审批人id：{},登录人id：{}", approvalPlanDto.getPlanId(),
                    planApprovals.get(0).getFkStaffId(), SecureUtil.getStaffId());
            throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_NOT_APPROVER", "审核失败,当前用户不是方案指定审批人"));
        }
        InstitutionProviderCommissionPlanApproval planApproval = new InstitutionProviderCommissionPlanApproval();
        planApproval.setFkInstitutionProviderCommissionPlanIds(approvalPlanDto.getPlanId().toString());
        planApproval.setApprovalStatus(approvalPlanDto.getApprovalStatus());
        planApproval.setApprovalComment(approvalPlanDto.getApprovalComment());
        planApproval.setApprovalTime(new Date());
        planApproval.setFkStaffId(planApprovals.get(0).getFkStaffId());
        planApproval.setGmtCreate(new Date());
        planApproval.setGmtModified(new Date());
        planApproval.setGmtModified(new Date());
        planApproval.setGmtCreateUser(SecureUtil.getLoginId());
        planApproval.setGmtModifiedUser(SecureUtil.getLoginId());
        planApproval.setCommissionPlanName(plan.getName());
        planApproval.setTypeKey(ProviderCommissionPlanApprovalTypeEnum.SINGLE.getCode());
        commissionPlanApprovalMapper.insert(planApproval);
        //更新合同状态
        plan.setApprovalStatus(approvalPlanDto.getApprovalStatus());
        plan.setGmtModified(new Date());
        plan.setGmtModifiedUser(SecureUtil.getLoginId());
        institutionProviderCommissionPlanMapper.updateById(plan);
        //保存审核附件
        if (CollectionUtils.isNotEmpty(approvalPlanDto.getMediaList())) {
            pmpMediaAndAttachedService.saveMedia(MediaTableEnum.PROVIDER_COMMISSION_PLAN_APPROVAL.getCode(),
                    planApproval.getId(), approvalPlanDto.getMediaList());
        }

        //修改代理佣金方案修改标记-0623调整为审核通过之后再修改
        if (approvalPlanDto.getApprovalStatus().equals(ApprovalStatusEnum.PASS.getCode())) {
            agentCommissionPlanService.update(new LambdaUpdateWrapper<AgentCommissionPlan>()
                    .set(AgentCommissionPlan::getIsInstitutionProviderCommissionModify, 1)
                    .eq(AgentCommissionPlan::getFkInstitutionProviderCommissionPlanId, approvalPlanDto.getPlanId()));
            //同步学校
            List<Long> institutionIds = providerCommissionPlanInstitutionService.list(new LambdaQueryWrapper<InstitutionProviderCommissionPlanInstitution>()
                            .eq(InstitutionProviderCommissionPlanInstitution::getFkInstitutionProviderCommissionPlanId, approvalPlanDto.getPlanId()))
                    .stream()
                    .map(InstitutionProviderCommissionPlanInstitution::getFkInstitutionId).collect(Collectors.toList());
            agentCommissionPlanInstitutionService.syncAgentCommissionPlanInstitution(institutionIds, plan.getId());
        }

        if (approvalPlanDto.getApprovalStatus().equals(ApprovalStatusEnum.PASS.getCode())) {
            try {
                Integer i = institutionCenterClient.updateContractStatus(plan.getFkInstitutionProviderId(), 1);
                log.info("更新供应商合同状态,供应商id:{},状态:{}", plan.getFkInstitutionProviderId(), i);
            } catch (Exception e) {
                log.error("更新供应商合同状态失败:{}", e.getMessage());
            }
        }
    }

    @Override
    public List<InstitutionProviderCommissionPlanApproval> getApprovalList(Long contractId, Long planId, String planName) {
        List<Long> planIds = institutionProviderCommissionPlanMapper.selectList(new LambdaQueryWrapper<InstitutionProviderCommissionPlan>()
                        .eq(InstitutionProviderCommissionPlan::getFkInstitutionProviderContractId, contractId)
                        .eq(Objects.nonNull(planId) && planId > 0, InstitutionProviderCommissionPlan::getId, planId)
                        .like(StringUtils.isNotBlank(planName), InstitutionProviderCommissionPlan::getName, "%" + planName + "%"))
                .stream().map(InstitutionProviderCommissionPlan::getId).collect(Collectors.toList());
        List<Long> batchPlanIds = institutionProviderCommissionPlanMapper.selectList(new LambdaQueryWrapper<InstitutionProviderCommissionPlan>()
                        .eq(InstitutionProviderCommissionPlan::getFkInstitutionProviderContractId, contractId)
                        .eq(Objects.nonNull(planId) && planId > 0, InstitutionProviderCommissionPlan::getId, planId)
                        .exists("select 1 from m_institution_provider_commission_plan_approval where type_key = 'mass' and " +
                                "find_in_set(m_institution_provider_commission_plan.id, fk_institution_provider_commission_plan_ids)" +
                                " and commission_plan_name like '%" + planName + "%'"))
                .stream().map(InstitutionProviderCommissionPlan::getId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(batchPlanIds)) {
            planIds.addAll(batchPlanIds);
        }
        if (CollectionUtils.isEmpty(planIds)) {
            return Collections.emptyList();
        }
        List<InstitutionProviderCommissionPlanApproval> approvalList = commissionPlanApprovalMapper.getApprovalList(planIds);
        approvalList.stream().forEach(approval -> {
            List<MediaVo> mediaList = pmpMediaAndAttachedService.getMediaList(MediaTableEnum.PROVIDER_COMMISSION_PLAN_APPROVAL.getCode(), Arrays.asList(approval.getId()));
            approval.setMediaList(mediaList);
        });
        return approvalList;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void changeContractProvider(Long contractId, Long newProviderId) {
        List<InstitutionProviderCommissionPlan> providerCommissionPlans = institutionProviderCommissionPlanMapper.selectList(new LambdaQueryWrapper<InstitutionProviderCommissionPlan>()
                .eq(InstitutionProviderCommissionPlan::getFkInstitutionProviderContractId, contractId));
        if (CollectionUtils.isNotEmpty(providerCommissionPlans)) {

            //判断佣金方案审核状态,如果包含已提交审核,审核通过,审核驳回的状态,不能修改提供商
            //只有包含了【通过】状态的方案合同，才不能修改提供商，其他状态都可以-0808
            providerCommissionPlans.stream().filter(p ->
                            p.getApprovalStatus().equals(ApprovalStatusEnum.PASS.getCode()))
                    .findFirst().ifPresent(p -> {
//                        throw new GetServiceException("该合同下的佣金方案审核状态存在待审核,审核通过,审核驳回等状态,不能修改提供商");
                        throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_PLAN_STATE_CONFLICT", "该合同下的佣金方案审核状态存在已通过状态,不能修改提供商"));
                    });
            //1-合同端佣金方案修改提供商
            this.update(new LambdaUpdateWrapper<InstitutionProviderCommissionPlan>()
                    .set(InstitutionProviderCommissionPlan::getFkInstitutionProviderId, newProviderId)
                    .set(InstitutionProviderCommissionPlan::getGmtModified, new Date())
                    .set(InstitutionProviderCommissionPlan::getApprovalStatus, ApprovalStatusEnum.UN_COMMITTED.getCode())
                    .set(InstitutionProviderCommissionPlan::getGmtModifiedUser, SecureUtil.getLoginId())
                    .eq(InstitutionProviderCommissionPlan::getFkInstitutionProviderContractId, contractId));
            List<Long> providerCommissionPlanIds = providerCommissionPlans.stream().map(InstitutionProviderCommissionPlan::getId).collect(Collectors.toList());
            //2-合同端佣金明细修改提供商
            providerCommissionService.update(new LambdaUpdateWrapper<InstitutionProviderCommission>()
                    .set(InstitutionProviderCommission::getFkInstitutionProviderId, newProviderId)
                    .set(InstitutionProviderCommission::getGmtModified, new Date())
                    .set(InstitutionProviderCommission::getGmtModifiedUser, SecureUtil.getLoginId())
                    .in(InstitutionProviderCommission::getFkInstitutionProviderCommissionPlanId, providerCommissionPlanIds));

            //3-合同端佣金方案解除学校绑定
            providerCommissionPlanInstitutionService.remove(new LambdaQueryWrapper<InstitutionProviderCommissionPlanInstitution>()
                    .in(InstitutionProviderCommissionPlanInstitution::getFkInstitutionProviderCommissionPlanId, providerCommissionPlanIds));

            List<AgentCommissionPlan> agentCommissionPlans = agentCommissionPlanService.list(new LambdaQueryWrapper<AgentCommissionPlan>()
                    .in(AgentCommissionPlan::getFkInstitutionProviderCommissionPlanId, providerCommissionPlanIds));
            if (CollectionUtils.isNotEmpty(agentCommissionPlans)) {
                List<Long> agentCommissionPlanIds = agentCommissionPlans.stream().map(AgentCommissionPlan::getId).collect(Collectors.toList());
                //4-代理端端佣金方案修改提供商
                agentCommissionPlanService.update(new LambdaUpdateWrapper<AgentCommissionPlan>()
                        .set(AgentCommissionPlan::getFkInstitutionProviderId, newProviderId)
                        .set(AgentCommissionPlan::getGmtModified, new Date())
                        .set(AgentCommissionPlan::getApprovalStatus, ApprovalStatusEnum.UN_COMMITTED.getCode())
                        .set(AgentCommissionPlan::getGmtModifiedUser, SecureUtil.getLoginId())
                        .in(AgentCommissionPlan::getId, agentCommissionPlanIds));
                //5-代理端端佣金明细修改提供商
                agentCommissionService.update(new LambdaUpdateWrapper<AgentCommission>()
                        .set(AgentCommission::getFkInstitutionProviderId, newProviderId)
                        .set(AgentCommission::getGmtModified, new Date())
                        .set(AgentCommission::getGmtModifiedUser, SecureUtil.getLoginId())
                        .in(AgentCommission::getFkAgentCommissionPlanId, agentCommissionPlanIds));
                //6-代理端佣金方案解除学校绑定
                agentCommissionPlanInstitutionService.remove(new LambdaQueryWrapper<AgentCommissionPlanInstitution>()
                        .in(AgentCommissionPlanInstitution::getFkAgentCommissionPlanId, agentCommissionPlanIds));
            }
        }
    }


    @Override
    public List<Long> getUserPermissionPlanIds(Long contractId) {
        //1.自己可看见自己的合同及方案（学校不涉及国别过滤）
        //当前用户可以看到的学校
        List<Long> userInstitutionIds = institutionCenterMapper.getInstitutionIdsCountryIds(SecureUtil.getCountryIds())
                .stream().map(InstitutionVo::getInstitutionId).distinct().collect(Collectors.toList());
        List<Long> userPlanIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(userInstitutionIds)) {
            //有学校的方案
            //2.方案下的学校会根据用户业务国别做数据过滤。
            List<Long> userInstitutionOPlanIds = providerCommissionPlanInstitutionService.list(new LambdaQueryWrapper<InstitutionProviderCommissionPlanInstitution>()
                            .in(InstitutionProviderCommissionPlanInstitution::getFkInstitutionId, userInstitutionIds)
                            .exists(Objects.nonNull(contractId) && contractId > 0,
                                    "select 1 from m_institution_provider_commission_plan where" +
                                            " r_institution_provider_commission_plan_institution.fk_institution_provider_commission_plan_id = id"))
                    .stream().map(InstitutionProviderCommissionPlanInstitution::getFkInstitutionProviderCommissionPlanId)
                    .distinct().collect(Collectors.toList());
            userPlanIds.addAll(userInstitutionOPlanIds);
        }
        //没有学校的方案:仅查询自己创建的
        List<Long> userNoInstitutionPlanIds = institutionProviderCommissionPlanMapper.selectList(new LambdaQueryWrapper<InstitutionProviderCommissionPlan>()
                        .eq(InstitutionProviderCommissionPlan::getGmtCreateUser, SecureUtil.getLoginId())
                        .notExists("select 1 from r_institution_provider_commission_plan_institution where" +
                                " fk_institution_provider_commission_plan_id = m_institution_provider_commission_plan.id")
                        .eq(Objects.nonNull(contractId), InstitutionProviderCommissionPlan::getFkInstitutionProviderContractId, contractId))
                .stream().map(InstitutionProviderCommissionPlan::getId)
                .distinct().collect(Collectors.toList());
        userPlanIds.addAll(userNoInstitutionPlanIds);
        if (CollectionUtils.isEmpty(userPlanIds)) {
            log.error("登录用户无佣金方案,登录人:{},分公司:{},国家:{}", SecureUtil.getLoginId(), SecureUtil.getCompanyIds(), SecureUtil.getCountryIds());
            return Collections.EMPTY_LIST;
        }
        return userPlanIds.stream().distinct().collect(Collectors.toList());
    }

    @Override
    public List<ProviderCommissionPlanVo> getProviderCommissionPlanAndTerritoryList(Long providerContractId) {
        List<Long> userPlanIds = getUserPermissionPlanIds(providerContractId);
        if (CollectionUtils.isEmpty(userPlanIds)) {
            log.error("登录用户无佣金方案,当前用户:{},国家:{},分公司:{}", SecureUtil.getLoginId(), SecureUtil.getCountryIds(), SecureUtil.getCompanyIds());
            return Collections.emptyList();
        }
        List<ProviderCommissionPlanVo> planList = institutionProviderCommissionPlanMapper.selectContractCommissionPlanList(providerContractId, userPlanIds);
        if (CollectionUtils.isNotEmpty(planList)) {
            List<Long> planIds = planList.stream().map(ProviderCommissionPlanVo::getId).collect(Collectors.toList());
            Map<Long, List<PlanTerritoryVo>> planTerritories = getPlanTerritories(planIds);
            planList.stream().forEach(plan -> {
                plan.setCurrentLoginId(SecureUtil.getLoginId());
                if (Objects.nonNull(planTerritories.get(plan.getId()))) {
                    plan.setPlanTerritoryList(planTerritories.get(plan.getId()));
                } else {
                    plan.setPlanTerritoryList(Arrays.asList(PlanTerritoryVo.createGlobalTerritory()));
                }
            });
        }
        return planList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void submitBatchApproval(BatchApprovalDto batchApprovalDto) {
        if (CollectionUtils.isEmpty(batchApprovalDto.getPlanIds())) {
            log.error("批量提交审批,方案为空:{}", JSONObject.toJSONString(batchApprovalDto));
            throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_SUBMIT_PLAN_EMPTY", "提审方案不能为空"));
        }
        //检验方案状态
        if (batchApprovalDto.getSubmitType().equals(2)) {
            Integer pendingApprovalCount = institutionProviderCommissionPlanMapper.selectCount(new LambdaQueryWrapper<InstitutionProviderCommissionPlan>()
                    .in(InstitutionProviderCommissionPlan::getId, batchApprovalDto.getPlanIds())
                    .eq(InstitutionProviderCommissionPlan::getApprovalStatus, ApprovalStatusEnum.PENDING_APPROVAL.getCode()));
            if (pendingApprovalCount > 0) {
                throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_PENDING_PLAN_EXISTS", "存在待审批方案,请勿提交待审批的方案"));
            }
        }
        if (batchApprovalDto.getSubmitType().equals(2) && Objects.isNull(batchApprovalDto.getStaffId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_PARAM_REQUIRED", "参数不能为空"));
        }
        //仅保存
        List<LogDto> logs = new ArrayList<>();
        batchApprovalDto.getPlanIds().stream().forEach(planId -> {
            List<LogDto> territoryLogs = commissionPlanTerritoryService.saveProviderCommissionPlanTerritory(batchApprovalDto.getContractId(),
                    planId, batchApprovalDto.getPlanTerritoryList());
            logs.addAll(territoryLogs);
        });
        //修改方案时间
        if (Objects.nonNull(batchApprovalDto.getApprovalOtherInfo())) {
            BatchApprovalOtherInfoVo otherInfo = batchApprovalDto.getApprovalOtherInfo();
            if (Objects.nonNull(batchApprovalDto.getApprovalOtherInfo().getStartTime())) {
                institutionProviderCommissionPlanMapper.update(new InstitutionProviderCommissionPlan(),
                        new LambdaUpdateWrapper<InstitutionProviderCommissionPlan>()
                                .set(InstitutionProviderCommissionPlan::getStartTime, otherInfo.getStartTime())
                                .set(InstitutionProviderCommissionPlan::getEndTime, otherInfo.getEndTime())
                                .set(InstitutionProviderCommissionPlan::getIsTimeless, otherInfo.getIsTimeless())
                                .set(StringUtils.isNotBlank(otherInfo.getRemark()), InstitutionProviderCommissionPlan::getRemark, otherInfo.getRemark())
                                .set(StringUtils.isNotBlank(otherInfo.getSummary()), InstitutionProviderCommissionPlan::getSummary, otherInfo.getSummary())
                                .set(StringUtils.isNotBlank(otherInfo.getTerritory()), InstitutionProviderCommissionPlan::getTerritory, otherInfo.getTerritory())
                                .in(InstitutionProviderCommissionPlan::getId, batchApprovalDto.getPlanIds()));
            }
        }
        if (CollectionUtils.isNotEmpty(logs)) {
            commissionChangedEventPublisher.publishCommissionChangedEvent(logs);
        }
        //如果方案处于审批中,自动上锁-对于仅保存
        if (batchApprovalDto.getSubmitType().equals(1)) {
            List<Long> pendingApprovalIds = institutionProviderCommissionPlanMapper.selectList(new LambdaQueryWrapper<InstitutionProviderCommissionPlan>()
                            .in(InstitutionProviderCommissionPlan::getId, batchApprovalDto.getPlanIds()))
                    .stream()
                    .filter(plan -> plan.getApprovalStatus().equals(ApprovalStatusEnum.PENDING_APPROVAL.getCode()))
                    .map(plan -> plan.getId())
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(pendingApprovalIds)) {
                institutionProviderCommissionPlanMapper.update(new InstitutionProviderCommissionPlan(),
                        new LambdaUpdateWrapper<InstitutionProviderCommissionPlan>()
                                .set(InstitutionProviderCommissionPlan::getIsLocked, 1)
                                .set(InstitutionProviderCommissionPlan::getGmtModified, new Date())
                                .set(InstitutionProviderCommissionPlan::getGmtCreateUser, SecureUtil.getLoginId())
                                .in(InstitutionProviderCommissionPlan::getId, batchApprovalDto.getPlanIds()));
            }
        }
        if (batchApprovalDto.getSubmitType().equals(2)) {
            //提交审批
            InstitutionProviderCommissionPlanApproval planApproval = new InstitutionProviderCommissionPlanApproval();
            String planIds = batchApprovalDto.getPlanIds().stream()
                    .map(String::valueOf)
                    .collect(Collectors.joining(","));
            planApproval.setFkInstitutionProviderCommissionPlanIds(planIds);
            planApproval.setApprovalStatus(ApprovalStatusEnum.PENDING_APPROVAL.getCode());
            planApproval.setSubmitNote(batchApprovalDto.getSubmitNote());
            planApproval.setFkStaffId(batchApprovalDto.getStaffId());
            planApproval.setCommissionPlanName("批量审核-" + DateUtil.formatDate(new Date()));
            planApproval.setGmtCreate(new Date());
            planApproval.setGmtModified(new Date());
            planApproval.setGmtModified(new Date());
            planApproval.setGmtCreateUser(SecureUtil.getLoginId());
            planApproval.setTypeKey(ProviderCommissionPlanApprovalTypeEnum.MASS.getCode());
            planApproval.setGmtModifiedUser(SecureUtil.getLoginId());
            BatchApprovalOtherInfoVo otherInfo = new BatchApprovalOtherInfoVo();
            if (Objects.nonNull(batchApprovalDto.getApprovalOtherInfo())) {
                otherInfo = batchApprovalDto.getApprovalOtherInfo();
            }
            otherInfo.setPlanTerritoryList(batchApprovalDto.getPlanTerritoryList());
            planApproval.setMassContentJson(JSONObject.toJSONString(otherInfo));
            commissionPlanApprovalMapper.insert(planApproval);
            //批量修改方案状态
            this.update(new LambdaUpdateWrapper<InstitutionProviderCommissionPlan>()
                    .set(InstitutionProviderCommissionPlan::getApprovalStatus, ApprovalStatusEnum.PENDING_APPROVAL.getCode())
                    .set(InstitutionProviderCommissionPlan::getIsLocked, 1)
                    .set(InstitutionProviderCommissionPlan::getGmtModified, new Date())
                    .set(InstitutionProviderCommissionPlan::getGmtModifiedUser, SecureUtil.getLoginId())
                    .in(InstitutionProviderCommissionPlan::getId, batchApprovalDto.getPlanIds()));

        }
    }

    @Override
    public List<BatchApprovalVo> getBatchApprovalList(Long providerContractId) {
        //获取待审批的方案id
        List<InstitutionProviderCommissionPlanApproval> batchApprovalList = commissionPlanApprovalMapper.selectList(new LambdaQueryWrapper<InstitutionProviderCommissionPlanApproval>()
                        .eq(InstitutionProviderCommissionPlanApproval::getFkStaffId, SecureUtil.getStaffId())
                        .eq(InstitutionProviderCommissionPlanApproval::getTypeKey, ProviderCommissionPlanApprovalTypeEnum.MASS.getCode())
                        .eq(InstitutionProviderCommissionPlanApproval::getApprovalStatus, ApprovalStatusEnum.PENDING_APPROVAL.getCode())
                        .exists("select 1 from m_institution_provider_commission_plan where" +
                                " find_in_set(id, m_institution_provider_commission_plan_approval.fk_institution_provider_commission_plan_ids) and approval_status = 1" +
                                " and fk_institution_provider_contract_id = " + providerContractId))
                .stream()
                .collect(Collectors.toMap(
                        InstitutionProviderCommissionPlanApproval::getFkInstitutionProviderCommissionPlanIds,
                        Function.identity(),
                        (a, b) -> a.getGmtCreate().after(b.getGmtCreate()) ? a : b))
                .values()
                .stream()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(batchApprovalList)) {
            return Collections.emptyList();
        }
        //再校验方案时候已经审核过了的
        batchApprovalList = batchApprovalList.stream().map(approval -> {
            List<InstitutionProviderCommissionPlanApproval> approvalList = commissionPlanApprovalMapper.selectList(new LambdaQueryWrapper<InstitutionProviderCommissionPlanApproval>()
                    .eq(InstitutionProviderCommissionPlanApproval::getFkInstitutionProviderCommissionPlanIds, approval.getFkInstitutionProviderCommissionPlanIds())
                    .orderByDesc(InstitutionProviderCommissionPlanApproval::getGmtCreate));
            InstitutionProviderCommissionPlanApproval latestPlanApproval = approvalList.get(0);
            if (latestPlanApproval.getApprovalStatus().equals(ApprovalStatusEnum.PASS.getCode())
                    || latestPlanApproval.getApprovalStatus().equals(ApprovalStatusEnum.REJECT.getCode())) {
                //如果最新的审核记录存在审核通过或者拒绝的记录,就说明该批量审批不是处于待审核的状态
                return null;
            }
            //如果最新的审核记录不是批量审核,剔除
            if (!latestPlanApproval.getTypeKey().equals(ProviderCommissionPlanApprovalTypeEnum.MASS.getCode())) {
                return null;
            }
            return approval;
        }).collect(Collectors.toList()).stream().filter(Objects::nonNull).collect(Collectors.toList());
        List<BatchApprovalVo> list = batchApprovalList.stream().map(approval -> {
            BatchApprovalVo vo = new BatchApprovalVo();
            BeanUtils.copyProperties(approval, vo);
            BatchApprovalOtherInfoVo otherInfo = new BatchApprovalOtherInfoVo();
            //方案名称
            if (StringUtils.isBlank(vo.getFkInstitutionProviderCommissionPlanIds())) {
                return null;
            }
            List<Long> planIds = Arrays.asList(vo.getFkInstitutionProviderCommissionPlanIds().split(",")).stream().map(Long::valueOf).collect(Collectors.toList());
            List<InstitutionProviderCommissionPlan> plans = institutionProviderCommissionPlanMapper.selectList(new LambdaQueryWrapper<InstitutionProviderCommissionPlan>()
                    .in(InstitutionProviderCommissionPlan::getId, planIds));
            if (CollectionUtils.isEmpty(plans)) {
                return null;
            }
            String planNames = plans.stream()
                    .map(InstitutionProviderCommissionPlan::getName)
                    .collect(Collectors.joining(","));
            vo.setPlanNames(planNames);
            //适用地区
            Map<Long, List<PlanTerritoryVo>> planTerritories = getPlanTerritories(Arrays.asList(planIds.get(0)));
            if (Objects.nonNull(planTerritories.get(planIds.get(0)))) {
                vo.setPlanTerritoryList(planTerritories.get(planIds.get(0)));
            }
            if (StringUtils.isNotBlank(vo.getMassContentJson())) {
                otherInfo = JSONObject.parseObject(vo.getMassContentJson(), BatchApprovalOtherInfoVo.class);
            }
            vo.setApprovalOtherInfo(otherInfo);
            return vo;
        }).collect(Collectors.toList());
        return list.stream().filter(Objects::nonNull)
                .sorted(Comparator.comparing(InstitutionProviderCommissionPlanApproval::getGmtCreate).reversed())
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void examineBatchApproval(ExamineBatchApprovalDto examineBatchApprovalDto) {
        InstitutionProviderCommissionPlanApproval planApproval = commissionPlanApprovalMapper.selectById(examineBatchApprovalDto.getId());
        if (Objects.isNull(planApproval)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_PLAN_RECORD_NOT_FOUND", "审批记录不存在"));
        }
        //新增审核记录
        InstitutionProviderCommissionPlanApproval newPlanApproval = InstitutionProviderCommissionPlanApproval.builder()
                .fkInstitutionProviderCommissionPlanIds(planApproval.getFkInstitutionProviderCommissionPlanIds())
                .approvalStatus(examineBatchApprovalDto.getApprovalStatus())
                .approvalComment(examineBatchApprovalDto.getApprovalComment())
                .approvalTime(new Date())
                .fkStaffId(SecureUtil.getStaffId())
                .commissionPlanName(planApproval.getCommissionPlanName())
                .typeKey(ProviderCommissionPlanApprovalTypeEnum.MASS.getCode())
                .build();
        newPlanApproval.setGmtCreate(new Date());
        newPlanApproval.setGmtModified(new Date());
        newPlanApproval.setGmtCreateUser(SecureUtil.getLoginId());
        newPlanApproval.setGmtModifiedUser(SecureUtil.getLoginId());
        commissionPlanApprovalMapper.insert(newPlanApproval);
        List<Long> planIds = Arrays.asList(planApproval.getFkInstitutionProviderCommissionPlanIds().split(",")).stream().map(Long::valueOf).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(planIds)) {
            //修改方案状态
            this.update(new LambdaUpdateWrapper<InstitutionProviderCommissionPlan>()
                    .set(InstitutionProviderCommissionPlan::getApprovalStatus, examineBatchApprovalDto.getApprovalStatus())
                    .set(InstitutionProviderCommissionPlan::getGmtModified, new Date())
                    .in(InstitutionProviderCommissionPlan::getId, planIds));
        }
    }

    @Override
    public List<WorkbenchApprovalVo> getWorkbenchApprovalList(PmpWorkbenchApprovalDto workbenchApprovalDto) {
        return approvalStrategyFactory.getStrategy(StringUtils.isBlank(workbenchApprovalDto.getApprovalType()) ? "ALL" : workbenchApprovalDto.getApprovalType())
                .getApprovalList(workbenchApprovalDto);
    }

    @Override
    public List<WorkbenchApprovalVo> getWorkbenchApprovalPage(PmpWorkbenchApprovalDto workbenchApprovalDto, Page page) {
        workbenchApprovalDto.setLoginId(SecureUtil.getLoginId());
        workbenchApprovalDto.setStaffId(SecureUtil.getStaffId());
        List<WorkbenchApprovalVo> result = getWorkbenchApprovalList(workbenchApprovalDto);
        //排序
        result = result.stream()
                .sorted(Comparator.comparing(WorkbenchApprovalVo::getGmtCreate).reversed())
                .collect(Collectors.toList());
        // 分页处理
        int currentPage = page.getCurrentPage();
        int showCount = page.getShowCount();
        int totalCount = result.size();
        int fromIndex = (currentPage - 1) * showCount;
        int toIndex = Math.min(fromIndex + showCount, totalCount);

        List<WorkbenchApprovalVo> pagedResult = fromIndex >= totalCount ? Collections.emptyList() : result.subList(fromIndex, toIndex);
        // 设置分页信息
        page.setTotalResult(totalCount);
        page.setTotalPage(Objects.nonNull(showCount) && showCount > 0 ? (totalCount + showCount - 1) / showCount : 0);
        return pagedResult;
    }

    @Override
    public List<InstitutionPlanVo> institutionPlanPage(IdDto params, Page page) {
        List<Long> userPermissionPlanIds = getUserPermissionPlanIds(null);
        if (CollectionUtils.isEmpty(userPermissionPlanIds)) {
            page.setTotalPage(0);
            page.setTotalResult(0);
            return Collections.emptyList();
        }
        List<Long> planIds = commissionPlanInstitutionMapper.selectList(new LambdaQueryWrapper<InstitutionProviderCommissionPlanInstitution>()
                        .eq(InstitutionProviderCommissionPlanInstitution::getFkInstitutionId, params.getId())
                        .in(InstitutionProviderCommissionPlanInstitution::getFkInstitutionProviderCommissionPlanId, userPermissionPlanIds))
                .stream().map(InstitutionProviderCommissionPlanInstitution::getFkInstitutionProviderCommissionPlanId)
                .distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(planIds)) {
            page.setTotalPage(0);
            page.setTotalResult(0);
            return Collections.emptyList();
        }
        IPage<InstitutionProviderCommissionPlan> pages = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<InstitutionPlanVo> result = institutionProviderCommissionPlanMapper.selectInstitutionProviderCommissionPlans(pages, planIds);
        Map<Long, ProviderVo> providerMap = new HashMap<>();
        Map<Long, InstitutionProviderContract> contractMap = new HashMap<>();
        List<Long> providerIds = result.stream().map(InstitutionPlanVo::getInstitutionProviderId).distinct().collect(Collectors.toList());
        List<Long> contractIds = result.stream().map(InstitutionPlanVo::getContractId).distinct().collect(Collectors.toList());
        List<ProviderVo> providerVos = institutionCenterMapper.institutionProviderList(SecureUtil.getCompanyIds(), SecureUtil.getCountryIds());
        if (CollectionUtils.isNotEmpty(providerIds)) {
            providerMap = providerVos.stream()
                    .filter(providerVo -> providerIds.contains(providerVo.getInstitutionProviderId()))
                    .collect(Collectors.toMap(
                            ProviderVo::getInstitutionProviderId,
                            Function.identity(),
                            (existing, replacement) -> existing));
        }
        if (CollectionUtils.isNotEmpty(contractIds)) {
            contractMap = providerContractService.getBaseMapper().selectList(new LambdaQueryWrapper<InstitutionProviderContract>()
                            .in(InstitutionProviderContract::getId, contractIds))
                    .stream()
                    .collect(Collectors.toMap(
                            InstitutionProviderContract::getId,
                            Function.identity(),
                            (existing, replacement) -> existing));
        }
        Map<Long, InstitutionProviderContract> finalContractMap = contractMap;
        Map<Long, ProviderVo> finalProviderMap = providerMap;
        result.stream().forEach(plan -> {
            plan.setContractTitle(finalContractMap.getOrDefault(plan.getContractId(), new InstitutionProviderContract()).getContractTitle());
            plan.setInstitutionProviderName(finalProviderMap.getOrDefault(plan.getInstitutionProviderId(), new ProviderVo()).getName());
        });
        int totalCount = (int) pages.getTotal();
        Integer showCount = page.getShowCount();
        page.setTotalPage(page.getShowCount() != null && showCount > 0 ? totalCount % showCount == 0 ? totalCount / showCount : totalCount / showCount + 1 : 0);
        page.setTotalResult(totalCount);
        return result;
    }

    @Override
    public Map<Long, Integer> institutionPlanCount(List<Long> institutionIds) {
        if (CollectionUtils.isEmpty(institutionIds)) {
            return Collections.emptyMap();
        }
        List<Long> userPermissionPlanIds = getUserPermissionPlanIds(null);
        if (CollectionUtils.isEmpty(userPermissionPlanIds)) {
            return Collections.emptyMap();
        }
        Map<Long, Integer> map = commissionPlanInstitutionMapper.selectList(new LambdaQueryWrapper<InstitutionProviderCommissionPlanInstitution>()
                        .in(InstitutionProviderCommissionPlanInstitution::getFkInstitutionId, institutionIds)
                        .in(InstitutionProviderCommissionPlanInstitution::getFkInstitutionProviderCommissionPlanId, userPermissionPlanIds))
                .stream()
                .collect(Collectors.groupingBy(
                        InstitutionProviderCommissionPlanInstitution::getFkInstitutionId,
                        Collectors.collectingAndThen(
                                Collectors.mapping(
                                        InstitutionProviderCommissionPlanInstitution::getFkInstitutionProviderCommissionPlanId,
                                        Collectors.toSet()
                                ),
                                Set::size
                        )
                ));
        // 给所有 institutionId 默认值 0（如果没查到则赋 0）
        Map<Long, Integer> result = new HashMap<>();
        for (Long institutionId : institutionIds) {
            result.put(institutionId, map.getOrDefault(institutionId, 0));
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePlanStatus(UpdatePlanStatusDto statusDto) {
        InstitutionProviderCommissionPlan plan = this.getById(statusDto.getPlanId());
        if (Objects.isNull(plan)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_PLAN_NOT_FOUND", "方案不存在"));
        }
        plan.setIsActive(statusDto.getIsActive());
        plan.setGmtModified(new Date());
        plan.setGmtModifiedUser(SecureUtil.getLoginId());
        this.updateById(plan);
        //下架方案-同步到代理端下架方案
        if (statusDto.getIsActive().equals(0)) {
            agentCommissionPlanService.update(new LambdaUpdateWrapper<AgentCommissionPlan>()
                    .set(AgentCommissionPlan::getIsActive, statusDto.getIsActive())
                    .set(AgentCommissionPlan::getGmtModified, new Date())
                    .set(AgentCommissionPlan::getGmtModifiedUser, SecureUtil.getLoginId())
                    .eq(AgentCommissionPlan::getFkInstitutionProviderCommissionPlanId, statusDto.getPlanId()));
        } else {
            //上架方案或明细，只操作采购端，但需要标记修改标记，提示用户所作的修改。
            agentCommissionPlanService.update(new LambdaUpdateWrapper<AgentCommissionPlan>()
                    .set(AgentCommissionPlan::getIsInstitutionProviderCommissionModify, 1)
                    .set(AgentCommissionPlan::getGmtModified, new Date())
                    .set(AgentCommissionPlan::getGmtModifiedUser, SecureUtil.getLoginId())
                    .eq(AgentCommissionPlan::getFkInstitutionProviderCommissionPlanId, statusDto.getPlanId()));
        }

        //操作日志-上下架方案
        List<LogDto> logs = new ArrayList<>();
        logs.add(GeneratorLogsUtil.generatorLog(LogTypeEnum.UPDATE_PLAN,
                LogTableEnum.PROVIDER_PLAN,
                statusDto.getPlanId(),
                statusDto.getIsActive().equals(1) ? LogEventEnum.ACTIVE_PLAN : LogEventEnum.UN_ACTIVE_PLAN,
                SecureUtil.getLoginId(),
                plan.getName(),
                plan.getFkInstitutionProviderContractId()));
        commissionChangedEventPublisher.publishCommissionChangedEvent(logs);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePlanRenewalStatus(UpdatePlanRenewalStatusDto renewalStatusDto) {
        InstitutionProviderCommissionPlan plan = this.getById(renewalStatusDto.getPlanId());
        if (Objects.isNull(plan)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_PLAN_NOT_FOUND", "方案不存在"));
        }
        plan.setIsRenewal(renewalStatusDto.getIsRenewal());
        plan.setGmtModified(new Date());
        plan.setGmtModifiedUser(SecureUtil.getLoginId());
        this.updateById(plan);
    }

    private void checkProviderCommissionPlan(SaveProviderCommissionPlanDto providerCommissionPlanDto) {
        if (Objects.isNull(providerCommissionPlanDto.getStartTime())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_START_DATE_REQUIRED", "方案开始时间不能为空"));
        }
        if (providerCommissionPlanDto.getIsTimeless() < 1 && Objects.isNull(providerCommissionPlanDto.getEndTime())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_END_DATE_REQUIRED", "方案结束时间不能为空"));
        }
        //合同没有附件不能保存
        int contractMediaCount = pmpMediaAndAttachedService.count(new LambdaQueryWrapper<PmpMediaAndAttached>()
                .eq(PmpMediaAndAttached::getFkTableName, MediaTableEnum.PROVIDER_CONTRACT.getCode())
                .eq(PmpMediaAndAttached::getFkTableId, providerCommissionPlanDto.getInstitutionProviderContractId()));
        if (contractMediaCount < 1) {
            throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_CONTRACT_FILE_MISSING", "合同附件暂未上传,请先上传合同附件"));
        }
    }

    private void checkDelPlan(Long id) {
        //佣金方案的删除，若有代理那边有继承，不能删，
        // 有方案附件，不能删，
        // 有佣金明细不能删。需要用户，一层层删，当能删，把方案下关系数据删除(学校，适用，日志等)
        Integer agentPlan = agentCommissionPlanMapper.selectCount(new LambdaQueryWrapper<AgentCommissionPlan>()
                .eq(AgentCommissionPlan::getFkInstitutionProviderCommissionPlanId, id));
        if (agentPlan > 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_PLAN_INHERITED", "方案已被代理方案继承,无法删除"));

        }
    }
}
