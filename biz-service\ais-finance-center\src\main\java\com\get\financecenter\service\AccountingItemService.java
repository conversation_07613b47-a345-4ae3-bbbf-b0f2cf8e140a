package com.get.financecenter.service;

import com.get.common.result.Page;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.financecenter.dto.AccountingItemDto;
import com.get.financecenter.vo.AccountingItemDropdownMenuVo;
import com.get.financecenter.vo.AccountingItemSelectVo;

import javax.validation.Valid;
import java.util.List;

public interface AccountingItemService {
    /**
     * 科目下拉框
     *
     * @return
     */
    AccountingItemSelectVo accountingItemSelectById(Long accountingItemId);

    /**
     * 关联项下拉框
     *
     * @param relationTargetKey
     * @param companyId
     * @return
     */
    List<BaseSelectEntity> relationTargetSelect(String relationTargetKey, Long companyId);

    List<BaseSelectEntity> accountingItemTypeSelect();

    List<BaseSelectEntity> balanceDirection();

    List<AccountingItemSelectVo> getAccountingItemAll(@Valid AccountingItemDto accountingItemDto, Page page);

    List<AccountingItemDropdownMenuVo> getAccountingItemByGrade(Integer grade);

    List<AccountingItemDropdownMenuVo> getAccountingItemDropdownMenu();

    Integer save(AccountingItemDto accountingItemDto);

    Integer updateById(AccountingItemDto accountingItemDto);

    void delete(Long id);

    /**
     * 传入科目id，迭代获取所有的子科目的子科目的List
     * @param parentIds
     * @return
     */
    List<Long> getChildrenAccountingItems(List<Long> parentIds);
}
