package com.get.pmpcenter.vo.agent;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/8/11
 * @Version 1.0
 * @apiNote:学校佣金方案信息-AI助手
 */
@Data
public class AiInstitutionCommissionVo {

    @ApiModelProperty(value = "学校名称")
    private String institutionName;

    @ApiModelProperty(value = "学校佣金方案信息")
    private List<AIInstitutionPlan> planList;


    @Data
    @ApiModel(value = "方案信息")
    public static class AIInstitutionPlan {

        @ApiModelProperty(value = "方案名称")
        private String planName;

        @ApiModelProperty(value = "方案明细")
        private List<AIInstitutionCommissionDetail> commissionList;


    }

    @Data
    @ApiModel(value = "方案信息")
    public static class AIInstitutionCommissionDetail {

        @ApiModelProperty(value = "课程名称")
        private String course;

        @ApiModelProperty(value = "课程等级名称")
        private String levelName;

        @ApiModelProperty(value = "佣金")
        private BigDecimal commission;

        @ApiModelProperty(value = "佣金单位")
        private String commissionUnit;

    }
}
