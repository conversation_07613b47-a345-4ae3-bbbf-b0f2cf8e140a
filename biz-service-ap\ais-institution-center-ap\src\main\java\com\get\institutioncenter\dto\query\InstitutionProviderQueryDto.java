package com.get.institutioncenter.dto.query;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class InstitutionProviderQueryDto extends BaseVoEntity {

    /**
     * 国家Id
     */
    @ApiModelProperty(value = "国家Id", required = true)
    private Long fkAreaCountryId;



    /**
     * 查询公司Id
     */
    @ApiModelProperty(value = "查询公司Id")
    private Long fkCompanyId;


    /**
     * 业务国家ids
     */
    @ApiModelProperty(value = "业务国家ids")
    @NotNull(message = "公开对象")
    private List<Long> areaCountryIds;

    /**
     * 所属渠道Id
     */
    @ApiModelProperty(value = "所属渠道Id")
    private Long fkInstitutionChannelId;



    /**
     * 所属集团Id
     */
    @ApiModelProperty(value = "所属集团Id")
    private Long fkInstitutionGroupId;


    /**
     * 学校提供商类型Id：渠道=1，集团=2，学校=3
     */
    @ApiModelProperty(value = "学校提供商类型Id：渠道=1，集团=2，学校=3", required = true)
    @NotNull(message = "学校提供商类型Id不能为空")
    private Long fkInstitutionProviderTypeId;




    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是")
    @NotNull(message = "是否激活不能为空")
    private Boolean isActive;


    /**
     * 查询关键字
     */
    @ApiModelProperty(value = "查询关键字")
    private String keyWord;

    /**
     * 公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2
     */
    @ApiModelProperty(value = "公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2")
    @NotBlank(message = "公开对象")
    private String publicLevel;

    /**
     * 合同状态：0未有合同/1有合同（生效中/已过期）/2续约中
     */
    @ApiModelProperty(value = "合同状态：0未有合同/1有合同（生效中/已过期）/2续约中")
    private Integer contractStatus;

}
