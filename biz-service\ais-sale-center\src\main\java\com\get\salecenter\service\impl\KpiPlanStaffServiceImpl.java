package com.get.salecenter.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.alibaba.nacos.shaded.com.google.common.collect.Maps;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.UtilService;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.permissioncenter.vo.StaffVo;
import com.get.salecenter.dao.sale.KpiPlanGroupItemMapper;
import com.get.salecenter.dao.sale.KpiPlanStaffLabelMapper;
import com.get.salecenter.dao.sale.KpiPlanStaffMapper;
import com.get.salecenter.dao.sale.KpiPlanTargetMapper;
import com.get.salecenter.dto.KpiPlanStaffDto;
import com.get.salecenter.entity.*;
import com.get.salecenter.service.IStaffBdCodeService;
import com.get.salecenter.service.KpiPlanService;
import com.get.salecenter.service.KpiPlanStaffService;
import com.get.salecenter.vo.KpiPlanStaffLabelVo;
import com.get.salecenter.vo.KpiPlanStaffTreeVo;
import com.get.salecenter.vo.KpiPlanStaffVo;
import com.google.common.collect.Sets;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-16
 */
@Service
public class KpiPlanStaffServiceImpl extends ServiceImpl<KpiPlanStaffMapper, KpiPlanStaff> implements KpiPlanStaffService {

    @Resource
    private KpiPlanStaffMapper kpiPlanStaffMapper;

    @Resource
    private UtilService utilService;

    @Resource
    private IPermissionCenterClient permissionCenterClient;

    @Resource
    private KpiPlanService kpiPlanService;

    @Resource
    private IStaffBdCodeService staffBdCodeService;
    @Resource
    private KpiPlanGroupItemMapper kpiPlanGroupItemMapper;
    @Resource
    private KpiPlanTargetMapper kpiPlanTargetMapper;
    @Resource
    private KpiPlanStaffLabelMapper kpiPlanStaffLabelMapper;

    @Override
    public List<KpiPlanStaffVo> datas(KpiPlanStaffDto kpiPlanStaffVo) {
        if (GeneralTool.isEmpty(kpiPlanStaffVo)
                || GeneralTool.isEmpty(kpiPlanStaffVo.getFkKpiPlanId())
                || GeneralTool.isEmpty(kpiPlanStaffVo.getRootFkStaffId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }
        Long fkKpiPlanId = kpiPlanStaffVo.getFkKpiPlanId();
        Long rootFkStaffId = kpiPlanStaffVo.getRootFkStaffId();
        // 获取该KPI方案下所有的考核人员
        List<KpiPlanStaff> kpiPlanStaffs = kpiPlanStaffMapper.selectList(Wrappers.<KpiPlanStaff>lambdaQuery()
                .eq(KpiPlanStaff::getFkKpiPlanId, fkKpiPlanId)
                .orderByDesc(KpiPlanStaff::getViewOrder));
        if(GeneralTool.isEmpty(kpiPlanStaffs)){
            return Collections.emptyList();
        }
        // 所有考核人员
        List<KpiPlanStaffVo> kpiPlanAllStaff = BeanCopyUtils.copyListProperties(kpiPlanStaffs, KpiPlanStaffVo::new);
        Set<Long> staffIds = kpiPlanAllStaff.stream().map(KpiPlanStaffVo::getFkStaffId).collect(Collectors.toSet());
        Map<Long, String> staffNamesMap = permissionCenterClient.getStaffNamesByIds(staffIds);

        //根据考核人员获取对应公司
        Map<Long, String> companyNamesMap = permissionCenterClient.getCompanyNamesByStaffIds(staffIds).getData();


        // 指定人员的业务下属
        Set<Long> staffFollowerIds = Sets.newHashSet();
        Result<List<Long>> followerIdsResult = permissionCenterClient.getStaffFollowerIds(rootFkStaffId);
        if (followerIdsResult.isSuccess() && GeneralTool.isNotEmpty(followerIdsResult.getData())) {
            List<Long> followerIds = followerIdsResult.getData();
            staffFollowerIds.addAll(followerIds);
        }
        //staffFollowerIds.add(rootFkStaffId);
        staffFollowerIds.removeIf(Objects::isNull);
        // 获取考核人员列表（指定人员添加的考核人员）
        List<KpiPlanStaff> staffList = kpiPlanStaffMapper.selectList(Wrappers.<KpiPlanStaff>lambdaQuery()
                .eq(KpiPlanStaff::getFkKpiPlanId, fkKpiPlanId)
                .and(wrapper -> wrapper
                        // 指定人员添加的考核人员
                        .or(w1 -> w1.eq(KpiPlanStaff::getFkStaffIdAdd, rootFkStaffId))
                )
                .orderByDesc(KpiPlanStaff::getCountMode)
                .orderByDesc(KpiPlanStaff::getViewOrder));

        if (GeneralTool.isEmpty(staffList)) { // 没有找到考核人员，返回考核人员树最低层级的考核人员列表
            //staffList = kpiPlanService.getMinLevelStaffs(fkKpiPlanId, staffFollowerIds);
        }
        List<KpiPlanStaffVo> result = BeanCopyUtils.copyListProperties(staffList, KpiPlanStaffVo::new);

        for (KpiPlanStaffVo kpiPlanStaffDto : result) {
            kpiPlanStaffDto.setStaffName(staffNamesMap.get(kpiPlanStaffDto.getFkStaffId()));
            kpiPlanStaffDto.setCompanyName(companyNamesMap.get(kpiPlanStaffDto.getFkStaffId()));
            kpiPlanStaffDto.setCountRoleName(ProjectExtraEnum.getValueByKey(kpiPlanStaffDto.getCountRole(), ProjectExtraEnum.COUNT_ROLE));
            kpiPlanStaffDto.setCountModeName(ProjectExtraEnum.getValueByKey(kpiPlanStaffDto.getCountMode(), ProjectExtraEnum.COUNT_MODE));
            // （获取员工姓名）所有下级配置成员列表
            List<KpiPlanStaffVo> children = this.getChildren(kpiPlanAllStaff, kpiPlanStaffDto.getFkStaffId());
            //List<String> staffNameChildren = children.stream().map(KpiPlanStaffVo::getStaffName).collect(Collectors.toList());
            List<String> staffNameChildren = children.stream()
                    .map(KpiPlanStaffVo::getStaffName)
                    .filter(name -> name != null && !name.trim().isEmpty())  // 过滤掉null和空字符串
                    .collect(Collectors.toList());
            kpiPlanStaffDto.setStaffNameChildren(staffNameChildren);
        }

        //只看自己和自己下属的数据
        //result = result.stream().filter(staff -> staffFollowerIds.contains(staff.getFkStaffId())).collect(Collectors.toList());

        return result;
    }

    /**
     * 递归获取指定员工ID的所有下级配置成员信息
     *
     * @param allStaff    所有成员信息列表
     * @param rootStaffId 目标根节点（指定员工ID）
     * @return 所有下级配置成员信息
     */
    private List<KpiPlanStaffVo> getChildren(List<KpiPlanStaffVo> allStaff, Long rootStaffId) {
        if (GeneralTool.isEmpty(allStaff) || GeneralTool.isEmpty(rootStaffId)) {
            return Collections.emptyList();
        }
        // 直接下属（不包括自己）
        List<KpiPlanStaffVo> directChildren = allStaff.stream().filter(staff -> rootStaffId.equals(staff.getFkStaffIdAdd()) && !rootStaffId.equals(staff.getFkStaffId())).collect(Collectors.toList());
        List<KpiPlanStaffVo> res = new ArrayList<>(directChildren);
        for (KpiPlanStaffVo staffDto : directChildren) {
            List<KpiPlanStaffVo> children = getChildren(allStaff, staffDto.getFkStaffId());
            res.addAll(children);
        }
        return res;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long addKpiPlanStaff(KpiPlanStaffDto kpiPlanStaffVo) {

        if (GeneralTool.isEmpty(kpiPlanStaffVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        Long fkKpiPlanId = kpiPlanStaffVo.getFkKpiPlanId();
        Long rootFkStaffId = kpiPlanStaffVo.getRootFkStaffId();
        Integer countMode = kpiPlanStaffVo.getCountMode();
        List<Long> fkStaffIdList = kpiPlanStaffVo.getFkStaffIdList();

//  不能配置【自己+团队】的组合方式的考核人员
        if (fkStaffIdList.contains(rootFkStaffId) && ProjectExtraEnum.TEAM.key.equals(countMode)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("can_not_add_oneself_to_team"));
        }

//  检查每个员工是否已被添加为相同统计方式的考核人员
        List<KpiPlanStaff> existingStaffs = kpiPlanStaffMapper.selectList(
                Wrappers.<KpiPlanStaff>lambdaQuery()
                        .eq(KpiPlanStaff::getFkKpiPlanId, fkKpiPlanId)
                        .eq(KpiPlanStaff::getCountMode, countMode)
                        .in(KpiPlanStaff::getFkStaffId, fkStaffIdList)
        );

        if (!existingStaffs.isEmpty()) {
            // 获取已存在的员工信息
            Set<Long> staffIds = existingStaffs.stream()
                    .flatMap(staff -> Stream.of(staff.getFkStaffId(), staff.getFkStaffIdAdd()))
                    .collect(Collectors.toSet());

            Map<Long, String> staffNamesMap = permissionCenterClient.getStaffNamesByIds(staffIds);

            // 构造错误信息
            String errorMsg = existingStaffs.stream()
                    .map(staff -> {
                        String value = ProjectExtraEnum.getValueByKey(staff.getCountMode(), ProjectExtraEnum.COUNT_MODE);
                        return staffNamesMap.get(staff.getFkStaffId()) + "（" + value + "）" +
                                LocaleMessageUtils.getMessage("already_been") + "【" +
                                staffNamesMap.get(staff.getFkStaffIdAdd()) + "】";
                    })
                    .collect(Collectors.joining("、")) +
                    LocaleMessageUtils.getMessage("cannot_be_added_repeatedly");

            throw new GetServiceException(errorMsg);
        }

// 3. 获取最大排序值
        KpiPlanStaff maxView = kpiPlanStaffMapper.selectOne(
                Wrappers.<KpiPlanStaff>lambdaQuery()
                        .select(KpiPlanStaff::getViewOrder)
                        .eq(KpiPlanStaff::getFkKpiPlanId, fkKpiPlanId)
                        .orderByDesc(KpiPlanStaff::getViewOrder)
                        .last("limit 1")
        );
        Integer viewOrder = GeneralTool.isEmpty(maxView) ? 0 : maxView.getViewOrder() + 1;

// 4. 批量插入考核人员
        List<KpiPlanStaff> staffsToInsert = fkStaffIdList.stream()
                .map(fkStaffId -> {
                    KpiPlanStaff staff = BeanCopyUtils.objClone(kpiPlanStaffVo, KpiPlanStaff::new);
                    staff.setFkStaffId(fkStaffId);
                    staff.setViewOrder(viewOrder);
                    staff.setFkStaffIdAdd(rootFkStaffId);
                    utilService.setCreateInfo(staff);
                    return staff;
                })
                .collect(Collectors.toList());

// 批量插入
        staffsToInsert.forEach(kpiPlanStaffMapper::insert);

// 返回第一个插入记录的ID（根据业务需求可能需要调整）
        return staffsToInsert.get(0).getId();

//        if (GeneralTool.isEmpty(kpiPlanStaffVo)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
//        }
//        Long fkKpiPlanId = kpiPlanStaffVo.getFkKpiPlanId();
//        Long rootFkStaffId = kpiPlanStaffVo.getRootFkStaffId();
//        Integer countMode = kpiPlanStaffVo.getCountMode();
//        Long fkStaffId = kpiPlanStaffVo.getFkStaffId();
//        // 不能配置【自己+团队】的组合方式的考核人员
//        if (rootFkStaffId.equals(fkStaffId) && ProjectExtraEnum.TEAM.key.equals(countMode)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("can_not_add_oneself_to_team"));
//        }
//        // 添加操作，需要做检测，当已经存在同一：统计方式+考核人员，提示：刘小明（个人）已经被【陈海斌】添加为考核人员，不能再添加。
//        KpiPlanStaff verifyKpiPlanStaff = kpiPlanStaffMapper.selectOne(Wrappers.<KpiPlanStaff>lambdaQuery()
//                .eq(KpiPlanStaff::getFkKpiPlanId, fkKpiPlanId)
//                .eq(KpiPlanStaff::getCountMode, countMode)
//                .eq(KpiPlanStaff::getFkStaffId, fkStaffId)
//                .last("limit 1"));
//        if (GeneralTool.isNotEmpty(verifyKpiPlanStaff)) {
//            Long verifyFkStaffId = verifyKpiPlanStaff.getFkStaffId();
//            Long verifyFkStaffIdAdd = verifyKpiPlanStaff.getFkStaffIdAdd();
//            Integer verifyCountMode = verifyKpiPlanStaff.getCountMode();
//            Set<Long> staffIds = Sets.newHashSet(verifyFkStaffId, verifyFkStaffIdAdd);
//            Map<Long, String> staffNamesMap = permissionCenterClient.getStaffNamesByIds(staffIds);
//            String value = ProjectExtraEnum.getValueByKey(verifyCountMode, ProjectExtraEnum.COUNT_MODE);
//            throw new GetServiceException(staffNamesMap.get(verifyFkStaffId) + "（" + value + "）"+LocaleMessageUtils.getMessage("already_been")+"【" + staffNamesMap.get(verifyFkStaffIdAdd) + "】"+LocaleMessageUtils.getMessage("cannot_be_added_repeatedly"));
//        }
//
//        KpiPlanStaff kpiPlanStaff = BeanCopyUtils.objClone(kpiPlanStaffVo, KpiPlanStaff::new);
//        //获取最大排序
//        KpiPlanStaff maxView = kpiPlanStaffMapper.selectOne(Wrappers.<KpiPlanStaff>lambdaQuery()
//                .select(KpiPlanStaff::getViewOrder)
//                .eq(KpiPlanStaff::getFkKpiPlanId, fkKpiPlanId)
//                .orderByDesc(KpiPlanStaff::getViewOrder)
//                .last("limit 1"));
//        Integer viewOrder = GeneralTool.isEmpty(maxView) ? 0 : maxView.getViewOrder() + 1;
//        kpiPlanStaff.setViewOrder(viewOrder);
//        kpiPlanStaff.setFkStaffIdAdd(rootFkStaffId);
//        utilService.setCreateInfo(kpiPlanStaff);
//        kpiPlanStaffMapper.insert(kpiPlanStaff);
//        return kpiPlanStaff.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        KpiPlanStaff kpiPlanStaff = kpiPlanStaffMapper.selectById(id);
        if (GeneralTool.isEmpty(kpiPlanStaff)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }

        // 删除所有下级配置成员相关记录
        Set<Long> kpiPlanStaffIds = new HashSet<>();
        if(GeneralTool.isNotEmpty(kpiPlanStaff.getCountMode())&&kpiPlanStaff.getCountMode()==2){
            List<KpiPlanStaff> allKpiPlanStaff = kpiPlanStaffMapper.selectList(Wrappers.<KpiPlanStaff>lambdaQuery()
                    .eq(KpiPlanStaff::getFkKpiPlanId, kpiPlanStaff.getFkKpiPlanId())
                    .orderByDesc(KpiPlanStaff::getViewOrder));
            List<KpiPlanStaffVo> allKpiPlanStaffDto = BeanCopyUtils.copyListProperties(allKpiPlanStaff, KpiPlanStaffVo::new);
            List<KpiPlanStaffVo> children = this.getChildren(allKpiPlanStaffDto, kpiPlanStaff.getFkStaffId());
            // 获取所有下级配置成员的m_kpi_plan_staff主键id
            kpiPlanStaffIds = children.stream().map(KpiPlanStaffVo::getId).collect(Collectors.toSet());
            kpiPlanStaffIds.add(id);
        }else {
            kpiPlanStaffIds.add(id);
        }
        int staffDelete = kpiPlanStaffMapper.deleteBatchIds(kpiPlanStaffIds);
        if (staffDelete < 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }

        // 删除m_kpi_plan_target相关记录
        List<KpiPlanGroupItem> groupItemList = kpiPlanGroupItemMapper.selectList(Wrappers.<KpiPlanGroupItem>lambdaQuery()
                .eq(KpiPlanGroupItem::getFkKpiPlanId, kpiPlanStaff.getFkKpiPlanId()));
        if (GeneralTool.isNotEmpty(groupItemList)) {
            Set<Long> groupItemIds = groupItemList.stream().map(KpiPlanGroupItem::getId).collect(Collectors.toSet());
            // 当前记录删除 fkKpiPlanStaffId，唯一
            int targetDelete = kpiPlanTargetMapper.delete(Wrappers.<KpiPlanTarget>lambdaQuery()
                    .in(KpiPlanTarget::getFkKpiPlanGroupItemId, groupItemIds)
                    .in(KpiPlanTarget::getFkKpiPlanStaffId, kpiPlanStaffIds));
            if (targetDelete < 0) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
            }
        }
        // 删除m_kpi_plan_staff_label相关记录
        int staffLabelDelete = kpiPlanStaffLabelMapper.delete(Wrappers.<KpiPlanStaffLabel>lambdaQuery()
                .in(KpiPlanStaffLabel::getFkKpiPlanStaffId, kpiPlanStaffIds));
        if (staffLabelDelete < 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }
    }

    @Override
    public void movingOrder(Long fkKpiPlanId, Integer start, Integer end) {
        if (GeneralTool.isEmpty(fkKpiPlanId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        LambdaQueryWrapper<KpiPlanStaff> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(KpiPlanStaff::getFkKpiPlanId,fkKpiPlanId);
        if (end > start){
            lambdaQueryWrapper.between(KpiPlanStaff::getViewOrder,start,end).orderByDesc(KpiPlanStaff::getViewOrder);
        }else {
            lambdaQueryWrapper.between(KpiPlanStaff::getViewOrder,end,start).orderByDesc(KpiPlanStaff::getViewOrder);

        }
        List<KpiPlanStaff> kpiPlanStaffs = list(lambdaQueryWrapper);

        //从下上移 列表倒序排序
        List<KpiPlanStaff> updateList = Lists.newArrayList();
        if (end > start){
            int finalEnd = end;
            List<KpiPlanStaff> sortedList = Lists.newArrayList();
            KpiPlanStaff policy = kpiPlanStaffs.get(kpiPlanStaffs.size() - 1);
            sortedList.add(policy);
            kpiPlanStaffs.remove(kpiPlanStaffs.size() - 1);
            sortedList.addAll(kpiPlanStaffs);
            for (KpiPlanStaff kpiPlanStaff : sortedList) {
                kpiPlanStaff.setViewOrder(finalEnd);
                finalEnd--;
            }
            updateList.addAll(sortedList);
        }else {
            int finalStart = start;
            List<KpiPlanStaff> sortedList = Lists.newArrayList();
            KpiPlanStaff policy = kpiPlanStaffs.get(0);
            kpiPlanStaffs.remove(0);
            sortedList.addAll(kpiPlanStaffs);
            sortedList.add(policy);
            for (KpiPlanStaff kpiPlanStaff : sortedList) {
                kpiPlanStaff.setViewOrder(finalStart);
                finalStart--;
            }
            updateList.addAll(sortedList);
        }

        if (GeneralTool.isNotEmpty(updateList)) {
            updateList.forEach(kpiPlanStaff -> utilService.setUpdateInfo(kpiPlanStaff));
            boolean batch = updateBatchById(updateList);
            if (!batch){
                throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
            }
        }
    }

    @Override
    public List<StaffVo> getKpiStaffSelect(KpiPlanStaffDto kpiPlanStaffVo) {
        // 参数校验保持不变
        if (GeneralTool.isEmpty(kpiPlanStaffVo)
                || GeneralTool.isEmpty(kpiPlanStaffVo.getFkKpiPlanId())
                || GeneralTool.isEmpty(kpiPlanStaffVo.getRootFkStaffId())
                || GeneralTool.isEmpty(kpiPlanStaffVo.getCountRole())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }

        Long fkKpiPlanId = kpiPlanStaffVo.getFkKpiPlanId();
        Long rootFkStaffId = kpiPlanStaffVo.getRootFkStaffId();
        Integer countRole = kpiPlanStaffVo.getCountRole();

        KpiPlan kpiPlan = kpiPlanService.getById(fkKpiPlanId);
        if(GeneralTool.isEmpty(kpiPlan)){
            return Collections.emptyList();
        }
        List<Long> fkCompanyIds = new ArrayList<>();
        // 修改点1：将单个fkCompanyId改为解析多个公司ID
        if(GeneralTool.isNotEmpty(kpiPlanStaffVo.getCompanyId())){
            fkCompanyIds.add(kpiPlanStaffVo.getCompanyId());
        }else {
            String fkCompanyIdsStr = kpiPlan.getFkCompanyIds();
            fkCompanyIds = GeneralTool.isNotEmpty(fkCompanyIdsStr)
                    ? Arrays.stream(fkCompanyIdsStr.split(","))
                    .map(String::trim)
                    .filter(s -> !s.isEmpty())
                    .map(Long::parseLong)
                    .collect(Collectors.toList())
                    : Collections.emptyList();
        }

        if (fkCompanyIds.isEmpty()) {
            return Collections.emptyList();
        }

        // 指定人员 + 业务下属（保持不变）
        Set<Long> staffFollowerIds = Sets.newHashSet();
        List<Long> followerIds = permissionCenterClient.getStaffFollowerIds(rootFkStaffId).getData();
        staffFollowerIds.addAll(followerIds);
        staffFollowerIds.add(rootFkStaffId);
        staffFollowerIds.removeIf(Objects::isNull);

        List<StaffVo> staffDtos = new ArrayList<>();

        //BD（修改点2：将eq改为in查询）
        if (ProjectExtraEnum.BD.key.equals(countRole)) {
            List<StaffBdCode> staffBdCodes = staffBdCodeService.list(Wrappers.<StaffBdCode>lambdaQuery()
                    .in(StaffBdCode::getFkCompanyId, fkCompanyIds));  // 改为in查询
            if (GeneralTool.isNotEmpty(staffBdCodes)) {
                Set<Long> fkStaffIds = staffBdCodes.stream()
                        .map(StaffBdCode::getFkStaffId)
                        .filter(staffFollowerIds::contains)
                        .collect(Collectors.toSet());
                staffDtos = permissionCenterClient.getStaffByIds(fkStaffIds);
            }
        }

        String departmentStr = null;
        //项目成员（修改点3：合并多个公司的配置）
        if (ProjectExtraEnum.STUDENT_OFFER_STAFF.key.equals(countRole)) {
            Map<Long, String> companyConfigMap = permissionCenterClient.getCompanyConfigMap(
                    ProjectKeyEnum.KPI_PLAN_STAFF_DEPARTMENT.key, 1).getData();
            // 获取所有公司的配置，用逗号连接
            departmentStr = fkCompanyIds.stream()
                    .map(companyConfigMap::get)
                    .filter(Objects::nonNull)
                    .collect(Collectors.joining(","));
        }
        //PM（修改点4：合并多个公司的配置）
        if (ProjectExtraEnum.CPM.key.equals(countRole)) {
            Map<Long, String> companyConfigMap = permissionCenterClient.getCompanyConfigMap(
                    ProjectKeyEnum.KPI_PLAN_STAFF_DEPARTMENT.key, 2).getData();
            // 获取所有公司的配置，用逗号连接
            departmentStr = fkCompanyIds.stream()
                    .map(companyConfigMap::get)
                    .filter(Objects::nonNull)
                    .collect(Collectors.joining(","));
        }

        // 部门ID处理（保持不变）
        List<Long> departmentIds = Lists.newArrayList();
        if(GeneralTool.isNotEmpty(departmentStr)){
            JSONArray jsonArray = JSONObject.parseArray(departmentStr);
            if (GeneralTool.isNotEmpty(jsonArray)){
                departmentIds = jsonArray.toJavaList(Long.class);
            }
        }

        if (GeneralTool.isNotEmpty(departmentIds)) {
            List<BaseSelectEntity> departmentStaffSelectList = permissionCenterClient.getStaffByDepartmentIds(departmentIds).getData();
            if (GeneralTool.isNotEmpty(departmentStaffSelectList)) {
                Set<Long> fkStaffIds = departmentStaffSelectList.stream()
                        .map(BaseSelectEntity::getId)
                        .filter(staffFollowerIds::contains)
                        .collect(Collectors.toSet());
                if (GeneralTool.isNotEmpty(fkStaffIds)) {
                    // 新增公司过滤：只查询属于指定公司的员工
                    List<Long> finalFkCompanyIds = fkCompanyIds;
                    staffDtos = permissionCenterClient.getStaffByIds(fkStaffIds)
                            .stream()
                            .filter(staff -> finalFkCompanyIds.contains(staff.getFkCompanyId()))
                            .collect(Collectors.toList());
                }
            }
        }
        return staffDtos;
//        if (GeneralTool.isEmpty(kpiPlanStaffVo)
//                || GeneralTool.isEmpty(kpiPlanStaffVo.getFkKpiPlanId())
//                || GeneralTool.isEmpty(kpiPlanStaffVo.getRootFkStaffId())
//                || GeneralTool.isEmpty(kpiPlanStaffVo.getCountRole())) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
//        }
//        Long fkKpiPlanId = kpiPlanStaffVo.getFkKpiPlanId();
//        Long rootFkStaffId = kpiPlanStaffVo.getRootFkStaffId();
//        Integer countRole = kpiPlanStaffVo.getCountRole();
//
//        KpiPlan kpiPlan = kpiPlanService.getById(fkKpiPlanId);
//        if(GeneralTool.isEmpty(kpiPlan)){
//           return Collections.emptyList();
//        }
//        Long fkCompanyIds = kpiPlan.getFkCompanyId();
//
//        // 指定人员 + 业务下属
//        Set<Long> staffFollowerIds = Sets.newHashSet();
//        List<Long> followerIds = permissionCenterClient.getStaffFollowerIds(rootFkStaffId).getData();
//        staffFollowerIds.addAll(followerIds);
//        staffFollowerIds.add(rootFkStaffId);
//        staffFollowerIds.removeIf(Objects::isNull);
//
//        List<StaffVo> staffDtos = new ArrayList<>();
//        //BD
//        if (ProjectExtraEnum.BD.key.equals(countRole)) {
//            List<StaffBdCode> staffBdCodes = staffBdCodeService.list(Wrappers.<StaffBdCode>lambdaQuery()
//                    .eq(StaffBdCode::getFkCompanyId, fkCompanyId));
//            if (GeneralTool.isNotEmpty(staffBdCodes)) {
//                Set<Long> fkStaffIds = staffBdCodes.stream()
//                        .map(StaffBdCode::getFkStaffId)
//                        .filter(staffFollowerIds::contains)
//                        .collect(Collectors.toSet());
//                staffDtos = permissionCenterClient.getStaffByIds(fkStaffIds);
//            }
//        }
//
//        String departmentStr = null;
//        //项目成员
//        if (ProjectExtraEnum.STUDENT_OFFER_STAFF.key.equals(countRole)) {
//            Map<Long, String> companyConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.KPI_PLAN_STAFF_DEPARTMENT.key, 1).getData();
//            departmentStr = companyConfigMap.get(fkCompanyId);
//        }
//        //PM
//        if (ProjectExtraEnum.CPM.key.equals(countRole)) {
//            Map<Long, String> companyConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.KPI_PLAN_STAFF_DEPARTMENT.key, 2).getData();
//            departmentStr = companyConfigMap.get(fkCompanyId);
//        }
//        List<Long> departmentIds = Lists.newArrayList();
//        if(GeneralTool.isNotEmpty(departmentStr)){
//            JSONArray jsonArray = JSONObject.parseArray(departmentStr);
//            if (GeneralTool.isNotEmpty(jsonArray)){
//                departmentIds = jsonArray.toJavaList(Long.class);
//            }
//        }
//        if (GeneralTool.isNotEmpty(departmentIds)) {
//            List<BaseSelectEntity> departmentStaffSelectList = permissionCenterClient.getStaffByDepartmentIds(departmentIds).getData();
//            if (GeneralTool.isNotEmpty(departmentStaffSelectList)) {
//                Set<Long> fkStaffIds = departmentStaffSelectList.stream()
//                        .map(BaseSelectEntity::getId)
//                        .filter(staffFollowerIds::contains)
//                        .collect(Collectors.toSet());
//                if (GeneralTool.isNotEmpty(fkStaffIds)) {
//                    staffDtos = permissionCenterClient.getStaffByIds(fkStaffIds);
//                }
//            }
//        }
//        return staffDtos;
    }

    /**
     * 获取该KPI方案下考核人员树（登录人员自己及业务下属设置的整个考核人员树）
     *
     * @param fkKpiPlanId KPI方案Id
     * @return 树形结构
     */
    @Override
    public List<KpiPlanStaffTreeVo> getKpiPlanStaffTree(Long fkKpiPlanId) {
        Long staffId = SecureUtil.getStaffId();
        // 员工id + 业务下属员工ids
        Set<Long> staffIds = Sets.newHashSet();
        List<Long> staffFollowerIds = permissionCenterClient.getStaffFollowerIds(staffId).getData();
        staffIds.addAll(staffFollowerIds);
        staffIds.add(staffId);
        staffIds.removeIf(Objects::isNull);

        List<KpiPlanStaff> kpiPlanStaffs = kpiPlanStaffMapper.selectList(Wrappers.<KpiPlanStaff>lambdaQuery()
                .eq(KpiPlanStaff::getFkKpiPlanId, fkKpiPlanId)
                .in(KpiPlanStaff::getFkStaffId, staffIds)
                .orderByDesc(KpiPlanStaff::getViewOrder));
        if (GeneralTool.isEmpty(kpiPlanStaffs)) {
            return Collections.emptyList();
        }
        // 考核人员标签Id集合
        Set<Long> kpiPlanStaffIds = kpiPlanStaffs.stream().map(KpiPlanStaff::getId).collect(Collectors.toSet());
        List<KpiPlanStaffVo> kpiPlanStaffDtos = BeanCopyUtils.copyListProperties(kpiPlanStaffs, KpiPlanStaffVo::new);
        Map<Long, String> staffNamesMap = permissionCenterClient.getStaffNamesByIds(staffIds);
        // 获取考核人员标签列表
        List<KpiPlanStaffLabel> kpiPlanStaffLabelList = kpiPlanStaffLabelMapper.selectList(Wrappers.<KpiPlanStaffLabel>lambdaQuery()
                .in(KpiPlanStaffLabel::getFkKpiPlanStaffId, kpiPlanStaffIds));
        Map<Long, List<KpiPlanStaffLabelVo>> staffLabelDtoMap = Maps.newHashMap();
        if (GeneralTool.isNotEmpty(kpiPlanStaffLabelList)) {
            List<KpiPlanStaffLabelVo> kpiPlanStaffLabelDtos = BeanCopyUtils.copyListProperties(kpiPlanStaffLabelList, KpiPlanStaffLabelVo::new);
            // 按KPI方案考核人员Id分组
            staffLabelDtoMap = kpiPlanStaffLabelDtos.stream().collect(Collectors.groupingBy(KpiPlanStaffLabelVo::getFkKpiPlanStaffId));
        }
        // 组装属性信息
        for (KpiPlanStaffVo kpiPlanStaffDto : kpiPlanStaffDtos) {
            kpiPlanStaffDto.setStaffName(staffNamesMap.get(kpiPlanStaffDto.getFkStaffId()));
            kpiPlanStaffDto.setCountRoleName(ProjectExtraEnum.getValueByKey(kpiPlanStaffDto.getCountRole(), ProjectExtraEnum.COUNT_ROLE));
            kpiPlanStaffDto.setCountModeName(ProjectExtraEnum.getValueByKey(kpiPlanStaffDto.getCountMode(), ProjectExtraEnum.COUNT_MODE));
            kpiPlanStaffDto.setKpiPlanStaffLabelDtoList(staffLabelDtoMap.get(kpiPlanStaffDto.getId()));
        }
        // 获取树的根节点集合
        Set<Long> rootStaffIds = kpiPlanStaffDtos.stream().filter(staff -> !staffIds.contains(staff.getFkStaffIdAdd()))
                .map(KpiPlanStaffVo::getFkStaffId).collect(Collectors.toSet());
        return this.buildTree(kpiPlanStaffDtos, rootStaffIds,true);
    }

    /**
     * 构建KPI方案考核人员树结构
     *
     * @param kpiPlanStaffs KPI方案考核人员所有节点
     * @param rootStaffIds  根节点的考核人员id
     * @return 树结构
     */
    @Override
    public List<KpiPlanStaffTreeVo> buildTree(List<KpiPlanStaffVo> kpiPlanStaffs, Set<Long> rootStaffIds,Boolean isRoot) {
        Map<Long, KpiPlanStaffTreeVo> nodeMap = new HashMap<>();
        for (KpiPlanStaffVo staff : kpiPlanStaffs) {
            KpiPlanStaffTreeVo node = BeanCopyUtils.objClone(staff, KpiPlanStaffTreeVo::new);
            node.setChildren(Lists.newArrayList());
            nodeMap.put(staff.getFkStaffId(), node);
        }

        List<KpiPlanStaffTreeVo> rootNodes = new ArrayList<>();
        for (KpiPlanStaffVo staff : kpiPlanStaffs) {
            if (rootStaffIds.contains(staff.getFkStaffId())) {
                KpiPlanStaffTreeVo rootNode = nodeMap.get(staff.getFkStaffId());
                // 根节点默认层级
                rootNode.setLevel(1);
                rootNodes.add(rootNode);
                if (isRoot) {
                    addChildrenLevel(rootNode, nodeMap);
                }else {
                    addChildrenLevelNew(rootNode, nodeMap);
                }

            }
        }
        return rootNodes;
    }

    /**
     * 递归设置子节点层级
     *
     * @param rootNode 根节点
     * @param nodeMap  节点集合
     */
    private void addChildrenLevel(KpiPlanStaffTreeVo rootNode, Map<Long, KpiPlanStaffTreeVo> nodeMap) {
        for (KpiPlanStaffTreeVo treeNode : nodeMap.values()) {
            if (treeNode.getFkStaffId().equals(rootNode.getFkStaffId())) {
                continue;
            }
            if (treeNode.getFkStaffIdAdd().equals(rootNode.getFkStaffId())) {
                KpiPlanStaffTreeVo childNode = nodeMap.get(treeNode.getFkStaffId());
                childNode.setLevel(rootNode.getLevel() + 1);
                rootNode.getChildren().add(childNode);
                addChildrenLevel(childNode, nodeMap);
            }
        }
    }

    /**
     * 递归设置子节点层级
     *
     * @param rootNode 根节点
     * @param nodeMap  节点集合
     */
    private void addChildrenLevelNew(KpiPlanStaffTreeVo rootNode, Map<Long, KpiPlanStaffTreeVo> nodeMap) {
        for (KpiPlanStaffTreeVo treeNode : nodeMap.values()) {
            if (treeNode.getFkStaffId().equals(rootNode.getFkStaffId())) {
                continue;
            }
            if (treeNode.getFkStaffIdAdd().equals(rootNode.getFkStaffId()) && treeNode.getCountMode() == 2) {
                KpiPlanStaffTreeVo childNode = nodeMap.get(treeNode.getFkStaffId());
                childNode.setLevel(rootNode.getLevel() + 1);
                rootNode.getChildren().add(childNode);
                addChildrenLevel(childNode, nodeMap);
            }
        }
    }





    /**
     * 获取指定人员下属添加的考核人员列表，然后返回该列表的最小层级
     *
     * @param kpiPlanStaffTree 考核人员树
     * @param staffFollowerIds 指定人员的下属Id集合
     * @return 最小层级
     */
    @Override
    public Integer getMinLevel(List<KpiPlanStaffTreeVo> kpiPlanStaffTree, Set<Long> staffFollowerIds) {
        if (GeneralTool.isEmpty(kpiPlanStaffTree) || GeneralTool.isEmpty(staffFollowerIds)) {
            return null;
        }
        List<KpiPlanStaffTreeVo> result = Lists.newArrayList();
        for (KpiPlanStaffTreeVo staffTreeDto : kpiPlanStaffTree) {
            collectByStaffIdAdd(result, staffTreeDto, staffFollowerIds);
        }
        Optional<Integer> minLevel = result.stream().map(KpiPlanStaffTreeVo::getLevel).min(Integer::compare);
        return minLevel.orElse(null);
    }

    /**
     * 递归收集指定人员下属添加的考核人员
     *
     * @param result           结构列表
     * @param staffTreeDto     当前节点
     * @param staffFollowerIds 指定人员下属id集合
     */
    private void collectByStaffIdAdd(List<KpiPlanStaffTreeVo> result, KpiPlanStaffTreeVo staffTreeDto, Set<Long> staffFollowerIds) {
        // 先判断fkStaffIdAdd是不是我的下属，再判断fkStaffId是不是我的下属
        if (staffFollowerIds.contains(staffTreeDto.getFkStaffIdAdd()) || staffFollowerIds.contains(staffTreeDto.getFkStaffId())) {
            result.add(staffTreeDto);
        } else {
            if (GeneralTool.isNotEmpty(staffTreeDto.getChildren())) {
                for (KpiPlanStaffTreeVo child : staffTreeDto.getChildren()) {
                    collectByStaffIdAdd(result, child, staffFollowerIds);
                }
            }
        }
    }

    /**
     * 根据指定层级获取对应的列表
     *
     * @param kpiPlanStaffTree 考核人员树
     * @param level            树层级
     * @return
     */
    @Override
    public List<KpiPlanStaff> getStaffTreeDtoByLevel(List<KpiPlanStaffTreeVo> kpiPlanStaffTree, Integer level) {
        if (GeneralTool.isEmpty(kpiPlanStaffTree) || GeneralTool.isEmpty(level)) {
            return Collections.emptyList();
        }
        List<KpiPlanStaffTreeVo> result = Lists.newArrayList();
        for (KpiPlanStaffTreeVo staffTreeDto : kpiPlanStaffTree) {
            collectByLevel(result, staffTreeDto, level);
        }
        return BeanCopyUtils.copyListProperties(result, KpiPlanStaff::new);
    }

    /**
     * 递归收集指定层级的考核人员树列表
     *
     * @param result       收集结果列表
     * @param staffTreeDto 当前节点
     * @param targetLevel  目标层级
     */
    private void collectByLevel(List<KpiPlanStaffTreeVo> result, KpiPlanStaffTreeVo staffTreeDto, Integer targetLevel) {
        if (Objects.equals(staffTreeDto.getLevel(), targetLevel)) {
            // 防止添加重复的考核人员
            if (result.stream().noneMatch(staff -> staff.getId().equals(staffTreeDto.getId()))) {
                result.add(staffTreeDto);
            }
        } else {
            if (GeneralTool.isNotEmpty(staffTreeDto.getChildren())) {
                for (KpiPlanStaffTreeVo child : staffTreeDto.getChildren()) {
                    collectByLevel(result, child, targetLevel);
                }
            }
        }
    }

}
