package com.get.financecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 科目余额汇总表Dto
 */
@Data
public class StageAccountingItemDto {

    @ApiModelProperty(value = "公司Id")
    @NotNull(message = "公司Id不能为空")
    private Long companyId;

    @NotNull(message = "开始年月不能为空")
    @ApiModelProperty(value = "开始年月")
    @JsonFormat(pattern = "yyyy-MM", timezone = "GMT+8")
    private Date startTime;

    @NotNull(message = "结束年月不能为空")
    @ApiModelProperty(value = "结束年月")
    @JsonFormat(pattern = "yyyy-MM", timezone = "GMT+8")
    private Date endTime;

    @ApiModelProperty(value = "科目级数")
    @NotNull(message = "科目级数不能为空")
    private Integer grade;

}
