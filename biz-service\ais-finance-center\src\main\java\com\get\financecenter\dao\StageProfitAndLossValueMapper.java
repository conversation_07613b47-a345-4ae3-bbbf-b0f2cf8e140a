package com.get.financecenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.financecenter.dto.ProfitAndLossStatementDto;
import com.get.financecenter.entity.StageProfitAndLossValue;
import com.get.financecenter.vo.ProfitAndLossStatementItemVo;
import com.get.financecenter.vo.ProfitAndLossStatementVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface StageProfitAndLossValueMapper extends BaseMapper<StageProfitAndLossValue> {

    void insertBatch(List<StageProfitAndLossValue> stageProfitAndLossValueList);

    /**
     * 获取损益表标题
     * @param probAndLossStatementDto
     * @return
     */
    List<ProfitAndLossStatementItemVo> getProfitAndLossStatementTitle(@Param("probAndLossStatementDto") ProfitAndLossStatementDto probAndLossStatementDto);

    /**
     * 获取未分配利润数据
     * @param companyId
     * @param year
     * @param month
     * @return
     */
    List<StageProfitAndLossValue> getStageProfitAndLossValueSumList(@Param("companyId") Long companyId, @Param("year") int year, @Param("month") int month);

}
