package com.get.financecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ResponseBo;
import com.get.core.log.annotation.OperationLogger;
import com.get.financecenter.dto.BalanceSheetStatementDto;
import com.get.financecenter.dto.RecalculateTheBalanceSheetDto;
import com.get.financecenter.service.BalanceSheetService;
import com.get.financecenter.vo.BalanceSheetStatementVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Api(tags = "资产负债表管理")
@RestController
@RequestMapping("finance/balanceSheet")
public class StageBalanceSheetController {

    @Resource
    private BalanceSheetService balanceSheetService;

    @ApiOperation(value = "生成并查询资产负债表", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.ADD, description = "财务中心/财务报表/生成并查询资产负债表")
    @PostMapping("createBalanceSheetStatement")
    public ResponseBo<BalanceSheetStatementVo> createProfitAndLossStatement(@RequestBody @Validated BalanceSheetStatementDto balanceSheetStatementDto) {
        BalanceSheetStatementVo balanceSheetStatementVo = balanceSheetService.createProfitAndLossStatement(balanceSheetStatementDto);
        return new ResponseBo<>(balanceSheetStatementVo);
    }

    @ApiOperation(value = "重新统计资产负债表", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.ADD, description = "财务中心/财务报表/重新统计资产负债表")
    @PostMapping("recalculateTheBalanceSheet")
    public ResponseBo recalculateTheBalanceSheet(@RequestBody @Validated RecalculateTheBalanceSheetDto recalculateTheBalanceSheetDto) {
        balanceSheetService.recalculateTheBalanceSheet(recalculateTheBalanceSheetDto);
        return ResponseBo.ok();
    }


}
