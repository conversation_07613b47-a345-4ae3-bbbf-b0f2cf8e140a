<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.EventIncentiveMapper">
    <select id="getEventIncentives" resultType="com.get.salecenter.vo.EventIncentiveListVo">
        SELECT
            a.id,
            a.fk_company_id,
            a.fk_institution_provider_id,
            a.num,
            a.receiving_reward_time,
            a.actual_publicity_time,
            a.event_start_time,
            a.event_end_time,
            a.accord_with_incentive_intake,
            a.incentive_policy,
            a.suggest_check_time,
            a.expect_target_count,
            a.actual_target_count,
            a.fk_currency_type_num,
            a.actual_pay_amount,
            a.remark,
            a.`status`,
            a.gmt_create,
            a.gmt_create_user,
            a.gmt_modified,
            a.gmt_modified_user,
            a.public_level,
            a.event_title,
            a.is_distributed,
            GROUP_CONCAT(DISTINCT b.fk_area_country_id) AS fkAreaCountryIds,
            IFNULL(z.sumAmount,0) AS actualReceivableAmount,
            IFNULL(z.sumAmount,0)-a.actual_pay_amount AS balanceAmount,
            CONCAT(c.name,IFNULL(CONCAT("（",c.name_chn,")"),"")) as fkInstitutionProviderName
        FROM
            m_event_incentive AS a
                LEFT JOIN
            r_event_incentive_area_country AS b
            ON
                a.id = b.fk_event_incentive_id
                LEFT JOIN
            (
                SELECT
                    a.fk_event_incentive_id,
                    SUM(IFNULL(a.amount,0)) AS sumAmount
                FROM
                    m_event_incentive_cost AS a
                GROUP BY
                    a.fk_event_incentive_id
            ) AS z
            ON
                a.id = z.fk_event_incentive_id
        LEFT JOIN ais_institution_center.m_institution_provider c on c.id = a.fk_institution_provider_id
        LEFT JOIN m_event_incentive_cost d on a.id = d.fk_event_incentive_id
        where 1=1
        <if test="eventIncentiveListDto.fkCompanyId != null">
            AND a.fk_company_id = #{eventIncentiveListDto.fkCompanyId}
        </if>
        <if test="eventIncentiveListDto.status != null">
            AND a.status = #{eventIncentiveListDto.status}
        </if>
        <if test="eventIncentiveListDto.remark != null and eventIncentiveListDto.remark !=''">
            AND a.remark like concat("%",#{eventIncentiveListDto.remark},"%")
        </if>
        <if test="eventIncentiveListDto.suggestCheckTimeBegin != null">
            AND DATE_FORMAT(a.suggest_check_time,'%Y-%m-%d') <![CDATA[>= ]]> DATE_FORMAT(#{eventIncentiveListDto.suggestCheckTimeBegin},'%Y-%m-%d' )
        </if>
        <if test="eventIncentiveListDto.suggestCheckTimeEnd != null">
            AND DATE_FORMAT(a.suggest_check_time,'%Y-%m-%d') <![CDATA[<= ]]> DATE_FORMAT(#{eventIncentiveListDto.suggestCheckTimeEnd},'%Y-%m-%d' )
        </if>
        <if test="eventIncentiveListDto.publicLevel != null">
            AND FIND_IN_SET(#{eventIncentiveListDto.publicLevel},a.public_level)
        </if>
        <if test="eventIncentiveListDto.eventTitle != null and eventIncentiveListDto.eventTitle !=''">
            AND LOWER(a.event_title) like concat("%",LOWER(#{eventIncentiveListDto.eventTitle}),"%")
        </if>
        <if test="eventIncentiveListDto.num != null and eventIncentiveListDto.num !=''">
            AND a.num like concat("%",#{eventIncentiveListDto.num},"%" )
        </if>
        GROUP BY a.id
        having 1=1
        <if test="eventIncentiveListDto.fkInstitutionProviderName != null and eventIncentiveListDto.fkInstitutionProviderName !=''">
            AND fkInstitutionProviderName like concat("%",#{eventIncentiveListDto.fkInstitutionProviderName},"%")
        </if>
        <if test="eventIncentiveListDto.fkAreaCountryIdList !=null and eventIncentiveListDto.fkAreaCountryIdList.size()>0">
            <foreach collection="eventIncentiveListDto.fkAreaCountryIdList" item="fkAreaCountry" open=" " separator=" " close=" ">
                and FIND_IN_SET(#{fkAreaCountry},fkAreaCountryIds)
            </foreach>
        </if>
        ORDER BY a.gmt_create DESC
    </select>
    <select id="getEventIncentiveList" resultType="com.get.salecenter.vo.EventIncentiveListVo">
        SELECT
            a.id,
            a.fk_company_id,
            a.fk_institution_provider_id,
            a.num,
            a.receiving_reward_time,
            a.actual_publicity_time,
            a.event_start_time,
            a.event_end_time,
            a.accord_with_incentive_intake,
            a.incentive_policy,
            a.suggest_check_time,
            a.expect_target_count,
            a.actual_target_count,
            a.fk_currency_type_num,
            a.actual_pay_amount,
            a.remark,
            a.`status`,
            a.gmt_create,
            a.gmt_create_user,
            a.gmt_modified,
            a.gmt_modified_user,
            CONCAT(c.name,IFNULL(CONCAT("（",c.name_chn,")"),"")) as fkInstitutionProviderName,
            d.amount,
            d.amount_receivable AS amountReceivable,
            d.id as fkEventIncentiveCostId
        FROM
            m_event_incentive AS a
                LEFT JOIN ais_institution_center.m_institution_provider c on c.id = a.fk_institution_provider_id
                INNER JOIN m_event_incentive_cost d on a.id = d.fk_event_incentive_id
        where 1=1
        <if test="eventIncentiveDistributeDto.fkEventBillId != null">
            AND d.fk_event_bill_id = #{eventIncentiveDistributeDto.fkEventBillId}
        </if>
        order by d.gmt_create desc
</select>
    <select id="getAmountReceivableCurrencyNumbyEventIncentiveIds"
            resultType="com.get.salecenter.vo.EventIncentiveListVo">
        SELECT
            b.id as fkEventIncentiveCostId,
            c.fk_currency_type_num_event as amountReceivableCurrencyNum
        FROM
            `m_event_incentive` a
                LEFT JOIN m_event_incentive_cost b on a.id = b.fk_event_incentive_id
                LEFT JOIN m_event_bill c on c.id = b.fk_event_bill_id
        where a.id in
        <foreach collection="ids" item="id" index="index" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
</mapper>