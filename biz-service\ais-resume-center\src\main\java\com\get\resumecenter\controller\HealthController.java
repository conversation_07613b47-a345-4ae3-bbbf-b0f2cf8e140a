package com.get.resumecenter.controller;

import com.get.core.secure.annotation.VerifyLogin;
import com.get.core.secure.annotation.VerifyPermission;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("api/check")
public class HealthController {

    @GetMapping("/healthz")
    @VerifyLogin(IsVerify = false)
    @VerifyPermission(IsVerify = false)
    public ResponseEntity<String> health() {
        // 核心服务状态检查（简化版）
        boolean isHealthy = true;
        String details = "";

        // 1. 快速检查数据库连接
        try {
            // 执行简单查询如 SELECT 1
        } catch (Exception e) {
            isHealthy = false;
            details += "DB_ERROR;";
        }

        // 2. 返回状态
        if (isHealthy) {
            return ResponseEntity.ok("OK");
        } else {
            return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE).body("ERROR:" + details);
        }
    }
}