package com.get.financecenter.service;

import com.get.core.secure.StaffInfo;
import com.get.financecenter.dto.ReCalculateSubjectBalanceSummaryDto;
import com.get.financecenter.dto.StageAccountingItemDto;
import com.get.financecenter.entity.StageValueTaskQueue;
import com.get.financecenter.vo.StageAccountingItemVo;

import java.util.List;

public interface StageAccountingItemService {

    /**
     * 生成并查询科目余额汇总表
     *
     * @param stageAccountingItemDto
     * @return
     */
    List<StageAccountingItemVo> createProfitAndLossStatement(StageAccountingItemDto stageAccountingItemDto);

    /**
     * 重新统计科目余额汇总表
     * @param reCalculateSubjectBalanceSummaryDto
     */
    void reCalculateSubjectBalanceSummary(ReCalculateSubjectBalanceSummaryDto reCalculateSubjectBalanceSummaryDto);

    void reCalculateSubjectBalanceSummaryAsync(ReCalculateSubjectBalanceSummaryDto reCalculateSubjectBalanceSummaryDto, StaffInfo staffInfo, StageValueTaskQueue stageValueTaskQueue);
}
