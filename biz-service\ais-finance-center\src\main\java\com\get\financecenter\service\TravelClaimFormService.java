package com.get.financecenter.service;

import com.get.common.result.Page;
import com.get.common.result.SearchBean;
import com.get.core.mybatis.utils.ValidList;
import com.get.financecenter.dto.MediaAndAttachedDto;
import com.get.financecenter.dto.TravelClaimFormDto;
import com.get.financecenter.dto.query.TravelClaimFormQueryDto;
import com.get.financecenter.entity.TravelClaimForm;
import com.get.financecenter.vo.FMediaAndAttachedVo;
import com.get.financecenter.vo.TravelClaimFeeTypeVo;
import com.get.financecenter.vo.TravelClaimFormVo;
import java.math.BigDecimal;
import java.util.List;
import javax.validation.Valid;

public interface TravelClaimFormService {
    /**
     * 查询差旅报销申请单
     *
     * @param travelClaimFormQueryDto
     * @param page
     * @return
     */
    List<TravelClaimFormVo> getTravelClaimForms(@Valid TravelClaimFormQueryDto travelClaimFormQueryDto, SearchBean<TravelClaimFormQueryDto> page);

    /**
     * 新增差旅报销申请单
     *
     * @param travelClaimFormDto
     * @return
     */
    Long addTravelClaimForm(TravelClaimFormDto travelClaimFormDto);

    /**
     * 差旅报销费用类型下拉框数据
     *
     * @return
     */
    List<TravelClaimFeeTypeVo> getTravelClaimFeeTypeSelect();

    /**
     * 更新差旅报销申请单
     *
     * @param travelClaimFormDto
     */
    void updateTravelClaimForm(TravelClaimFormDto travelClaimFormDto);

    /**
     * 差旅报销申请单详情
     *
     * @param id
     * @return
     */
    TravelClaimFormVo findTravelClaimFormById(Long id);

    /**
     * 查询差旅报销申请单附件
     *
     * @param data
     * @param page
     * @return
     */
    List<FMediaAndAttachedVo> getItemMedia(MediaAndAttachedDto data, Page page);

    /**
     * 保存差旅报销申请单附件
     *
     * @param mediaAttachedVos
     * @return
     */
    List<FMediaAndAttachedVo> addItemMedia(ValidList<MediaAndAttachedDto> mediaAttachedVos);

    /**
     * 开启差旅报销申请单流程
     *
     * @param companyId
     * @param businessKey
     * @param procdefKey
     */
    void startProcess(Long companyId, Long businessKey, String procdefKey);

    /**
     * 差旅报销申请单作废
     *
     * @param id
     */
    void updateStatus(Long id);

    /**
     * 差旅报销申请单撤单
     *
     * @param id
     * @param summary
     */
    void getRevokeExpenseClaimForm(Long id, String summary);

    /**
     * 根据id获取差旅报销单
     *
     * @param targetId
     * @return
     */
    TravelClaimForm getTravelClaimFormById(Long targetId);

    /**
     * 更新差旅报销单状态
     *
     * @param travelClaimForm
     * @return
     */
    Boolean updateTravelClaimFormStatus(TravelClaimForm travelClaimForm);

    /**
     * 获取差旅报销单总金额
     *
     * @param id
     * @return
     */
    BigDecimal getTravelClaimFormTotalAmount(Long id);

}
