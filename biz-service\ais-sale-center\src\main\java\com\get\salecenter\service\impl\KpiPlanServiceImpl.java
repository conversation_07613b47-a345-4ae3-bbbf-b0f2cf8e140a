package com.get.salecenter.service.impl;

import cn.hutool.core.io.IoUtil;
import cn.hutool.poi.excel.BigExcelWriter;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.shaded.com.google.common.collect.Maps;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.get.common.consts.CacheKeyConstants;
import com.get.common.eunms.ErrorCodeEnum;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.redis.cache.GetRedis;
import com.get.core.secure.UserInfo;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.DateUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.core.tool.utils.RequestHeaderHandler;
import com.get.file.utils.FileUtils;
import com.get.file.utils.TemplateExcelUtils;
import com.get.institutioncenter.vo.AreaCountryVo;
import com.get.institutioncenter.vo.InstitutionProviderInstitutionVo;
import com.get.institutioncenter.vo.InstitutionProviderVo;
import com.get.institutioncenter.entity.MajorLevel;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.institutioncenter.vo.InstitutionVo;
import com.get.permissioncenter.entity.StaffAreaCountry;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.permissioncenter.vo.CompanyVo;
import com.get.salecenter.dao.sale.KpiPlanMapper;
import com.get.salecenter.dto.*;
import com.get.salecenter.entity.*;
import com.get.salecenter.service.*;
import com.get.salecenter.vo.*;
import com.get.salecenter.vo.KpiPlanAllTargetStatisticsVo;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.DataFormat;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;
import org.hibernate.validator.internal.util.stereotypes.Lazy;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-16
 */
@Slf4j
@Service
public class KpiPlanServiceImpl extends ServiceImpl<KpiPlanMapper, KpiPlan> implements KpiPlanService {

    private final static ObjectMapper objectMapper = new ObjectMapper();
    // 最大百分比上限
    private final static BigDecimal maxRatio = new BigDecimal("250");

    @Resource
    private KpiPlanMapper kpiPlanMapper;

    @Resource
    private KpiPlanGroupService kpiPlanGroupService;

    @Resource
    private KpiPlanGroupItemService kpiPlanGroupItemService;

    @Resource
    private KpiPlanTargetService kpiPlanTargetService;

    @Resource
    private KpiPlanStaffService kpiPlanStaffService;

    @Resource
    private IInstitutionCenterClient institutionCenterClient;

    @Resource
    private IPermissionCenterClient permissionCenterClient;

    @Resource
    private UtilService utilService;

    @Resource
    private IStudentOfferItemService studentOfferItemService;

    @Resource
    private IStudentProjectRoleStaffService studentProjectRoleStaffService;

    @Resource(name = "saleTaskExecutor")
    private ThreadPoolTaskExecutor saleTaskExecutor;

    @Resource
    private IAgentService agentService;
    @Resource
    private IAgentStaffService agentStaffService;
    @Resource
    private AsyncExportService asyncExportService;
    @Resource
    private KpiPlanStaffLabelService kpiPlanStaffLabelService;
    @Resource
    private KpiPlanTaskResultService kpiPlanTaskResultService;
    @Resource
    @Lazy
    private GetRedis getRedis;
    @Resource
    private AsyncStatisticsService asyncStatisticsService;


    @Override
    public List<KpiPlanVo> datas(KpiPlanSearchDto kpiPlanSearchDto, Page page) {
        LambdaQueryWrapper<KpiPlan> wrapper = Wrappers.lambdaQuery();
        if (GeneralTool.isNotEmpty(kpiPlanSearchDto.getFkCompanyIds())) {
            //wrapper.eq(KpiPlan::getFkCompanyId, kpiPlanSearchDto.getFkCompanyId());
            String[] companyIds = kpiPlanSearchDto.getFkCompanyIds().split(",");
            wrapper.and(w -> {
                for (String id : companyIds) {
                    String trimmedId = id.trim();
                    w.or().apply("FIND_IN_SET({0}, fk_company_ids ) > 0", trimmedId);
                }
            });
        }
        if (GeneralTool.isNotEmpty(kpiPlanSearchDto.getIsEnableScheduledCount())) {
            wrapper.eq(KpiPlan::getIsEnableScheduledCount, kpiPlanSearchDto.getIsEnableScheduledCount());
        }

        if (GeneralTool.isNotEmpty(kpiPlanSearchDto.getKeyword())) {
            wrapper.and(w -> w.like(KpiPlan::getTitle, kpiPlanSearchDto.getKeyword())
                    .or()
                    .like(KpiPlan::getDescription, kpiPlanSearchDto.getKeyword())
            );
        }
        wrapper.orderByDesc(KpiPlan::getGmtCreate);
        IPage<KpiPlan> iPage = kpiPlanMapper.selectPage(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), wrapper);
        List<KpiPlan> kpiPlans = iPage.getRecords();
        if (GeneralTool.isEmpty(kpiPlans)) {
            return Collections.emptyList();
        }
        page.setAll((int) iPage.getTotal());
        List<KpiPlanVo> kpiPlanVos = BeanCopyUtils.copyListProperties(kpiPlans, KpiPlanVo::new);
        //Set<Long> fkCompanyIds = kpiPlanVos.stream().map(KpiPlanVo::getFkCompanyId).collect(Collectors.toSet());
        Set<Long> fkCompanyIds = kpiPlanVos.stream()
                .map(KpiPlanVo::getFkCompanyIds)
                .filter(Objects::nonNull)
                .flatMap(fkCompanyId -> Arrays.stream(fkCompanyId.split(",")))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .map(Long::valueOf)
                .collect(Collectors.toSet());

        Result<Map<Long, String>> result = permissionCenterClient.getCompanyNamesByIds(fkCompanyIds);
        Map<Long, String> companyNameMap = result.getData();

        Set<Long> kpiPlanIds = kpiPlanVos.stream().map(KpiPlanVo::getId).collect(Collectors.toSet());
        // 获取每个kpi方案下统计方式为【团队】的考核人员列表
        List<KpiPlanStaff> kpiPlanStaffTeams = kpiPlanStaffService.list(Wrappers.<KpiPlanStaff>lambdaQuery()
                .in(KpiPlanStaff::getFkKpiPlanId, kpiPlanIds)
                .eq(KpiPlanStaff::getCountMode, ProjectExtraEnum.TEAM.key));
        // key：kpi方案Id value：kpi方案下统计方式为【团队】的考核人员列表
        Map<Long, Set<Long>> teamStaffMap = Maps.newHashMap();
        if (GeneralTool.isNotEmpty(kpiPlanStaffTeams)) {
            teamStaffMap = kpiPlanStaffTeams.stream().collect(Collectors.groupingBy(
                    KpiPlanStaff::getFkKpiPlanId,
                    Collectors.mapping(
                            KpiPlanStaff::getFkStaffId,
                            Collectors.toSet()
                    )
            ));
        }
        // 获取每个KPI方案下，所有的设置人与被设置人id
        List<KpiPlanStaff> kpiPlanStaffs = kpiPlanStaffService.list(Wrappers.<KpiPlanStaff>lambdaQuery()
                .in(KpiPlanStaff::getFkKpiPlanId, kpiPlanIds));
        // key：kpi方案Id value：kpi方案下【设置人及被设置人及2者对应业务上司】
        Map<Long, Set<Long>> staffIdsMap = this.getIsKpiPlanStatistics(kpiPlanStaffs);

        for (KpiPlanVo kpiPlanDto : kpiPlanVos) {
            StringBuilder defaultTime = new StringBuilder();
            if (GeneralTool.isNotEmpty(kpiPlanDto.getStudentCreateTimeStart()) && GeneralTool.isNotEmpty(kpiPlanDto.getStudentCreateTimeEnd())) {
                defaultTime.append("学生创建时间：")
                        .append(DateUtil.formatDate(kpiPlanDto.getStudentCreateTimeStart()))
                        .append("至")
                        .append(DateUtil.formatDate(kpiPlanDto.getStudentCreateTimeEnd()));
            }
            if (GeneralTool.isNotEmpty(kpiPlanDto.getIntakeTimeStart()) && GeneralTool.isNotEmpty(kpiPlanDto.getIntakeTimeEnd())) {
                if (defaultTime.length() > 0) {
                    defaultTime.append("，");
                }
                defaultTime.append("入学时间：")
                        .append(DateUtil.formatDate(kpiPlanDto.getIntakeTimeStart()))
                        .append("至")
                        .append(DateUtil.formatDate(kpiPlanDto.getIntakeTimeEnd()));
            }

            if (GeneralTool.isNotEmpty(kpiPlanDto.getOfferItemCreateTimeStart()) && GeneralTool.isNotEmpty(kpiPlanDto.getOfferItemCreateTimeEnd())) {
                if (defaultTime.length() > 0) {
                    defaultTime.append("，");
                }
                defaultTime.append("申请创建时间：")
                        .append(DateUtil.formatDate(kpiPlanDto.getOfferItemCreateTimeStart()))
                        .append("至")
                        .append(DateUtil.formatDate(kpiPlanDto.getOfferItemCreateTimeEnd()));
            }
            if (GeneralTool.isNotEmpty(kpiPlanDto.getStepTimeStart()) && GeneralTool.isNotEmpty(kpiPlanDto.getStepTimeEnd())) {
                if (defaultTime.length() > 0) {
                    defaultTime.append("，");
                }
                defaultTime.append("业务步骤登记时间：")
                        .append(DateUtil.formatDate(kpiPlanDto.getStepTimeStart()))
                        .append("至")
                        .append(DateUtil.formatDate(kpiPlanDto.getStepTimeEnd()));
            }

            //kpiPlanDto.setFkCompanyName(companyNameMap.get(kpiPlanDto.getFkCompanyIds()));
            // 当前登录人有权限看到的公司ID列表
            List<Long> companyIdList = SecureUtil.getCompanyIds();

            // 设置公司名称
            String companyIdsStr = kpiPlanDto.getFkCompanyIds();
            if (StringUtils.isNotBlank(companyIdsStr)) {
                String joinedCompanyNames = Arrays.stream(companyIdsStr.split(","))
                        .map(String::trim)
                        .map(Long::parseLong) // 转换为Long
                        .filter(companyIdList::contains) // 只保留当前用户有权限的公司ID
                        .map(companyNameMap::get)
                        .filter(Objects::nonNull)
                        .collect(Collectors.joining(", "));
                kpiPlanDto.setFkCompanyName(joinedCompanyNames);
            } else {
                kpiPlanDto.setFkCompanyName("");
            }

            kpiPlanDto.setDefaultTime(defaultTime.toString());
            // 能进入新增考核人员配置的人员列表
            Set<Long> teamStaffIds = teamStaffMap.get(kpiPlanDto.getId());
            kpiPlanDto.setIsAddKpiPlanStaff(GeneralTool.isNotEmpty(teamStaffIds) && teamStaffIds.contains(SecureUtil.getStaffId()));
            // 能进入KPI方案统计的人员列表
            Set<Long> statisticsStaffIds = staffIdsMap.get(kpiPlanDto.getId());
            kpiPlanDto.setIsKpiPlanStatistics(GeneralTool.isNotEmpty(statisticsStaffIds) && statisticsStaffIds.contains(SecureUtil.getStaffId()));
        }
        return kpiPlanVos;
    }

    /**
     * 获取每个KPI方案下，能进入KPI方案统计的人员列表
     *
     * @param kpiPlanStaffs KPI方案列表
     * @return key：kpi方案Id  value：kpi方案下【设置人及被设置人及2者对应业务上司】
     */
    private Map<Long, Set<Long>> getIsKpiPlanStatistics(List<KpiPlanStaff> kpiPlanStaffs) {
        Map<Long, Set<Long>> staffIdsMap = Maps.newHashMap();
        if (GeneralTool.isNotEmpty(kpiPlanStaffs)) {
            // 设置人及被设置人id
            Set<Long> staffIds = kpiPlanStaffs.stream()
                    .flatMap(kpiPlanStaff -> Stream.of(kpiPlanStaff.getFkStaffId(), kpiPlanStaff.getFkStaffIdAdd()))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());
            // 获取每个员工的所有上司（直到最顶级）id集合
            Map<Long, Set<Long>> superiorMap = permissionCenterClient.getAllStaffSuperiorByStaffIds(staffIds).getData();
            Map<Long, List<KpiPlanStaff>> kpiPlanStaffMap = kpiPlanStaffs.stream().collect(Collectors.groupingBy(KpiPlanStaff::getFkKpiPlanId));
            for (Map.Entry<Long, List<KpiPlanStaff>> entry : kpiPlanStaffMap.entrySet()) {
                // 设置人及被设置人及2者对应业务上司 id集合
                Set<Long> staffIdSet = new HashSet<>();
                List<KpiPlanStaff> staffList = entry.getValue();
                for (KpiPlanStaff kpiPlanStaff : staffList) {
                    Long fkStaffId = kpiPlanStaff.getFkStaffId();
                    Long fkStaffIdAdd = kpiPlanStaff.getFkStaffIdAdd();
                    Set<Long> staffSuperiorIds = superiorMap.getOrDefault(fkStaffId, Collections.emptySet());
                    Set<Long> staffAddSuperiorIds = superiorMap.getOrDefault(fkStaffIdAdd, Collections.emptySet());
                    staffIdSet.add(fkStaffId);
                    staffIdSet.add(fkStaffIdAdd);
                    staffIdSet.addAll(staffSuperiorIds);
                    staffIdSet.addAll(staffAddSuperiorIds);
                }
                staffIdsMap.put(entry.getKey(), staffIdSet);
            }
        }
        return staffIdsMap;
    }

    @Override
    public KpiPlanVo findKpiPlanById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        KpiPlan kpiPlan = kpiPlanMapper.selectById(id);
        if (GeneralTool.isEmpty(kpiPlan)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        KpiPlanVo kpiPlanDto = BeanCopyUtils.objClone(kpiPlan, KpiPlanVo::new);

        // 1. 获取并处理公司ID字符串
        String companyIdsStr = kpiPlanDto.getFkCompanyIds();
        Set<Long> companyIdSet = Collections.emptySet();

// 2. 转换公司ID为Long集合
        if (StringUtils.isNotBlank(companyIdsStr)) {
            companyIdSet = Arrays.stream(companyIdsStr.split(","))
                    .map(String::trim)
                    .filter(s -> !s.isEmpty())  // 过滤空字符串
                    .map(Long::parseLong)
                    .collect(Collectors.toSet());
        }
// 当前登录人有权限看到的公司ID列表
        List<Long> companyIdList = SecureUtil.getCompanyIds();
// 3. 如果公司ID集合不为空，则查询公司名称
        if (!companyIdSet.isEmpty()) {
            Result<Map<Long, String>> result = permissionCenterClient.getCompanyNamesByIds(companyIdSet);

            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                Map<Long, String> companyNameMap = result.getData();

                // 4. 构建公司名称字符串
                String joinedCompanyNames = companyIdSet.stream()
                        .filter(companyIdList::contains)
                        .map(companyNameMap::get)
                        .filter(Objects::nonNull)
                        .collect(Collectors.joining(", "));
                kpiPlanDto.setFkCompanyName(joinedCompanyNames);
            } else {
                kpiPlanDto.setFkCompanyName("");  // 查询失败或数据为空时设为空字符串
            }
        } else {
            kpiPlanDto.setFkCompanyName("");  // 公司ID为空时设为空字符串
        }

        // 获取kpi方案下统计方式为【团队】的考核人员列表
        List<KpiPlanStaff> kpiPlanStaffTeams = kpiPlanStaffService.list(Wrappers.<KpiPlanStaff>lambdaQuery()
                .in(KpiPlanStaff::getFkKpiPlanId, id)
                .eq(KpiPlanStaff::getCountMode, ProjectExtraEnum.TEAM.key));
        // key：kpi方案Id value：kpi方案下统计方式为【团队】的考核人员列表
        Map<Long, Set<Long>> teamStaffMap = Maps.newHashMap();
        if (GeneralTool.isNotEmpty(kpiPlanStaffTeams)) {
            teamStaffMap = kpiPlanStaffTeams.stream().collect(Collectors.groupingBy(
                    KpiPlanStaff::getFkKpiPlanId,
                    Collectors.mapping(
                            KpiPlanStaff::getFkStaffId,
                            Collectors.toSet()
                    )
            ));
        }
        // 获取KPI方案下，所有的设置人与被设置人id
        List<KpiPlanStaff> kpiPlanStaffs = kpiPlanStaffService.list(Wrappers.<KpiPlanStaff>lambdaQuery()
                .in(KpiPlanStaff::getFkKpiPlanId, id));
        // key：kpi方案Id value：kpi方案下【设置人及被设置人及2者对应业务上司】
        Map<Long, Set<Long>> staffIdsMap = this.getIsKpiPlanStatistics(kpiPlanStaffs);

        // 能进入新增考核人员配置的人员列表
        Set<Long> teamStaffIds = teamStaffMap.get(id);
        kpiPlanDto.setIsAddKpiPlanStaff(GeneralTool.isNotEmpty(teamStaffIds) && teamStaffIds.contains(SecureUtil.getStaffId()));
        // 能进入KPI方案统计的人员列表
        Set<Long> statisticsStaffIds = staffIdsMap.get(id);
        kpiPlanDto.setIsKpiPlanStatistics(GeneralTool.isNotEmpty(statisticsStaffIds) && statisticsStaffIds.contains(SecureUtil.getStaffId()));
        return kpiPlanDto;
    }

    @Override
    public void exportKpiPlanStatisticsExcel(KpiPlanStatisticsExportDto  exportDto, HttpServletResponse response) {
        if (GeneralTool.isEmpty(exportDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        KpiPlanStatisticsDto kpiPlanStatisticsVo = BeanCopyUtils.objClone(exportDto, KpiPlanStatisticsDto::new);
        KpiPlanStatisticsVo kpiPlanStatistics = this.getKpiPlanTaskResult(kpiPlanStatisticsVo);

        // 统计表头
        List<KpiPlanStatisticsHeaderDto> kpiPlanStatisticsHeaderList = exportDto.getKpiPlanStatisticsHeaderList();
        // 组别小计的表头
//        List<KpiPlanStatisticsHeaderDto> kpiPlanGroupAllStatisticsHeaderList = exportVo.getKpiPlanGroupAllStatisticsHeaderList();
        Long rootFkStaffId = exportDto.getRootFkStaffId();
        int mergeNum = exportDto.getMergeNum();
        // 统计表头
        Map<String, String> itemHeaderMap = Maps.newLinkedHashMap();
        // 组别小计表头
        Map<String, String> groupHeaderMap = Maps.newLinkedHashMap();
        // 每列对应的单元格样式，是数字还是字符串
        Map<String, CellType> cellTypeMap = Maps.newLinkedHashMap();
        // 用于存储字体颜色
        Map<String, Short> colorMap = Maps.newLinkedHashMap();
        for (int i = 0; i < kpiPlanStatisticsHeaderList.size(); i++) {
            KpiPlanStatisticsHeaderDto kpiPlanStatisticsHeaderVo = kpiPlanStatisticsHeaderList.get(i);
            itemHeaderMap.put(kpiPlanStatisticsHeaderVo.getHeaderKey(), kpiPlanStatisticsHeaderVo.getHeaderName());
            if (i < mergeNum) { // 补单元格，用于合并
                groupHeaderMap.put(kpiPlanStatisticsHeaderVo.getHeaderKey(), "组别名称");
            } else {
                groupHeaderMap.put(kpiPlanStatisticsHeaderVo.getHeaderKey(), kpiPlanStatisticsHeaderVo.getHeaderName());
            }
            cellTypeMap.put(kpiPlanStatisticsHeaderVo.getHeaderKey(), CellType.valueOf(kpiPlanStatisticsHeaderVo.getColumnCellType().toUpperCase()));
            colorMap.put(kpiPlanStatisticsHeaderVo.getHeaderKey(), IndexedColors.BLACK.getIndex());
        }

//        // 每列对应的单元格样式，是数字还是字符串
//        Map<String, CellType> groupCellTypeMap = Maps.newLinkedHashMap();
//        // 用于存储字体颜色
//        Map<String, Short> groupColorMap = Maps.newLinkedHashMap();
//        for (KpiPlanStatisticsHeaderDto groupAllHeader : kpiPlanGroupAllStatisticsHeaderList) {
//            groupHeaderMap.put(groupAllHeader.getHeaderKey(), groupAllHeader.getHeaderName());
//            groupCellTypeMap.put(groupAllHeader.getHeaderKey(), CellType.valueOf(groupAllHeader.getColumnCellType().toUpperCase()));
//            groupColorMap.put(groupAllHeader.getHeaderKey(), IndexedColors.BLACK.getIndex());
//        }

        // 考核人员列表
        List<KpiPlanStaffVo> staffDtoList = kpiPlanStatistics.getKpiPlanStaffDtoList();
        if (GeneralTool.isNotEmpty(staffDtoList)) {
            for (KpiPlanStaffVo single : staffDtoList) {
                List<String> staffHeaderList = Lists.newArrayList();
                Long fkKpiPlanStaffId = single.getId();
                String staffName = single.getStaffName();
                String countModeName = single.getCountModeName();
                // 需要隐藏当前指定人员（团队）的成功入学数、进度 列
                if (Objects.equals(rootFkStaffId, single.getFkStaffId()) && ProjectExtraEnum.TEAM.key.equals(single.getCountMode())) {
                    staffHeaderList.add(staffName + "KPI");
                } else {
                    staffHeaderList.add(staffName + "KPI");
                    staffHeaderList.add("成功入学数");
                    staffHeaderList.add("【" + countModeName + "】" + staffName + "进度");
                }
                for (int i = 0; i < staffHeaderList.size(); i++) {
                    String key = fkKpiPlanStaffId + "_" + i;
                    itemHeaderMap.put(key, staffHeaderList.get(i));
                    groupHeaderMap.put(key, staffHeaderList.get(i));
                    cellTypeMap.put(key, CellType.NUMERIC);
                    colorMap.put(key, IndexedColors.BLACK.getIndex());

//                    groupCellTypeMap.put(key, CellType.NUMERIC);
//                    groupColorMap.put(key, IndexedColors.BLACK.getIndex());
                }
            }
        }

        // 创建工作薄
        HSSFWorkbook workbook = new HSSFWorkbook();
        // 创建sheet
        HSSFSheet sheet = workbook.createSheet("KpiPlanStatisticsExcel");
        // 自适应列宽
        FileUtils.setSizeColumn(sheet, itemHeaderMap.size());
        // 冻结第一行
        sheet.createFreezePane(0, 1, 0, 1);
        HSSFCellStyle headerStyle = workbook.createCellStyle();
        // 水平居中
        headerStyle.setAlignment(HorizontalAlignment.CENTER);
        // 垂直居中
        headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        // 设置边框
        headerStyle.setBorderTop(BorderStyle.THIN);
        headerStyle.setBorderBottom(BorderStyle.THIN);
        headerStyle.setBorderLeft(BorderStyle.THIN);
        headerStyle.setBorderRight(BorderStyle.THIN);
        // 自动换行
//        headerStyle.setWrapText(true);
        // 字体样式
        HSSFFont headerFont = workbook.createFont();
        headerFont.setFontName("微软雅黑");
        headerFont.setFontHeightInPoints((short) 14);
        headerStyle.setFont(headerFont);
        headerStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        // 记录表格的当前行
        int curRowIndex = 0;
        // 表头
        HSSFRow header = sheet.createRow(curRowIndex);
        HSSFCell headerCell;
        Iterator<Map.Entry<String, String>> itemHeaderIterator = itemHeaderMap.entrySet().iterator();
        int itemHeaderIndex = 0;
        while (itemHeaderIterator.hasNext()) {
            Map.Entry<String, String> next = itemHeaderIterator.next();
            headerCell = header.createCell(itemHeaderIndex++);
            headerCell.setCellStyle(headerStyle);
            headerCell.setCellType(CellType.STRING);
            headerCell.setCellValue(next.getValue());
        }

        // 组别行样式
        HSSFCellStyle groupItemStyle = workbook.createCellStyle();
        groupItemStyle.cloneStyleFrom(headerStyle);
        groupItemStyle.setAlignment(HorizontalAlignment.LEFT);

        HSSFCellStyle bodyStyle = workbook.createCellStyle();
        // 水平居中
        bodyStyle.setAlignment(HorizontalAlignment.LEFT);
        // 垂直居中
        bodyStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        // 设置边框
        bodyStyle.setBorderTop(BorderStyle.THIN);
        bodyStyle.setBorderBottom(BorderStyle.THIN);
        bodyStyle.setBorderLeft(BorderStyle.THIN);
        bodyStyle.setBorderRight(BorderStyle.THIN);
        // 自动换行
//        bodyStyle.setWrapText(true);

        // 用于存储对应表头的数据
        Map<String, Object> cellValueMap = Maps.newLinkedHashMap();
        int index = 1;
        // 统计数据
        List<KpiPlanGroupOrItemStatisticsVo> bodyDtoList = Lists.newArrayList();
        List<KpiPlanGroupOrItemStatisticsHeadVo> headDtoList = Lists.newArrayList();
        KpiPlanGroupOrItemStatisticsResultVo kpiPlanGroupOrItemStatisticsResultDto = kpiPlanStatistics.getKpiPlanGroupOrItemStatisticsResultDto();
        if (GeneralTool.isNotEmpty(kpiPlanGroupOrItemStatisticsResultDto)) {
            bodyDtoList = kpiPlanGroupOrItemStatisticsResultDto.getKpiPlanGroupOrItemStatisticsBodyDtoList();
            headDtoList = kpiPlanGroupOrItemStatisticsResultDto.getKpiPlanGroupOrItemStatisticsHeadDtoList();
        }
        for (KpiPlanGroupOrItemStatisticsVo item : bodyDtoList) {
            HSSFRow body = sheet.createRow(++curRowIndex);
            // 设置组别名称
            if (GeneralTool.isEmpty(item.getFkKpiPlanGroupId())) {
                // 每个序号从组别位置开始计算
                index = 1;
                HSSFCell cell = body.createCell(0);
                cell.setCellStyle(groupItemStyle);
                cell.setCellType(CellType.STRING);
                cell.setCellValue(item.getGroupName());
                // 合并整行单元格
                CellRangeAddress region = new CellRangeAddress(curRowIndex, curRowIndex, 0, itemHeaderMap.size() - 1);
                sheet.addMergedRegion(region);
                continue;
            }
            cellValueMap.clear();
            /**
             * 与前端约定的key值
             */
            // 序号
            cellValueMap.put("orderNum", index++);
            // 学校/集团
            cellValueMap.put("fkInstitutionProviderName", item.getFkInstitutionProviderName());
            // 课程等级
            cellValueMap.put("fkMajorLevelNames", item.getFkMajorLevelNames());
            // 国家/地区
            cellValueMap.put("fkAreaCountryNames", item.getFkAreaCountryNames());
            // 剔除学校
            cellValueMap.put("fkInstitutionIdsExcludingNames", item.getFkInstitutionIdsExcludingNames());
            // 时间设定
            cellValueMap.put("timeSet", item.getTimeSet());
            // 描述
            cellValueMap.put("description", item.getDescription());
            // KPI小计（目标）
            cellValueMap.put("targetEnrolled", item.getTargetEnrolled());
            // 考核小计
            cellValueMap.put("assessmentSubtotal", Integer.parseInt(item.getAssessmentSubtotal()));
            // 进度
            cellValueMap.put("allSchedule", getRatioValue(item.getAllSchedule()));
            // KPI总占比
            cellValueMap.put("kpiAllRatio", getRatioValue(item.getKpiAllRatio()));
            // 考核总占比
            cellValueMap.put("assessmentAllRatio", getRatioValue(item.getAssessmentAllRatio()));
            // 申请数
            cellValueMap.put("kpiApplicationCount", item.getKpiApplicationCount());
            // 学生数
            cellValueMap.put("kpiStudentCount", item.getKpiStudentCount());
            // 交押数
            cellValueMap.put("kpiConfirmationCount", item.getKpiConfirmationCount());
            // 代理数
            cellValueMap.put("kpiAgentCount", item.getKpiAgentCount());
            List<KpiPlanTargetStatisticsVo> kpiPlanTargetStatisticsDtoList = item.getKpiPlanTargetStatisticsDtoList();
            for (KpiPlanGroupOrItemStatisticsHeadVo headDto : headDtoList) {
                Long fkKpiPlanStaffId = headDto.getFkKpiPlanStaffId();
                Optional<KpiPlanTargetStatisticsVo> targetOptional = kpiPlanTargetStatisticsDtoList.stream()
                        // fkKpiPlanStaffId，唯一
                        .filter(k -> k.getFkKpiPlanStaffId().equals(headDto.getFkKpiPlanStaffId()))
                        .findFirst();
                if (targetOptional.isPresent()) {
                    KpiPlanTargetStatisticsVo targetDto = targetOptional.get();
                    cellValueMap.put(fkKpiPlanStaffId + "_0", targetDto.getTargetEnrolled());
                    cellValueMap.put(fkKpiPlanStaffId + "_1", targetDto.getSuccessCount());
                    cellValueMap.put(fkKpiPlanStaffId + "_2", getRatioValue(targetDto.getSchedule()));
                } else {
                    cellValueMap.put(fkKpiPlanStaffId + "_0", 0);
                    cellValueMap.put(fkKpiPlanStaffId + "_1", 0);
                    cellValueMap.put(fkKpiPlanStaffId + "_2", new BigDecimal("0.00"));
                }
            }
            // 设置单元格样式和值
            setCellValue(workbook, body, bodyStyle, staffDtoList, colorMap, cellTypeMap, cellValueMap);
        }

        //总计
        KpiPlanAllStatisticsVo kpiPlanAllStatisticsDto = kpiPlanStatistics.getKpiPlanAllStatisticsDto();
        if (GeneralTool.isNotEmpty(kpiPlanAllStatisticsDto)) {
            HSSFRow allTotalRow = sheet.createRow(++curRowIndex);
            cellValueMap.clear();
            cellValueMap.put("orderNum", "总计");
            // KPI小计（目标）
            cellValueMap.put("targetEnrolled", Integer.parseInt(kpiPlanAllStatisticsDto.getKpiAllTotal()));
            // 考核小计
            cellValueMap.put("assessmentSubtotal", Integer.parseInt(kpiPlanAllStatisticsDto.getAssessmentAllTotal()));
            // 进度
            cellValueMap.put("allSchedule", getRatioValue(kpiPlanAllStatisticsDto.getAllScheduleTotal()));
            // KPI总占比
            cellValueMap.put("kpiAllRatio", getRatioValue(kpiPlanAllStatisticsDto.getKpiAllRatioTotal()));
            // 考核总占比
            cellValueMap.put("assessmentAllRatio", getRatioValue(kpiPlanAllStatisticsDto.getAssessmentAllRatioTotal()));
            // 申请数
            cellValueMap.put("kpiApplicationCount", Integer.parseInt(kpiPlanAllStatisticsDto.getApplicationCountTotal()));
            // 学生数
            cellValueMap.put("kpiStudentCount", Integer.parseInt(kpiPlanAllStatisticsDto.getStudentCountTotal()));
            // 交押数
            cellValueMap.put("kpiConfirmationCount", Integer.parseInt(kpiPlanAllStatisticsDto.getConfirmationCountTotal()));
            // 代理数
            cellValueMap.put("kpiAgentCount", Integer.parseInt(kpiPlanAllStatisticsDto.getAgentCountTotal()));
            List<KpiPlanAllTargetStatisticsVo> kpiPlanAllTargetStatisticsDtoList = kpiPlanAllStatisticsDto.getKpiPlanAllTargetStatisticsDtoList();
            for (KpiPlanGroupOrItemStatisticsHeadVo headDto : headDtoList) {
                Long fkKpiPlanStaffId = headDto.getFkKpiPlanStaffId();
                Optional<KpiPlanAllTargetStatisticsVo> targetOptional = kpiPlanAllTargetStatisticsDtoList.stream()
                        // fkKpiPlanStaffId，唯一
                        .filter(k -> k.getFkKpiPlanStaffId().equals(headDto.getFkKpiPlanStaffId()))
                        .findFirst();
                if (targetOptional.isPresent()) {
                    KpiPlanAllTargetStatisticsVo targetDto = targetOptional.get();
                    cellValueMap.put(fkKpiPlanStaffId + "_0", targetDto.getTargetEnrolled());
                    cellValueMap.put(fkKpiPlanStaffId + "_1", targetDto.getSuccessCount());
                    cellValueMap.put(fkKpiPlanStaffId + "_2", getRatioValue(targetDto.getSchedule()));
                } else {
                    cellValueMap.put(fkKpiPlanStaffId + "_0", 0);
                    cellValueMap.put(fkKpiPlanStaffId + "_1", 0);
                    cellValueMap.put(fkKpiPlanStaffId + "_2", new BigDecimal("0.00"));
                }
            }
            setCellValue(workbook, allTotalRow, groupItemStyle, staffDtoList, colorMap, cellTypeMap, cellValueMap);
            // 单元格合并
            CellRangeAddress region = new CellRangeAddress(curRowIndex, curRowIndex, 0, mergeNum - 1);
            sheet.addMergedRegion(region);
        }

        // 空行
        sheet.createRow(++curRowIndex);

        // 组别的小计
        // 表头
        HSSFRow groupHeaderRow = sheet.createRow(++curRowIndex);
        HSSFCell groupHeaderCell;
        Iterator<Map.Entry<String, String>> groupHeaderIterator = groupHeaderMap.entrySet().iterator();
        int groupHeaderIndex = 0;
        while (groupHeaderIterator.hasNext()) {
            Map.Entry<String, String> next = groupHeaderIterator.next();
            groupHeaderCell = groupHeaderRow.createCell(groupHeaderIndex++);
            groupHeaderCell.setCellStyle(headerStyle);
            groupHeaderCell.setCellType(CellType.STRING);
            groupHeaderCell.setCellValue(next.getValue());
        }
        // 单元格合并
        CellRangeAddress groupHeaderRegion = new CellRangeAddress(curRowIndex, curRowIndex, 0, mergeNum - 1);
        sheet.addMergedRegion(groupHeaderRegion);
        // 表体
        List<KpiPlanGroupAllStatisticsVo> kpiPlanGroupAllStatisticsDtos = kpiPlanStatistics.getKpiPlanGroupAllStatisticsDtos();
        if (GeneralTool.isNotEmpty(kpiPlanGroupAllStatisticsDtos)) {
            for (KpiPlanGroupAllStatisticsVo groupAllStatisticsDto : kpiPlanGroupAllStatisticsDtos) {
                HSSFRow groupBody = sheet.createRow(++curRowIndex);
                cellValueMap.clear();
                cellValueMap.put("orderNum", groupAllStatisticsDto.getGroupName());
                // KPI小计（目标）
                cellValueMap.put("targetEnrolled", Integer.parseInt(groupAllStatisticsDto.getKpiAllTotal()));
                // 考核小计
                cellValueMap.put("assessmentSubtotal", Integer.parseInt(groupAllStatisticsDto.getAssessmentAllTotal()));
                // 进度
                cellValueMap.put("allSchedule", getRatioValue(groupAllStatisticsDto.getAllScheduleTotal()));
                // KPI总占比
                cellValueMap.put("kpiAllRatio", getRatioValue(groupAllStatisticsDto.getKpiAllRatioTotal()));
                // 考核总占比
                cellValueMap.put("assessmentAllRatio", getRatioValue(groupAllStatisticsDto.getAssessmentAllRatioTotal()));
                // 申请数
                cellValueMap.put("kpiApplicationCount", Integer.parseInt(groupAllStatisticsDto.getApplicationCountTotal()));
                // 学生数
                cellValueMap.put("kpiStudentCount", Integer.parseInt(groupAllStatisticsDto.getStudentCountTotal()));
                // 交押数
                cellValueMap.put("kpiConfirmationCount", Integer.parseInt(groupAllStatisticsDto.getConfirmationCountTotal()));
                // 代理数
                cellValueMap.put("kpiAgentCount", Integer.parseInt(groupAllStatisticsDto.getAgentCountTotal()));
                List<KpiPlanAllTargetStatisticsVo> groupTargetStatisticsDtos = groupAllStatisticsDto.getKpiPlanAllTargetStatisticsDtoList();
                for (KpiPlanGroupOrItemStatisticsHeadVo headDto : headDtoList) {
                    Long fkKpiPlanStaffId = headDto.getFkKpiPlanStaffId();
                    Optional<KpiPlanAllTargetStatisticsVo> groupTargetOptional = groupTargetStatisticsDtos.stream()
                            // fkKpiPlanStaffId，唯一
                            .filter(k -> k.getFkKpiPlanStaffId().equals(headDto.getFkKpiPlanStaffId()))
                            .findFirst();
                    if (groupTargetOptional.isPresent()) {
                        KpiPlanAllTargetStatisticsVo targetDto = groupTargetOptional.get();
                        cellValueMap.put(fkKpiPlanStaffId + "_0", targetDto.getTargetEnrolled());
                        cellValueMap.put(fkKpiPlanStaffId + "_1", targetDto.getSuccessCount());
                        cellValueMap.put(fkKpiPlanStaffId + "_2", getRatioValue(targetDto.getSchedule()));
                    } else {
                        cellValueMap.put(fkKpiPlanStaffId + "_0", 0);
                        cellValueMap.put(fkKpiPlanStaffId + "_1", 0);
                        cellValueMap.put(fkKpiPlanStaffId + "_2", new BigDecimal("0.00"));
                    }
                }
                setCellValue(workbook, groupBody, bodyStyle, staffDtoList, colorMap, cellTypeMap, cellValueMap);
                // 单元格合并
                CellRangeAddress region = new CellRangeAddress(curRowIndex, curRowIndex, 0, mergeNum - 1);
                sheet.addMergedRegion(region);
            }
        }

        String fileName = "KpiPlanStatisticsExcel";
        response.setContentType("application/vnd.ms-excel;charset=utf-8");
        response.setHeader("Content-Disposition", "attachment;filename=" + fileName + ".xlsx");
        ServletOutputStream out = null;
        try {
            out = response.getOutputStream();
            workbook.write(out);
            out.flush();
        } catch (IOException e) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("kpiplan_statistics_excel_export_fail"));
        } finally {
            IoUtil.close(out);
        }
    }

    /**
     * 设置每一行单元格的值和样式
     *
     * @param workbook     表格对象
     * @param row          当前行对象
     * @param cellStyle    单元格样式
     * @param staffDtoList 考核人员列表
     * @param colorMap     对应表头每列的字体颜色
     * @param cellTypeMap  对应表头每个单元格的类型
     * @param cellValueMap 对应表头每个单元格的值
     */
    private void setCellValue(HSSFWorkbook workbook, HSSFRow row, HSSFCellStyle cellStyle, List<KpiPlanStaffVo> staffDtoList,
                              Map<String, Short> colorMap, Map<String, CellType> cellTypeMap, Map<String, Object> cellValueMap) {
        // 考核小计 下标：8 考核小计<KPI小计
        if (((Integer) cellValueMap.get("assessmentSubtotal")).compareTo((Integer) cellValueMap.get("targetEnrolled")) < 0) {
            colorMap.put("assessmentSubtotal", IndexedColors.RED.getIndex());
        } else {
            colorMap.put("assessmentSubtotal", IndexedColors.GREEN.getIndex());
        }

        // 存储背景颜色
        Map<String, Short> staffGroundColorMap = Maps.newLinkedHashMap();
        if (GeneralTool.isNotEmpty(staffDtoList)) {
            for (int i = 0; i < staffDtoList.size(); i++) {
                KpiPlanStaffVo kpiPlanStaffDto = staffDtoList.get(i);
                String fkKpiPlanStaffId1 = kpiPlanStaffDto.getId() + "_0";
                String fkKpiPlanStaffId2 = kpiPlanStaffDto.getId() + "_1";
                String fkKpiPlanStaffId3 = kpiPlanStaffDto.getId() + "_2";
                // 存储考核人员列下标的背景颜色
                if (i % 2 == 0) {
                    staffGroundColorMap.put(fkKpiPlanStaffId1, IndexedColors.PALE_BLUE.getIndex());
                    staffGroundColorMap.put(fkKpiPlanStaffId2, IndexedColors.PALE_BLUE.getIndex());
                    staffGroundColorMap.put(fkKpiPlanStaffId3, IndexedColors.PALE_BLUE.getIndex());
                } else {
                    staffGroundColorMap.put(fkKpiPlanStaffId1, IndexedColors.LEMON_CHIFFON.getIndex());
                    staffGroundColorMap.put(fkKpiPlanStaffId2, IndexedColors.LEMON_CHIFFON.getIndex());
                    staffGroundColorMap.put(fkKpiPlanStaffId3, IndexedColors.LEMON_CHIFFON.getIndex());
                }
                // 设置考核人员成功入学数列的字体颜色
                if (((Integer) cellValueMap.get(fkKpiPlanStaffId2)).compareTo((Integer) cellValueMap.get(fkKpiPlanStaffId1)) < 0) {
                    colorMap.put(fkKpiPlanStaffId2, IndexedColors.RED.getIndex());
                } else {
                    colorMap.put(fkKpiPlanStaffId2, IndexedColors.GREEN.getIndex());
                }
            }
        }

        Set<String> groundColorIndex = staffGroundColorMap.keySet();

        Iterator<Map.Entry<String, CellType>> cellTypeIterator = cellTypeMap.entrySet().iterator();
        int index = 0;
        while (cellTypeIterator.hasNext()) {
            Map.Entry<String, CellType> next = cellTypeIterator.next();
            String key = next.getKey();
            CellType cellType = next.getValue();
            HSSFCell cell = row.createCell(index++);

            // 设置字体颜色
            CellStyle newStyle = workbook.createCellStyle();
            newStyle.cloneStyleFrom(cellStyle);

            // 设置考核人员的背景颜色，跳过总计
            if (groundColorIndex.contains(key)) {
                newStyle.setFillForegroundColor(staffGroundColorMap.get(key));
                newStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            }

            HSSFFont font = workbook.createFont();
            font.setFontName("微软雅黑");
            font.setFontHeightInPoints((short) 12);
            font.setColor(colorMap.get(key));
            newStyle.setFont(font);

            cell.setCellType(cellType);
            Object obj = cellValueMap.get(key);
            // 根据不同类型进行处理，不然单元格样式不会生效
            if (obj instanceof Integer) {
                // 水平居中
                newStyle.setAlignment(HorizontalAlignment.CENTER);
                // 垂直居中
                newStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                cell.setCellValue((Integer) obj);
            } else if (obj instanceof String) {
                cell.setCellValue((String) obj);
            } else if (obj instanceof BigDecimal) {
                // 水平居中
                newStyle.setAlignment(HorizontalAlignment.CENTER);
                // 垂直居中
                newStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                DataFormat dataFormat = workbook.createDataFormat();
                newStyle.setDataFormat(dataFormat.getFormat("0.00%"));

                short ratioColor;
                if (((BigDecimal) obj).compareTo(BigDecimal.ONE) < 0) {
                    ratioColor = IndexedColors.RED.getIndex();
                } else {
                    ratioColor = IndexedColors.GREEN.getIndex();
                }
                // 设置字体颜色
                HSSFFont newFont = workbook.createFont();
                newFont.setFontName("微软雅黑");
                newFont.setFontHeightInPoints((short) 12);
                newFont.setColor(ratioColor);
                newStyle.setFont(newFont);
                cell.setCellValue(((BigDecimal) obj).doubleValue());
            }
            cell.setCellStyle(newStyle);
        }
    }

    /**
     * 还原百分比的值
     *
     * @param ratio 百分比
     * @return
     */
    private BigDecimal getRatioValue(String ratio) {
        BigDecimal result = new BigDecimal("0.00");
        if (!"-".equals(ratio)) {
            result = new BigDecimal(ratio.substring(0, ratio.length() - 1))
                    .divide(new BigDecimal("100"))
                    .setScale(4, RoundingMode.HALF_UP);
        }
        return result;
    }

    /**
     * 设置字体样式
     *
     * <AUTHOR>
     * @DateTime 2024/4/24 17:39
     */
    public void setFontStyle(BigExcelWriter writer, int x, int y) {
        CellStyle cellStyle = writer.createCellStyle(x, y);
        Font font = writer.createFont();
        //加粗
        font.setBold(true);
        //字体大小
        font.setFontHeightInPoints((short) 18);
        cellStyle.setFont(font);
        //居左
        cellStyle.setAlignment(HorizontalAlignment.LEFT);
        // 设置边框
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBorderLeft(BorderStyle.THIN);
        cellStyle.setBorderRight(BorderStyle.THIN);
    }

    /**
     * 将统计后的结果插入结果表中
     *
     * @param kpiPlanStatisticsVo 参数
     * @return
     */
    @Override
    public void addKpiPlanTaskResult(KpiPlanStatisticsDto kpiPlanStatisticsVo) {
        if (GeneralTool.isEmpty(kpiPlanStatisticsVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        Long fkKpiPlanId = kpiPlanStatisticsVo.getFkKpiPlanId();
        List<KpiPlan> kpiPlanList = kpiPlanMapper.selectBatchIds(Collections.singleton(fkKpiPlanId));
        String key = CacheKeyConstants.KPI_PLAN_STATISTICS_KEY + fkKpiPlanId;
        Boolean flag = getRedis.setNx(key, 1, 3600L);
        if (flag) {
            asyncStatisticsService.batchAddKpiPlanTaskResult(kpiPlanList);
        }
//        KpiPlanTaskResult kpiPlanTaskResult = new KpiPlanTaskResult();
//        try {
//            kpiPlanTaskResult.setFkKpiPlanId(kpiPlanStatisticsVo.getFkKpiPlanId());
//            kpiPlanTaskResult.setRootFkStaffId(kpiPlanStatisticsVo.getRootFkStaffId());
//            kpiPlanTaskResult.setTaskStartTime(Date.from(LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant()));
//            // KPI方案统计
//            KpiPlanStatisticsDto kpiPlanStatistics = this.getKpiPlanStatistics(kpiPlanStatisticsVo);
//            kpiPlanTaskResult.setTaskEndTime(Date.from(LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant()));
//            kpiPlanTaskResult.setKpiStatisticalResult(objectMapper.writeValueAsString(kpiPlanStatistics));
//            utilService.setCreateInfo(kpiPlanTaskResult);
//
//            kpiPlanTaskResultService.save(kpiPlanTaskResult);
//        } catch (Exception e) {
//            e.printStackTrace();
//            throw new GetServiceException(e.getMessage());
//        }
//        return BeanCopyUtils.objClone(kpiPlanTaskResult, KpiPlanTaskResultDto::new);
    }

    /**
     * 获取KPI方案统计结果
     *
     * @param kpiPlanStatisticsVo 参数
     * @return
     */
    @Override
    public KpiPlanStatisticsVo getKpiPlanTaskResult(KpiPlanStatisticsDto kpiPlanStatisticsVo) {
        if (GeneralTool.isEmpty(kpiPlanStatisticsVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        KpiPlanStatisticsVo kpiPlanStatisticsDto = new KpiPlanStatisticsVo();
        // 取最新的统计结果
        KpiPlanTaskResult result = kpiPlanTaskResultService.getOne(Wrappers.<KpiPlanTaskResult>lambdaQuery()
                .eq(KpiPlanTaskResult::getFkKpiPlanId, kpiPlanStatisticsVo.getFkKpiPlanId())
                .eq(KpiPlanTaskResult::getFkStaffIdRoot, kpiPlanStatisticsVo.getRootFkStaffId())
                .orderByDesc(KpiPlanTaskResult::getTaskEndTime)
                .last("limit 1"));
        if (GeneralTool.isEmpty(result)) {
            return kpiPlanStatisticsDto;
        }
        String kpiStatisticalResult = result.getKpiStatisticalResult();
        Date taskEndTime = result.getTaskEndTime();
        try {
            kpiPlanStatisticsDto = objectMapper.readValue(kpiStatisticalResult, KpiPlanStatisticsVo.class);
            kpiPlanStatisticsDto.setTaskEndTime(taskEndTime);
        } catch (Exception e) {
            e.printStackTrace();
            throw new GetServiceException(e.getMessage());
        }
        return kpiPlanStatisticsDto;
    }

    /**
     * KPI方案统计
     *
     * @param kpiPlanStatisticsVo 参数
     * @return
     */
    @Override
    public KpiPlanStatisticsVo getKpiPlanStatistics(KpiPlanStatisticsDto kpiPlanStatisticsVo) {
        if (GeneralTool.isEmpty(kpiPlanStatisticsVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage(null, "search_vo_null"));
        }
        Long fkKpiPlanId = kpiPlanStatisticsVo.getFkKpiPlanId();
        // 统计结果
        KpiPlanStatisticsVo kpiPlanStatisticsDto = new KpiPlanStatisticsVo();

        // 获取KPI方案数据时间戳之和
        String sumTimeKpiData = kpiPlanMapper.getSumTimeKpiData(fkKpiPlanId);
        kpiPlanStatisticsDto.setSumTimeKpiData(sumTimeKpiData);

        // 查询KPI方案信息
        KpiPlan kpiPlan = kpiPlanMapper.selectById(fkKpiPlanId);
        if (GeneralTool.isEmpty(kpiPlan)) {
            return kpiPlanStatisticsDto;
        }

        // 查询KPI方案下所有组别
        List<KpiPlanGroup> kpiPlanGroups = kpiPlanGroupService.list(Wrappers.<KpiPlanGroup>lambdaQuery()
                .eq(KpiPlanGroup::getFkKpiPlanId, fkKpiPlanId)
                .orderByDesc(KpiPlanGroup::getViewOrder));
        if (GeneralTool.isEmpty(kpiPlanGroups)) {
            return kpiPlanStatisticsDto;
        }

        // 查询组别下所有子项明细
        Set<Long> groupIds = kpiPlanGroups.stream().map(KpiPlanGroup::getId).collect(Collectors.toSet());
        List<KpiPlanGroupItem> groupItemList = Lists.newArrayList();
        if (GeneralTool.isNotEmpty(groupIds)) {
            groupItemList = kpiPlanGroupItemService.list(Wrappers.<KpiPlanGroupItem>lambdaQuery()
                    .in(KpiPlanGroupItem::getFkKpiPlanGroupId, groupIds)
                    .orderByDesc(KpiPlanGroupItem::getViewOrder));
        }

        // 指定维度人员
        Long rootFkStaffId = kpiPlanStatisticsVo.getRootFkStaffId();

        // 返回当前指定人员的目标设置人Id，用于前端页面的跳转
        KpiPlanStaff addStaff = kpiPlanStaffService.getOne(Wrappers.<KpiPlanStaff>lambdaQuery()
                .select(KpiPlanStaff::getFkStaffIdAdd)
                .eq(KpiPlanStaff::getFkKpiPlanId, fkKpiPlanId)
                .eq(KpiPlanStaff::getCountMode, ProjectExtraEnum.TEAM.key)
                .eq(KpiPlanStaff::getFkStaffId, rootFkStaffId)
                .last("limit 1"));
        if (GeneralTool.isNotEmpty(addStaff)) {
            kpiPlanStatisticsDto.setFkStaffIdAdd(addStaff.getFkStaffIdAdd());
        }

        // 指定人员的业务下属
        Set<Long> staffFollowerIds = Sets.newHashSet();
        Result<List<Long>> followerIdsResult = permissionCenterClient.getStaffFollowerIds(rootFkStaffId);
        if (followerIdsResult.isSuccess() && GeneralTool.isNotEmpty(followerIdsResult.getData())) {
            List<Long> followerIds = followerIdsResult.getData();
            staffFollowerIds.addAll(followerIds);
        }
        staffFollowerIds.removeIf(Objects::isNull);
        // 获取考核人员列表（指定人员添加的考核人员）
        List<KpiPlanStaff> staffList = kpiPlanStaffService.list(Wrappers.<KpiPlanStaff>lambdaQuery()
                .eq(KpiPlanStaff::getFkKpiPlanId, fkKpiPlanId)
                .and(wrapper -> wrapper
                        // 指定人员添加的考核人员
                        .or(w1 -> w1.eq(KpiPlanStaff::getFkStaffIdAdd, rootFkStaffId))
                        // 指定人员自己（团队）
                        .or(w2 -> w2.eq(KpiPlanStaff::getFkStaffId, rootFkStaffId)
                                .eq(KpiPlanStaff::getCountMode, ProjectExtraEnum.TEAM.key))
                 )
                .orderByDesc(KpiPlanStaff::getCountMode)
                .orderByDesc(KpiPlanStaff::getViewOrder));

        if (GeneralTool.isNotEmpty(staffList)) { // 能找到考核人员，过滤组别明细
            /**
             * 过滤组别明细
             */
            if (GeneralTool.isNotEmpty(groupItemList)) {
                Set<Long> groupItemIds = groupItemList.stream().map(KpiPlanGroupItem::getId).collect(Collectors.toSet());
                // 查询当前指定人员（团队）是否有KPI目标值
                List<KpiPlanStaff> kpiPlanStaffs = kpiPlanStaffService.list(Wrappers.<KpiPlanStaff>lambdaQuery()
                        .eq(KpiPlanStaff::getFkKpiPlanId, fkKpiPlanId)
                        .eq(KpiPlanStaff::getFkStaffId, rootFkStaffId)
                        .eq(KpiPlanStaff::getCountMode, ProjectExtraEnum.TEAM.key));
                List<KpiPlanTarget> kpiPlanTargets = Lists.newArrayList();
                if (GeneralTool.isNotEmpty(kpiPlanStaffs)) {
                    Set<Long> kpiPlanStaffIds = kpiPlanStaffs.stream().map(KpiPlanStaff::getId).collect(Collectors.toSet());
                    kpiPlanTargets = kpiPlanTargetService.list(Wrappers.<KpiPlanTarget>lambdaQuery()
                            .in(KpiPlanTarget::getFkKpiPlanGroupItemId, groupItemIds)
                            .in(KpiPlanTarget::getFkKpiPlanStaffId, kpiPlanStaffIds)
                            .ne(KpiPlanTarget::getTargetEnrolled, 0));
                }

                if (GeneralTool.isNotEmpty(kpiPlanTargets)) { // 当前指定人员（团队）有KPI目标值时才去做组别明细的过滤
                    // 需要显示的组别明细id
                    Set<Long> showGroupItemIds = kpiPlanTargets.stream().map(KpiPlanTarget::getFkKpiPlanGroupItemId).collect(Collectors.toSet());
                    // 剔除不需要显示的组别明细
                    groupItemList = groupItemList.stream().filter(item -> showGroupItemIds.contains(item.getId())).collect(Collectors.toList());
                    // 剔除没有显示组别明细的组别
                    Set<Long> showGroupIds = groupItemList.stream().map(KpiPlanGroupItem::getFkKpiPlanGroupId).collect(Collectors.toSet());
                    kpiPlanGroups = kpiPlanGroups.stream().filter(group -> showGroupIds.contains(group.getId())).collect(Collectors.toList());
                    if (GeneralTool.isEmpty(kpiPlanGroups)) {
                        return kpiPlanStatisticsDto;
                    }
                }
            }
        } else { // 没有找到考核人员，返回考核人员树最低层级的考核人员列表
            staffList = this.getMinLevelStaffs(fkKpiPlanId, staffFollowerIds);
        }

        // 组装考核人员的相关信息
        List<KpiPlanStaffVo> kpiPlanStaffDtoList = Lists.newArrayList();
        if (GeneralTool.isNotEmpty(staffList)) {
            // 排序
            staffList = staffList.stream()
                    .sorted(Comparator.comparing(KpiPlanStaff::getCountMode, Comparator.reverseOrder())
                            .thenComparing(KpiPlanStaff::getViewOrder, Comparator.reverseOrder()))
                    .collect(Collectors.toList());
            // 找到指定人员（团队）
            Optional<KpiPlanStaff> specificStaff = staffList.stream()
                    .filter(staff -> Objects.equals(staff.getFkStaffId(), rootFkStaffId)
                            && Objects.equals(staff.getCountMode(), ProjectExtraEnum.TEAM.key))
                    .findFirst();
            // 如果找到了找到指定人员（团队），则将其移动到第一个位置
            if (specificStaff.isPresent()) {
                KpiPlanStaff targetStaff = specificStaff.get();
                staffList.remove(targetStaff);
                staffList.add(0, targetStaff);
            }

            Set<Long> fkStaffIds = staffList.stream().map(KpiPlanStaff::getFkStaffId).collect(Collectors.toSet());
            // 获取考核人员名称
            Map<Long, String> staffNamesMap = permissionCenterClient.getStaffChnNameByIds(fkStaffIds);
            Set<Long> kpiPlanStaffIds = staffList.stream().map(KpiPlanStaff::getId).collect(Collectors.toSet());
            // 获取考核人员标签
            List<KpiPlanStaffLabel> kpiPlanStaffLabelList = kpiPlanStaffLabelService.list(Wrappers.<KpiPlanStaffLabel>lambdaQuery()
                    .in(KpiPlanStaffLabel::getFkKpiPlanStaffId, kpiPlanStaffIds));
            Map<Long, List<KpiPlanStaffLabelVo>> staffLabelDtoMap = Maps.newHashMap();
            if (GeneralTool.isNotEmpty(kpiPlanStaffLabelList)) {
                List<KpiPlanStaffLabelVo> kpiPlanStaffLabelVos = BeanCopyUtils.copyListProperties(kpiPlanStaffLabelList, KpiPlanStaffLabelVo::new);
                // 按KPI方案考核人员Id分组
                staffLabelDtoMap = kpiPlanStaffLabelVos.stream().collect(Collectors.groupingBy(KpiPlanStaffLabelVo::getFkKpiPlanStaffId));
            }
            for (KpiPlanStaff kpiPlanStaff : staffList) {
                KpiPlanStaffVo kpiPlanStaffDto = BeanCopyUtils.objClone(kpiPlanStaff, KpiPlanStaffVo::new);
                kpiPlanStaffDto.setStaffName(staffNamesMap.get(kpiPlanStaff.getFkStaffId()));
                kpiPlanStaffDto.setCountRoleName(ProjectExtraEnum.getInitialValueByKey(kpiPlanStaffDto.getCountRole(), ProjectExtraEnum.COUNT_ROLE));
                kpiPlanStaffDto.setCountModeName(ProjectExtraEnum.getInitialValueByKey(kpiPlanStaffDto.getCountMode(), ProjectExtraEnum.COUNT_MODE));
                kpiPlanStaffDto.setKpiPlanStaffLabelDtoList(staffLabelDtoMap.get(kpiPlanStaffDto.getId()));
                kpiPlanStaffDtoList.add(kpiPlanStaffDto);
            }
            kpiPlanStatisticsDto.setKpiPlanStaffDtoList(kpiPlanStaffDtoList);
        }

        Set<Long> fkInstitutionIds = Sets.newHashSet();
        // 当前kpi方案下（提供商维度），根据公司id、提供商获取它下面对应的所有学校
        Set<Long> fkInstitutionProviderIds = groupItemList.stream()
                .map(KpiPlanGroupItem::getFkInstitutionProviderId)
                .filter(Objects::nonNull).collect(Collectors.toSet());

        // 1. 获取并处理公司ID字符串
        String companyIdsStr = kpiPlan.getFkCompanyIds();
        Set<Long> companyIdSet = Collections.emptySet();

// 2. 转换公司ID为Long集合
        if (StringUtils.isNotBlank(companyIdsStr)) {
            companyIdSet = Arrays.stream(companyIdsStr.split(","))
                    .map(String::trim)
                    .filter(s -> !s.isEmpty())  // 过滤空字符串
                    .map(Long::parseLong)
                    .collect(Collectors.toSet());
        }
        Result<List<InstitutionProviderInstitutionVo>> institutionByProvider = institutionCenterClient.getInstitutionByProvider(companyIdSet, fkInstitutionProviderIds);
        if (institutionByProvider.isSuccess() && GeneralTool.isNotEmpty(institutionByProvider.getData())) {
            List<InstitutionProviderInstitutionVo> data = institutionByProvider.getData();
            Set<Long> institutionIdsByProvider = data.stream().map(InstitutionProviderInstitutionVo::getFkInstitutionId).collect(Collectors.toSet());
            fkInstitutionIds.addAll(institutionIdsByProvider);
        }
        // 增加国家维度的统计，根据公司id、国家获取它下面对应的所有学校
        Set<Long> fkAreaCountryIds = groupItemList.stream()
                .map(KpiPlanGroupItem::getFkAreaCountryIdsKpi)  // 此时返回的是String（如"1,2,3"）
                .filter(Objects::nonNull)  // 过滤null值
                .flatMap(idsStr -> Arrays.stream(idsStr.split(",")))  // 拆分字符串
                .map(String::trim)  // 去除空格
                .filter(s -> !s.isEmpty())  // 过滤空字符串
                .map(Long::parseLong)  // 转为Long
                .collect(Collectors.toSet());  // 收集为Set
//        Set<Long> fkAreaCountryIds = groupItemList.stream()
//                .map(KpiPlanGroupItem::getFkAreaCountryIdKpi)
//                .filter(Objects::nonNull).collect(Collectors.toSet());
        Result<List<InstitutionVo>> institutionByCountry = institutionCenterClient.getInstitutionByCountryIds(fkAreaCountryIds);
        if (institutionByCountry.isSuccess() && GeneralTool.isNotEmpty(institutionByCountry.getData())) {
            List<InstitutionVo> data = institutionByCountry.getData();
            Set<Long> institutionIdsByCountry = data.stream().map(InstitutionVo::getId).collect(Collectors.toSet());
            fkInstitutionIds.addAll(institutionIdsByCountry);
        }

        // 获取相关学习计划
        KpiPlanStudentOfferItemListDto vo = BeanCopyUtils.objClone(kpiPlan, KpiPlanStudentOfferItemListDto::new);
        vo.setFkInstitutionIds(fkInstitutionIds);
        List<Long> htiAndChildCompany = permissionCenterClient.getHtiAndChildCompany().getData();
        if(GeneralTool.isNotEmpty(htiAndChildCompany)&&htiAndChildCompany.size()>0){
            Set<Long> matchedCompanyIds = companyIdSet.stream()
                    .filter(htiAndChildCompany::contains) // 检查是否存在于 htiAndChildCompany
                    .collect(Collectors.toSet()); // 收集到新的 Set
            if(GeneralTool.isNotEmpty(matchedCompanyIds)&&matchedCompanyIds.size()>0){
                vo.setFkCompanyIds(matchedCompanyIds);
            }else {
                vo.setFkCompanyIds(new HashSet<>());
            }
        }

        /**
         * 查询申请数统计相关所有学习计划（异步处理）
         */
        CompletableFuture<List<KpiStudentOfferItemVo>> kpiPlanApplicationStudentOfferItemListFuture = CompletableFuture.supplyAsync(() -> {
            KpiPlanStudentOfferItemListDto vo1 = BeanCopyUtils.objClone(vo, KpiPlanStudentOfferItemListDto::new);
            return studentOfferItemService. getKpiPlanApplicationStudentOfferItemList(vo1);
        }, saleTaskExecutor).exceptionally(ex -> {
            log.error("Failed to get application student offer item list for kpiPlanId {} : {}", fkKpiPlanId, ex.toString());
            return Collections.emptyList();
        });
        CompletableFuture<List<KpiStudentOfferItemVo>> kpiPlanConfirmationStudentOfferItemListFuture = CompletableFuture.supplyAsync(() -> {
            KpiPlanStudentOfferItemListDto vo2 = BeanCopyUtils.objClone(vo, KpiPlanStudentOfferItemListDto::new);
            return studentOfferItemService.getKpiPlanConfirmationStudentOfferItemList(vo2);
        }, saleTaskExecutor).exceptionally(ex -> {
            log.error("Failed to get confirmation student offer item list for kpiPlanId {} : {}", fkKpiPlanId, ex.toString());
            return Collections.emptyList();
        });
        CompletableFuture<List<KpiStudentOfferItemVo>> kpiPlanSuccessStudentOfferItemListFuture = CompletableFuture.supplyAsync(() -> {
            KpiPlanStudentOfferItemListDto vo3 = BeanCopyUtils.objClone(vo, KpiPlanStudentOfferItemListDto::new);
            return studentOfferItemService.getKpiPlanSuccessStudentOfferItemList(vo3);
        }, saleTaskExecutor).exceptionally(ex -> {
            log.error("Failed to get success student offer item list for kpiPlanId {} : {}", fkKpiPlanId, ex.toString());
            return Collections.emptyList();
        });
        CompletableFuture.allOf(kpiPlanApplicationStudentOfferItemListFuture, kpiPlanConfirmationStudentOfferItemListFuture, kpiPlanSuccessStudentOfferItemListFuture).join();
        List<KpiStudentOfferItemVo> kpiPlanApplicationStudentOfferItemList = kpiPlanApplicationStudentOfferItemListFuture.join();
        List<KpiStudentOfferItemVo> kpiPlanConfirmationStudentOfferItemList = kpiPlanConfirmationStudentOfferItemListFuture.join();
        List<KpiStudentOfferItemVo> kpiPlanSuccessStudentOfferItemList = kpiPlanSuccessStudentOfferItemListFuture.join();
        // 去重
        kpiPlanApplicationStudentOfferItemList = kpiPlanApplicationStudentOfferItemList.stream().distinct().collect(Collectors.toList());
        kpiPlanConfirmationStudentOfferItemList = kpiPlanConfirmationStudentOfferItemList.stream().distinct().collect(Collectors.toList());
        kpiPlanSuccessStudentOfferItemList = kpiPlanSuccessStudentOfferItemList.stream().distinct().collect(Collectors.toList());

        // 用于过滤每个考核人员
        // key=fkKpiPlanStaffId
        Map<Long, Set<Long>> filterStaffIdsMap = Maps.newHashMap();
        Map<Long, Set<Long>> filterOfferItemIdsMap = Maps.newHashMap();
        Map<Long, Set<Long>> filterCountryIdsMap = Maps.newHashMap();
        // 用于过滤整个kpi方案
        Set<Long> allFilterStaffIds = Sets.newHashSet();
        Set<Long> allFilterOfferItemIds = Sets.newHashSet();
        Set<Long> allFilterCountryIds = Sets.newHashSet();
        for (KpiPlanStaffVo kpiPlanStaff : kpiPlanStaffDtoList) {
            Long fkStaffId = kpiPlanStaff.getFkStaffId();
            // 统计角色，枚举：BD=1/项目成员=2/PM=3
            Integer countRole = kpiPlanStaff.getCountRole();
            // 统计方式，枚举：个人=1/团队(含业务下属)=2
            Integer countMode = kpiPlanStaff.getCountMode();

            // 员工id集合
            Set<Long> staffIds = Sets.newHashSet();
            // 个人
            if (GeneralTool.isNotEmpty(countMode) && countMode.equals(ProjectExtraEnum.PERSONAGE.key)) {
                staffIds.add(fkStaffId);
            }
            // 团队
            if (GeneralTool.isNotEmpty(countMode) && countMode.equals(ProjectExtraEnum.TEAM.key)) {
                //员工id + 业务下属员工ids
                Result<List<Long>> result = permissionCenterClient.getStaffFollowerIds(fkStaffId);
                if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                    staffIds.addAll(result.getData());
                }
                staffIds.add(fkStaffId);
            }
            staffIds.removeIf(Objects::isNull);
            // key = fkKpiPlanStaffId，唯一
            Long key = kpiPlanStaff.getId();
            // BD 员工ID
            if (GeneralTool.isNotEmpty(countRole) && countRole.equals(ProjectExtraEnum.BD.key)) {
                filterStaffIdsMap.put(key, staffIds);
                allFilterStaffIds.addAll(staffIds);
            }

            // 项目成员：对应绑定的申请方案，所对应的学生统计
            if (GeneralTool.isNotEmpty(countRole) && countRole.equals(ProjectExtraEnum.STUDENT_OFFER_STAFF.key)) {
                List<Long> filterOfferItemIdList = studentOfferItemService.filterProjectRoleStudentOfferItemList(kpiPlan, new ArrayList<>(staffIds), TableEnum.SALE_STUDENT_OFFER.key);
                filterOfferItemIdList.removeIf(Objects::isNull);
                filterOfferItemIdsMap.put(key, new HashSet<>(filterOfferItemIdList));
                allFilterOfferItemIds.addAll(filterOfferItemIdList);
            }

            // PM：所对应申请国家
            if (GeneralTool.isNotEmpty(countRole) && countRole.equals(ProjectExtraEnum.CPM.key)) {
                List<StaffAreaCountry> staffAreaCountrys = permissionCenterClient.getStaffAreaCountryByStaffIds(new ArrayList<>(staffIds)).getData();
                if (GeneralTool.isNotEmpty(staffAreaCountrys)) {
                    Set<String> fkAreaCountryKeys = staffAreaCountrys.stream().map(StaffAreaCountry::getFkAreaCountryKey).collect(Collectors.toSet());
                    List<AreaCountryVo> areaCountryList = institutionCenterClient.getCountryByKey(new ArrayList<>(fkAreaCountryKeys)).getData();
                    if (GeneralTool.isNotEmpty(areaCountryList)) {
                        Set<Long> countryIds = areaCountryList.stream().map(AreaCountryVo::getId).filter(Objects::nonNull).collect(Collectors.toSet());
                        filterCountryIdsMap.put(key, countryIds);
                        allFilterCountryIds.addAll(countryIds);
                    }
                }
            }
        }

        // 按照不同的统计角色（BD/项目成员/PM）去过滤整个数据源（申请数/交押数/成功入学数）
        kpiPlanApplicationStudentOfferItemList = this.filterStudentOfferItemListByCountRole(
                allFilterStaffIds, allFilterOfferItemIds, allFilterCountryIds, kpiPlanApplicationStudentOfferItemList);
        kpiPlanConfirmationStudentOfferItemList = this.filterStudentOfferItemListByCountRole(
                allFilterStaffIds, allFilterOfferItemIds, allFilterCountryIds, kpiPlanConfirmationStudentOfferItemList);
        kpiPlanSuccessStudentOfferItemList = this.filterStudentOfferItemListByCountRole(
                allFilterStaffIds, allFilterOfferItemIds, allFilterCountryIds, kpiPlanSuccessStudentOfferItemList);

        // 子项设置相关属性
        List<KpiPlanGroupItemStatisticsVo> kpiPlanGroupItemStatisticsDtos = this.getKpiPlanGroupItemStatisticsDtos(groupItemList,
                kpiPlanApplicationStudentOfferItemList, kpiPlanConfirmationStudentOfferItemList, kpiPlanSuccessStudentOfferItemList);

        // 子项设置每个考核人员的相关信息,kpi、成功入学数、进度
        this.getKpiPlanGroupItemTargetStatisticsDtoList(kpiPlanGroupItemStatisticsDtos, kpiPlanStaffDtoList,
                filterStaffIdsMap, filterOfferItemIdsMap, filterCountryIdsMap);

        // 组装代理排名（成功入学列表）
        if (GeneralTool.isNotEmpty(kpiPlanGroupItemStatisticsDtos)) {
            // 整个KPI方案下每条明细的成功入学列表
            List<KpiStudentOfferItemVo> kpiSuccessStudentOfferItemList = kpiPlanGroupItemStatisticsDtos.stream()
                    .map(KpiPlanGroupItemStatisticsVo::getKpiSuccessStudentOfferItemDtoList)
                    .filter(GeneralTool::isNotEmpty)
                    .flatMap(List::stream).collect(Collectors.toList());
            List<KpiAgentRankVo> kpiAgentRankDtos = getAgentRank(kpiSuccessStudentOfferItemList);
            kpiPlanStatisticsDto.setKpiAgentRankDtos(kpiAgentRankDtos);
        }

        //根据组别分组子项
        Map<Long, List<KpiPlanGroupItemStatisticsVo>> groupItemMap = kpiPlanGroupItemStatisticsDtos.stream().collect(
                Collectors.groupingBy(KpiPlanGroupItemStatisticsVo::getFkKpiPlanGroupId));

        KpiPlanGroupOrItemStatisticsResultVo resultDto = new KpiPlanGroupOrItemStatisticsResultVo();
        // 表头
        List<KpiPlanGroupOrItemStatisticsHeadVo> headDtoList = new ArrayList<>();
        // 所有考核人员
        for (KpiPlanStaffVo staff : kpiPlanStaffDtoList) {
            KpiPlanGroupOrItemStatisticsHeadVo headDto = new KpiPlanGroupOrItemStatisticsHeadVo();
            headDto.setFkKpiPlanStaffId(staff.getId());
            headDto.setFkStaffId(staff.getFkStaffId());
            headDto.setStaffName(staff.getStaffName());
            headDto.setCountRole(staff.getCountRole());
            headDto.setCountMode(staff.getCountMode());
            headDto.setStaffName(staff.getStaffName());
            headDtoList.add(headDto);
        }
        resultDto.setKpiPlanGroupOrItemStatisticsHeadDtoList(headDtoList);

        // 表明细：组别+组别明细
        List<KpiPlanGroupOrItemStatisticsVo> groupOrItemStatisticsBodyDtoList = new ArrayList<>();
        // 组别总计
        List<KpiPlanGroupAllStatisticsVo> kpiPlanGroupAllStatisticsDtoList = new ArrayList<>();

        List<KpiPlanGroupStatisticsVo> kpiPlanGroupStatisticsDtoList = BeanCopyUtils.copyListProperties(kpiPlanGroups, KpiPlanGroupStatisticsVo::new);
        for (KpiPlanGroupStatisticsVo kpiPlanGroup : kpiPlanGroupStatisticsDtoList) {
            // 组别
            KpiPlanGroupOrItemStatisticsVo groupOrItemStatisticsBodyDto = BeanCopyUtils.objClone(kpiPlanGroup, KpiPlanGroupOrItemStatisticsVo::new);
            groupOrItemStatisticsBodyDtoList.add(groupOrItemStatisticsBodyDto);

            // 当前组别子项详情
            List<KpiPlanGroupItemStatisticsVo> curGroupItemStatisticsDtos = groupItemMap.get(kpiPlanGroup.getId());
            if (GeneralTool.isNotEmpty(curGroupItemStatisticsDtos)) {
                List<KpiPlanGroupOrItemStatisticsVo> kpiPlanGroupOrItemStatisticsDtos = BeanCopyUtils.copyListProperties(curGroupItemStatisticsDtos, KpiPlanGroupOrItemStatisticsVo::new);
                groupOrItemStatisticsBodyDtoList.addAll(kpiPlanGroupOrItemStatisticsDtos);

                // 每个组别的总计
                KpiPlanAllStatisticsVo kpiPlanAllStatisticsDto = this.getKpiPlanAllStatisticsDto(curGroupItemStatisticsDtos, kpiPlanStaffDtoList);
                KpiPlanGroupAllStatisticsVo kpiPlanGroupAllStatisticsDto = BeanCopyUtils.objClone(kpiPlanAllStatisticsDto, KpiPlanGroupAllStatisticsVo::new);
                kpiPlanGroupAllStatisticsDto.setFkKpiPlanGroupId(kpiPlanGroup.getId());
                kpiPlanGroupAllStatisticsDto.setGroupName(kpiPlanGroup.getGroupName());
                kpiPlanGroupAllStatisticsDtoList.add(kpiPlanGroupAllStatisticsDto);

                resultDto.setKpiPlanGroupOrItemStatisticsBodyDtoList(groupOrItemStatisticsBodyDtoList);
            }
        }
        kpiPlanStatisticsDto.setKpiPlanGroupOrItemStatisticsResultDto(resultDto);
        // 设置kpi方案的所有字段的总计
        if (GeneralTool.isNotEmpty(kpiPlanGroupItemStatisticsDtos)) {
            KpiPlanAllStatisticsVo allStatisticsDto = this.getKpiPlanAllStatisticsDto(kpiPlanGroupItemStatisticsDtos, kpiPlanStaffDtoList);
            kpiPlanStatisticsDto.setKpiPlanAllStatisticsDto(allStatisticsDto);
        }
        // 组别总计
        kpiPlanStatisticsDto.setKpiPlanGroupAllStatisticsDtos(kpiPlanGroupAllStatisticsDtoList);

        // 能进入KPI方案统计的人员列表
        List<KpiPlanStaff> kpiPlanStaffs = kpiPlanStaffService.list(Wrappers.<KpiPlanStaff>lambdaQuery()
                .in(KpiPlanStaff::getFkKpiPlanId, fkKpiPlanId));
        Map<Long, Set<Long>> staffIdsMap = this.getIsKpiPlanStatistics(kpiPlanStaffs);
        Set<Long> statisticsStaffIds = staffIdsMap.get(fkKpiPlanId);
        kpiPlanStatisticsDto.setIsKpiPlanStatistics(GeneralTool.isNotEmpty(statisticsStaffIds) && statisticsStaffIds.contains(SecureUtil.getStaffId()));
        return kpiPlanStatisticsDto;
    }

    /**
     * 没有找到考核人员，返回考核人员树最低层级的考核人员列表
     *
     * @param fkKpiPlanId      KPI方案ID
     * @param staffFollowerIds 指定人员的业务下属Ids
     * @return
     */
    @Override
    public List<KpiPlanStaff> getMinLevelStaffs(Long fkKpiPlanId, Set<Long> staffFollowerIds) {
        List<KpiPlanStaff> staffList = Lists.newArrayList();
        // 获取该KPI方案下所有考核人员
        List<KpiPlanStaff> kpiPlanAllStaffList = kpiPlanStaffService.list(Wrappers.<KpiPlanStaff>lambdaQuery()
                .eq(KpiPlanStaff::getFkKpiPlanId, fkKpiPlanId));
        if (GeneralTool.isNotEmpty(kpiPlanAllStaffList)) {
            //KPI方案下所有考核人员
            Set<Long> staffIds = kpiPlanAllStaffList.stream().map(KpiPlanStaff::getFkStaffId).collect(Collectors.toSet());
            // 获取根节点集合 目标设置人Id人员不在考核人员Id中的考核对象
            Set<Long> rootStaffIds = kpiPlanAllStaffList.stream()
                    .filter(staff -> !staffIds.contains(staff.getFkStaffIdAdd()))
                    .map(KpiPlanStaff::getFkStaffId)
                    .collect(Collectors.toSet());
            List<KpiPlanStaffVo> kpiPlanStaffDtoList = BeanCopyUtils.copyListProperties(kpiPlanAllStaffList, KpiPlanStaffVo::new);
            // 构建树形结构
            List<KpiPlanStaffTreeVo> kpiPlanStaffTreeDtoList = kpiPlanStaffService.buildTree(kpiPlanStaffDtoList, rootStaffIds);
            // 获取指定人员下属添加的考核人员列表，然后返回该列表的最小层级
            Integer minLevel = kpiPlanStaffService.getMinLevel(kpiPlanStaffTreeDtoList, staffFollowerIds);
            // 返回考核人员树最低层级的考核人员列表
            staffList = kpiPlanStaffService.getStaffTreeDtoByLevel(kpiPlanStaffTreeDtoList, minLevel);
        }
        return staffList;
    }

    /**
     * 获取kpi方案的代理排名
     *
     * @param kpiSuccessStudentOfferItemList 整个KPI方案下每条明细的成功入学列表
     * @return 排名
     */
    private List<KpiAgentRankVo> getAgentRank(List<KpiStudentOfferItemVo> kpiSuccessStudentOfferItemList) {
        List<KpiAgentRankVo> kpiAgentRankVos = new ArrayList<>();
        if (GeneralTool.isEmpty(kpiSuccessStudentOfferItemList)) {
            return Collections.emptyList();
        }
        // 按照代理id分组
        Map<Long, List<KpiStudentOfferItemVo>> agentMap = kpiSuccessStudentOfferItemList.stream().collect(Collectors.groupingBy(KpiStudentOfferItemVo::getFkAgentId));
        Set<Long> fkAgentIds = agentMap.keySet();
        if (GeneralTool.isEmpty(fkAgentIds)) {
            return Collections.emptyList();
        }
        // 根据所有的代理id获取对应的bd名称
        Map<Long, String> bdNameMap = agentStaffService.getBdNameByAgentIds(fkAgentIds);
        //List<Agent> agentList = agentMapper.selectList(Wrappers.<Agent>lambdaQuery().eq(Agent::getIsActive, 1).in(Agent::getId, fkAgentIds));
        List<Agent> agentList = agentService.list(Wrappers.<Agent>lambdaQuery().eq(Agent::getIsActive, 1).in(Agent::getId, fkAgentIds));
        // 根据代理ids获取bd的区域dto集合
        Map<Long, List<AreaRegionVo>> areaRegionDtosMap = agentStaffService.getAreaRegionDtosByAgentIds(fkAgentIds);
        //国家ids
        Set<Long> countryIds = agentList.stream().map(Agent::getFkAreaCountryId).collect(Collectors.toSet());
        //州省ids
        Set<Long> stateIds = agentList.stream().map(Agent::getFkAreaStateId).collect(Collectors.toSet());
        //城市ids
        Set<Long> cityIds = agentList.stream().map(Agent::getFkAreaCityId).collect(Collectors.toSet());

        //根据国家ids获取国家名称
        Map<Long, String> countryNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(countryIds)) {
            Result<Map<Long, String>> countryNameResult = institutionCenterClient.getCountryNamesByIds(countryIds);
            if (countryNameResult.isSuccess() && GeneralTool.isNotEmpty(countryNameResult.getData())) {
                countryNamesByIds = countryNameResult.getData();
            }
        }
        //根据州省ids获取州省名称
        Map<Long, String> stateNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(stateIds)) {
            Result<Map<Long, String>> result = institutionCenterClient.getStateFullNamesByIds(stateIds);
            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                stateNamesByIds = result.getData();
            }
        }
        //根据州省ids获取城市名称
        Map<Long, String> cityNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(cityIds)) {
            Result<Map<Long, String>> result = institutionCenterClient.getCityFullNamesByIds(cityIds);
            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                cityNamesByIds = result.getData();
            }
        }
        // 设置代理排名内容
        for (Agent agent : agentList) {
            KpiAgentRankVo kpiAgentRankVo = new KpiAgentRankVo();
            Long agentId = agent.getId();
            kpiAgentRankVo.setFkAgentId(agentId);
            kpiAgentRankVo.setFkAgentName(agent.getName());
            kpiAgentRankVo.setBdName(bdNameMap.get(agentId));
            kpiAgentRankVo.setAreaRegionDtos(areaRegionDtosMap.get(agentId));
            kpiAgentRankVo.setCountryName(countryNamesByIds.get(agent.getFkAreaCountryId()));
            kpiAgentRankVo.setStateName(stateNamesByIds.get(agent.getFkAreaStateId()));
            kpiAgentRankVo.setCityName(cityNamesByIds.get(agent.getFkAreaCityId()));

            List<KpiStudentOfferItemVo> kpiStudentOfferItemVos = agentMap.get(agentId);
            int studentCount = 0;
            if (GeneralTool.isNotEmpty(kpiStudentOfferItemVos)) {
                // 学生数
                studentCount = kpiStudentOfferItemVos.stream().map(KpiStudentOfferItemVo::getFkStudentId).collect(Collectors.toSet()).size();
            }
            kpiAgentRankVo.setStudentCount(studentCount);
            Set<Long> offerItemIds = kpiStudentOfferItemVos.stream().map(KpiStudentOfferItemVo::getId).collect(Collectors.toSet());
            kpiAgentRankVo.setStudentCountOfferItemIds(offerItemIds);

            kpiAgentRankVos.add(kpiAgentRankVo);
        }
        // 按照 学生数（按学生）的值进行降序排名
        kpiAgentRankVos = kpiAgentRankVos.stream()
                .sorted(Comparator.comparing(KpiAgentRankVo::getStudentCount).reversed())
                .collect(Collectors.toList());
        // 小计
        int studentCountTotal = kpiAgentRankVos.stream()
                .mapToInt(i -> GeneralTool.isEmpty(i.getStudentCount()) ? 0 : i.getStudentCount()).sum();
        KpiAgentRankVo kpiAgentRankVoTotal = new KpiAgentRankVo();
        kpiAgentRankVoTotal.setStudentCount(studentCountTotal);
        kpiAgentRankVos.add(kpiAgentRankVoTotal);
        return kpiAgentRankVos;
    }

    /**
     * 求kpi方案的总计或者组别的总计对应的数据
     *
     * @param kpiPlanGroupItemStatisticsVos 将要计算总计的数据列表
     * @param kpiPlanGroupItemStatisticsVos 考核人员列表
     * @return
     */
    public KpiPlanAllStatisticsVo getKpiPlanAllStatisticsDto(List<KpiPlanGroupItemStatisticsVo> kpiPlanGroupItemStatisticsVos,
                                                             List<KpiPlanStaffVo> kpiPlanStaffVoList) {
        // 最后一条总计
        KpiPlanAllStatisticsVo kpiPlanAllStatisticsVo = new KpiPlanAllStatisticsVo();
        // kpi小计-总计
        int kpiAllTotal = kpiPlanGroupItemStatisticsVos.stream().mapToInt(i -> GeneralTool.isEmpty(i.getTargetEnrolled()) ? 0 : i.getTargetEnrolled()).sum();
        kpiPlanAllStatisticsVo.setKpiAllTotal(String.valueOf(kpiAllTotal));
        // 考核小计-总计
        int assessmentAllTotal = kpiPlanGroupItemStatisticsVos.stream()
                .mapToInt(i -> GeneralTool.isEmpty(i.getAssessmentSubtotal()) ? 0 : Integer.parseInt(i.getAssessmentSubtotal())).sum();
        kpiPlanAllStatisticsVo.setAssessmentAllTotal(String.valueOf(assessmentAllTotal));
        // 进度-总计，考核小计-总计/kpi小计-总计
        String allScheduleTotal = calculateRatio(String.valueOf(assessmentAllTotal), String.valueOf(kpiAllTotal));
        kpiPlanAllStatisticsVo.setAllScheduleTotal(allScheduleTotal);

        // kpi总占比-总计
        String kpiAllRatioTotal = calculateRatio(String.valueOf(kpiAllTotal), kpiPlanGroupItemStatisticsVos.get(0).getKpiAllTotal());
        kpiPlanAllStatisticsVo.setKpiAllRatioTotal(kpiAllRatioTotal);

        // 考核总占比-总计，总进度-总计 * KPI总占比-总计
        kpiPlanAllStatisticsVo.setAssessmentAllRatioTotal("0.00%");
        if ("-".equals(allScheduleTotal) || "-".equals(kpiAllRatioTotal)) {
            kpiPlanAllStatisticsVo.setAssessmentAllRatioTotal("-");
        } else {
            BigDecimal assessmentAllRatioTotal = new BigDecimal(allScheduleTotal.substring(0, allScheduleTotal.length() - 1))
                    .multiply(new BigDecimal(kpiAllRatioTotal.substring(0, kpiAllRatioTotal.length() - 1)))
                    .divide(new BigDecimal("100"))
                    .setScale(2, RoundingMode.HALF_UP);
            kpiPlanAllStatisticsVo.setAssessmentAllRatioTotal(assessmentAllRatioTotal + "%");
        }

        // 申请数-总计
        int applicationCountTotal = kpiPlanGroupItemStatisticsVos.stream()
                .mapToInt(i -> GeneralTool.isEmpty(i.getKpiApplicationCount()) ? 0 : i.getKpiApplicationCount()).sum();
        kpiPlanAllStatisticsVo.setApplicationCountTotal(String.valueOf(applicationCountTotal));
        // 学生数-总计
        int studentCountTotal = kpiPlanGroupItemStatisticsVos.stream()
                .mapToInt(i -> GeneralTool.isEmpty(i.getKpiStudentCount()) ? 0 : i.getKpiStudentCount()).sum();
        kpiPlanAllStatisticsVo.setStudentCountTotal(String.valueOf(studentCountTotal));
        // 交押数/定校数-总计
        int confirmationCountTotal = kpiPlanGroupItemStatisticsVos.stream()
                .mapToInt(i -> GeneralTool.isEmpty(i.getKpiConfirmationCount()) ? 0 : i.getKpiConfirmationCount()).sum();
        kpiPlanAllStatisticsVo.setConfirmationCountTotal(String.valueOf(confirmationCountTotal));
        // 代理数总计
        int agentCountTotal = kpiPlanGroupItemStatisticsVos.stream()
                .mapToInt(i -> GeneralTool.isEmpty(i.getKpiAgentCount()) ? 0 : i.getKpiAgentCount()).sum();
        kpiPlanAllStatisticsVo.setAgentCountTotal(String.valueOf(agentCountTotal));
        // 所有考核人员kpi目标列表-总计
        List<KpiPlanAllTargetStatisticsVo> kpiPlanAllTargetStatisticsVoList = new ArrayList<>();
        for (KpiPlanStaffVo staff : kpiPlanStaffVoList) {
            KpiPlanAllTargetStatisticsVo kpiPlanAllTargetStatisticsVo = new KpiPlanAllTargetStatisticsVo();
            int targetEnrolledTotal = 0;
            int successCountTotal = 0;
            for (KpiPlanGroupItemStatisticsVo item : kpiPlanGroupItemStatisticsVos) {
                List<KpiPlanTargetStatisticsVo> kpiPlanTargetStatisticsDtoList = item.getKpiPlanTargetStatisticsDtoList();
                if (GeneralTool.isNotEmpty(kpiPlanTargetStatisticsDtoList)) {
                    int targetEnrolled = kpiPlanTargetStatisticsDtoList.stream()
                            // fkKpiPlanStaffId，唯一
                            .filter(t -> t.getFkKpiPlanStaffId().equals(staff.getId()))
                            .mapToInt(i -> GeneralTool.isEmpty(i.getTargetEnrolled()) ? 0 : i.getTargetEnrolled()).sum();
                    targetEnrolledTotal = targetEnrolledTotal + targetEnrolled;
                    int successCount = kpiPlanTargetStatisticsDtoList.stream()
                            // fkKpiPlanStaffId，唯一
                            .filter(t -> t.getFkKpiPlanStaffId().equals(staff.getId()))
                            .mapToInt(i -> GeneralTool.isEmpty(i.getSuccessCount()) ? 0 : i.getSuccessCount()).sum();
                    successCountTotal = successCountTotal + successCount;
                }
            }
            kpiPlanAllTargetStatisticsVo.setFkKpiPlanStaffId(staff.getId());
            kpiPlanAllTargetStatisticsVo.setStaffName(staff.getStaffName());
            kpiPlanAllTargetStatisticsVo.setTargetEnrolled(targetEnrolledTotal);
            kpiPlanAllTargetStatisticsVo.setSuccessCount(successCountTotal);

            String scheduleTotal = calculateRatio(String.valueOf(successCountTotal), String.valueOf(targetEnrolledTotal));
            kpiPlanAllTargetStatisticsVo.setSchedule(scheduleTotal);
            kpiPlanAllTargetStatisticsVoList.add(kpiPlanAllTargetStatisticsVo);
        }
        kpiPlanAllStatisticsVo.setKpiPlanAllTargetStatisticsDtoList(kpiPlanAllTargetStatisticsVoList);
        return kpiPlanAllStatisticsVo;
    }

    /**
     * 设置每个组别明细中每个考核人员的明细数据
     *
     * @param kpiPlanGroupItemStatisticsDtos 所有组别明细数据
     * @param kpiPlanStaffDtoList            考核人员列表
     * @param filterStaffIdsMap              BD：员工Id（key=fkKpiPlanStaffId）
     * @param filterOfferItemIdsMap          项目成员：对应绑定的申请Id（key=fkKpiPlanStaffId）
     * @param filterCountryIdsMap            PM:所对应申请国家Id（key=fkKpiPlanStaffId）
     * @return
     */
    private void getKpiPlanGroupItemTargetStatisticsDtoList(List<KpiPlanGroupItemStatisticsVo> kpiPlanGroupItemStatisticsDtos,
                                                            List<KpiPlanStaffVo> kpiPlanStaffDtoList,
                                                            Map<Long, Set<Long>> filterStaffIdsMap,
                                                            Map<Long, Set<Long>> filterOfferItemIdsMap,
                                                            Map<Long, Set<Long>> filterCountryIdsMap) {
        for (KpiPlanGroupItemStatisticsVo item : kpiPlanGroupItemStatisticsDtos) {
            // 每条明细对应的考核人员kpi目标列表
            List<KpiPlanTargetVo> kpiPlanTargetDtoList = item.getKpiPlanTargetDtoList();
            // 每条明细对应成功入学量学习计划列表
            List<KpiStudentOfferItemVo> kpiSuccessStudentOfferItemDtoList = item.getKpiSuccessStudentOfferItemDtoList();
            List<KpiPlanTargetStatisticsVo> kpiPlanTargetStatisticsDtoList = new ArrayList<>();
            for (KpiPlanStaffVo staff : kpiPlanStaffDtoList) {
                // fkKpiPlanStaffId，唯一
                Long fkKpiPlanStaffId = staff.getId();
                KpiPlanTargetStatisticsVo kpiPlanTargetStatisticsDto = new KpiPlanTargetStatisticsVo();
                // 考核人员信息
                kpiPlanTargetStatisticsDto.setFkKpiPlanGroupItemId(item.getId());
                kpiPlanTargetStatisticsDto.setFkKpiPlanStaffId(staff.getId());
                kpiPlanTargetStatisticsDto.setStaffName(staff.getStaffName());


                // 当前考核人员对应的成功入学列表
                Integer successCount = 0;
                // 申请计划id列表，用于kpi方案统计当前考核人员成功入学数跳转到申请计划汇总
                Set<Long> kpiSuccessOfferItemIds = new HashSet<>();
                if (GeneralTool.isNotEmpty(kpiSuccessStudentOfferItemDtoList)) {
                    List<KpiStudentOfferItemVo> kpiStudentOfferItemDtos = filterStudentOfferItemListByCountRole(
                            filterStaffIdsMap.get(fkKpiPlanStaffId), filterOfferItemIdsMap.get(fkKpiPlanStaffId), filterCountryIdsMap.get(fkKpiPlanStaffId), kpiSuccessStudentOfferItemDtoList);
                    successCount = kpiStudentOfferItemDtos.stream().map(KpiStudentOfferItemVo::getFkStudentId).collect(Collectors.toSet()).size();
                    kpiSuccessOfferItemIds = kpiStudentOfferItemDtos.stream().map(KpiStudentOfferItemVo::getId).collect(Collectors.toSet());
                }
                kpiPlanTargetStatisticsDto.setSuccessCount(successCount);
                kpiPlanTargetStatisticsDto.setKpiSuccessOfferItemIds(kpiSuccessOfferItemIds);

                kpiPlanTargetStatisticsDto.setTargetEnrolled(0);
                if (GeneralTool.isNotEmpty(kpiPlanTargetDtoList)) {
                    // 当前考核人员kpi目标值
                    List<KpiPlanTargetVo> curStaffTarget = kpiPlanTargetDtoList.stream()
                            // fkKpiPlanStaffId，唯一
                            .filter(k -> k.getFkKpiPlanStaffId().equals(staff.getId()))
                            .collect(Collectors.toList());
                    if (GeneralTool.isNotEmpty(curStaffTarget)) {
                        Integer targetEnrolled = curStaffTarget.get(0).getTargetEnrolled();
                        kpiPlanTargetStatisticsDto.setTargetEnrolled(GeneralTool.isNotEmpty(targetEnrolled) ? targetEnrolled : 0);
                    }
                }

                // 进度，成功入学数/kpi目标成功入学数
                String schedule = calculateRatio(successCount.toString(), String.valueOf(kpiPlanTargetStatisticsDto.getTargetEnrolled()));
                kpiPlanTargetStatisticsDto.setSchedule(schedule);

                kpiPlanTargetStatisticsDtoList.add(kpiPlanTargetStatisticsDto);
            }
            item.setKpiPlanTargetStatisticsDtoList(kpiPlanTargetStatisticsDtoList);
        }
    }

    /**
     * 计算百分比（保留百分比后两位小数，四舍五入）
     *
     * @param numerator   分子
     * @param denominator 分母
     * @return 百分比字符串
     */
    private String calculateRatio(String numerator, String denominator) {
        BigDecimal numeratorBD = new BigDecimal(numerator);
        BigDecimal denominatorBD = new BigDecimal(denominator);
        String ratio = "-";
        if (!denominatorBD.equals(BigDecimal.ZERO)) {
            BigDecimal ratioBD = new BigDecimal(String.valueOf(numeratorBD.divide(denominatorBD, 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP)));
            if (ratioBD.compareTo(maxRatio) > 0) {
                ratioBD = maxRatio;
            }
            ratio = ratioBD + "%";
        }
        return ratio;
    }

    /**
     * 设置每个分组明细的属性数据
     *分组明细列表
     * @param
     * @param kpiPlanApplicationStudentOfferItemList  整个kpi方案申请数数据源
     * @param kpiPlanConfirmationStudentOfferItemList 整个kpi方案交押数数据源
     * @param kpiPlanSuccessStudentOfferItemList      整个kpi方案成功入学数数据源
     * @return
     */
    private List<KpiPlanGroupItemStatisticsVo> getKpiPlanGroupItemStatisticsDtos(List<KpiPlanGroupItem> groupItemList,
                                                                                  List<KpiStudentOfferItemVo> kpiPlanApplicationStudentOfferItemList,
                                                                                  List<KpiStudentOfferItemVo> kpiPlanConfirmationStudentOfferItemList,
                                                                                  List<KpiStudentOfferItemVo> kpiPlanSuccessStudentOfferItemList) {
        //查询所有子项的学校提供商
        Map<Long, String> providerNameMap = new HashMap<>();
        Set<Long> fkInstitutionProviderIds = groupItemList.stream().map(KpiPlanGroupItem::getFkInstitutionProviderId).collect(Collectors.toSet());
        if (GeneralTool.isNotEmpty(fkInstitutionProviderIds)) {
            Result<Map<Long, InstitutionProviderVo>> providerMapResult = institutionCenterClient.getInstitutionProviderMapByIds(fkInstitutionProviderIds);
            if (providerMapResult.isSuccess() && providerMapResult.getData() != null) {
                Map<Long, InstitutionProviderVo> data = providerMapResult.getData();
                for (Map.Entry<Long, InstitutionProviderVo> entry : data.entrySet()) {
                    InstitutionProviderVo institutionProviderVo = entry.getValue();
                    providerNameMap.put(entry.getKey(), institutionProviderVo.getName() + "（" + institutionProviderVo.getNameChn() + "）");
                }
            }
        }

        //查询所有子项的专业等级、申请国家、剔除提供商下的学校
        Map<Long, String> majorLevelNameMap = new HashMap<>();
        Map<Long, String> areaCountryNameMap = new HashMap<>();
        Map<Long, String> institutionNameMap = new HashMap<>();
        Set<Long> fkMajorLevelIds = new HashSet<>();
        Set<Long> fkAreaCountryIds = new HashSet<>();
        Set<Long> fkInstitutionIds = new HashSet<>();
        for (KpiPlanGroupItem item : groupItemList) {
            //专业等级
            if (GeneralTool.isNotEmpty(item.getFkMajorLevelIds())) {
                String[] split = item.getFkMajorLevelIds().split(",");
                for (String s : split) {
                    fkMajorLevelIds.add(Long.valueOf(s));
                }
            }
            //申请国家
            if (GeneralTool.isNotEmpty(item.getFkAreaCountryIds())) {
                String[] split = item.getFkAreaCountryIds().split(",");
                for (String s : split) {
                    fkAreaCountryIds.add(Long.valueOf(s));
                }
            }
            // 增加国家维度的统计
            if (GeneralTool.isNotEmpty(item.getFkAreaCountryIdsKpi())) {
                // 按逗号分割字符串
                String[] ids = item.getFkAreaCountryIdsKpi().split(",");
                for (String idStr : ids) {
                    // 去除空格并转换为Long
                    Long id = Long.parseLong(idStr.trim());
                    // 使用add方法添加到Set
                    fkAreaCountryIds.add(id);
                }
//                fkAreaCountryIds.add(item.getFkAreaCountryIdKpi());
            }
            //剔除提供商下的学校
            if (GeneralTool.isNotEmpty(item.getFkInstitutionIds())) {
                String[] split = item.getFkInstitutionIds().split(",");
                for (String s : split) {
                    fkInstitutionIds.add(Long.valueOf(s));
                }
            }
        }

        if (GeneralTool.isNotEmpty(fkMajorLevelIds)) {
            Result<Map<Long, MajorLevel>> result = institutionCenterClient.getMajorLevelByIds(fkMajorLevelIds);
            if (result.isSuccess()) {
                Map<Long, MajorLevel> data = result.getData();
                for (Map.Entry<Long, MajorLevel> entry : data.entrySet()) {
                    MajorLevel majorLevel = entry.getValue();
                    majorLevelNameMap.put(entry.getKey(), majorLevel.getLevelName() + "（" + majorLevel.getLevelNameChn() + "）");
                }
            }
        }
        if (GeneralTool.isNotEmpty(fkAreaCountryIds)) {
            Result<Map<Long, String>> countryNameResult = institutionCenterClient.getCountryFullNamesByIds(fkAreaCountryIds);
            if (countryNameResult.isSuccess() && countryNameResult.getData() != null) {
                areaCountryNameMap = countryNameResult.getData();
            }
        }
        if (GeneralTool.isNotEmpty(fkInstitutionIds)) {
            Result<Map<Long, String>> institutionNameResult = institutionCenterClient.getInstitutionNamesByIds(fkInstitutionIds);
            if (institutionNameResult.isSuccess() && institutionNameResult.getData() != null) {
                institutionNameMap = institutionNameResult.getData();
            }
        }

        //查询所有子项对应的KPI目标设置
        Set<Long> itemIds = groupItemList.stream().map(KpiPlanGroupItem::getId).collect(Collectors.toSet());
        List<KpiPlanTarget> kpiPlanTargets = Lists.newArrayList();
        if (GeneralTool.isNotEmpty(itemIds)) {
            kpiPlanTargets = kpiPlanTargetService.list(Wrappers.<KpiPlanTarget>lambdaQuery()
                    .in(KpiPlanTarget::getFkKpiPlanGroupItemId, itemIds));
        }

        // kpi小计（目标）-总计
        int kpiAllTotal = groupItemList.stream().mapToInt(i -> GeneralTool.isEmpty(i.getTargetEnrolled()) ? 0 : i.getTargetEnrolled()).sum();

        // 不改变传入数据源的数据
        List<KpiStudentOfferItemVo> applicationStudentOfferItemList = BeanCopyUtils.copyListProperties(kpiPlanApplicationStudentOfferItemList, KpiStudentOfferItemVo::new);
        List<KpiStudentOfferItemVo> confirmationStudentOfferItemList = BeanCopyUtils.copyListProperties(kpiPlanConfirmationStudentOfferItemList, KpiStudentOfferItemVo::new);
        List<KpiStudentOfferItemVo> successStudentOfferItemList = BeanCopyUtils.copyListProperties(kpiPlanSuccessStudentOfferItemList, KpiStudentOfferItemVo::new);

        List<KpiPlanGroupItemStatisticsVo> kpiPlanGroupItemDtos = new ArrayList<>();
        for (KpiPlanGroupItem item : groupItemList) {
            KpiPlanGroupItemStatisticsVo itemDto = BeanCopyUtils.objClone(item, KpiPlanGroupItemStatisticsVo::new);
            //学校/集团，如果提供商名称为空，则显示国家名称（国家维度统计）
            if (GeneralTool.isNotEmpty(itemDto.getFkInstitutionProviderId())) {
                itemDto.setFkInstitutionProviderName(providerNameMap.get(itemDto.getFkInstitutionProviderId()));
            } else {
                //将逗号分隔的字符串拆分为多个ID，并获取对应的名称
                if(GeneralTool.isNotEmpty(itemDto.getFkAreaCountryIdsKpi())){
                    Map<Long, String> finalAreaCountryNameMap = areaCountryNameMap;
                    String names = Arrays.stream(itemDto.getFkAreaCountryIdsKpi().split(","))
                            .map(String::trim)
                            .filter(s -> !s.isEmpty())
                            .map(id -> finalAreaCountryNameMap.getOrDefault(Long.parseLong(id), ""))
                            .collect(Collectors.joining(", "));

                    itemDto.setFkInstitutionProviderName(names);
                }

//                if (GeneralTool.isNotEmpty(itemDto.getFkAreaCountryIdKpi())) {
//                    itemDto.setFkInstitutionProviderName(areaCountryNameMap.get(itemDto.getFkAreaCountryIdKpi()));
//                }
            }

            //课程等级
            if (GeneralTool.isNotEmpty(itemDto.getFkMajorLevelIds())) {
                itemDto.setFkMajorLevelNames(getNameSub(itemDto.getFkMajorLevelIds(), majorLevelNameMap));
                List<Long> fkMajorLevelIdList = Arrays.stream(itemDto.getFkMajorLevelIds().split(","))
                        .map(Long::parseLong)
                        .collect(Collectors.toList());
                itemDto.setFkMajorLevelIdList(fkMajorLevelIdList);
            }

            //国家
            if (GeneralTool.isNotEmpty(itemDto.getFkAreaCountryIds())) {
                if (GeneralTool.isNotEmpty(itemDto.getCountryIncludeType())) {
                    List<Long> fkAreaCountryIdList = Arrays.stream(itemDto.getFkAreaCountryIds().split(","))
                            .map(Long::parseLong)
                            .collect(Collectors.toList());
                    itemDto.setFkAreaCountryIdList(fkAreaCountryIdList);
                    if (itemDto.getCountryIncludeType().equals(0)) {
                        itemDto.setFkAreaCountryNames("不包含国家：" + getNameSub(itemDto.getFkAreaCountryIds(), areaCountryNameMap));
                    }
                    if (itemDto.getCountryIncludeType().equals(1)) {
                        itemDto.setFkAreaCountryNames(getNameSub(itemDto.getFkAreaCountryIds(), areaCountryNameMap));
                    }
                }
            } else {
                itemDto.setFkAreaCountryNames("所有国家");
                if (GeneralTool.isNotEmpty(itemDto.getFkAreaCountryIdsKpi())) {
                    // 当是以国家维度时，不显示名称
                    itemDto.setFkAreaCountryNames("");
                }
            }

            //剔除学校
            if (GeneralTool.isNotEmpty(itemDto.getFkInstitutionIds())) {
               // itemDto.setFkInstitutionIdsExcludingNames(getNameSub(itemDto.getFkInstitutionIds(), institutionNameMap));
                List<Long> fkInstitutionIdList = Arrays.stream(itemDto.getFkInstitutionIds().split(","))
                        .map(Long::parseLong)
                        .collect(Collectors.toList());
                itemDto.setFkInstitutionIdList(fkInstitutionIdList);
                if (itemDto.getInstitutionIncludeType().equals(0)) {
                    itemDto.setFkInstitutionIdsExcludingNames("不包含学校：" + getNameSub(itemDto.getFkInstitutionIds(), institutionNameMap));
                }
                if (itemDto.getInstitutionIncludeType().equals(1)) {
                    itemDto.setFkInstitutionIdsExcludingNames(getNameSub(itemDto.getFkInstitutionIds(), institutionNameMap));
                }
            }else {
//                List<Long> fkInstitutionIdList = Arrays.stream(itemDto.getFkInstitutionIds().split(","))
//                        .map(Long::parseLong)
//                        .collect(Collectors.toList());
//                itemDto.setFkInstitutionIdList(fkInstitutionIdList);
                itemDto.setFkInstitutionIdsExcludingNames("");
            }




            //时间设定
            StringBuilder timeSet = new StringBuilder();
            if (GeneralTool.isNotEmpty(itemDto.getStudentCreateTimeStart()) && GeneralTool.isNotEmpty(itemDto.getStudentCreateTimeEnd())) {
                timeSet.append("学生创建时间：")
                        .append(DateUtil.formatDate(itemDto.getStudentCreateTimeStart()))
                        .append("至")
                        .append(DateUtil.formatDate(itemDto.getStudentCreateTimeEnd()));
            }
            if (GeneralTool.isNotEmpty(itemDto.getIntakeTimeStart()) && GeneralTool.isNotEmpty(itemDto.getIntakeTimeEnd())) {
                if (timeSet.length() > 0) {
                    timeSet.append("，");
                }
                timeSet.append("入学时间：")
                        .append(DateUtil.formatDate(itemDto.getIntakeTimeStart()))
                        .append("至")
                        .append(DateUtil.formatDate(itemDto.getIntakeTimeEnd()));
            }
            if (GeneralTool.isNotEmpty(itemDto.getStepTimeStart()) && GeneralTool.isNotEmpty(itemDto.getStepTimeEnd())) {
                if (timeSet.length() > 0) {
                    timeSet.append("，");
                }
                timeSet.append("业务步骤登记时间：")
                        .append(DateUtil.formatDate(itemDto.getStepTimeStart()))
                        .append("至")
                        .append(DateUtil.formatDate(itemDto.getStepTimeEnd()));
            }
            //kpi分组申请计划
            if (GeneralTool.isNotEmpty(itemDto.getOfferItemCreateTimeStart()) && GeneralTool.isNotEmpty(itemDto.getOfferItemCreateTimeEnd())) {
                if (timeSet.length() > 0) {
                    timeSet.append("，");
                }
                timeSet.append("申请计划时间：")
                        .append(DateUtil.formatDate(itemDto.getOfferItemCreateTimeStart()))
                        .append("至")
                        .append(DateUtil.formatDate(itemDto.getOfferItemCreateTimeEnd()));
            }

            itemDto.setTimeSet(timeSet.toString());

            // 过滤是否只统计直录课程,是才处理，不是就不过滤
            if (GeneralTool.isNotEmpty(itemDto.getIsDirect()) && itemDto.getIsDirect()) {
                // 过滤申请量学习计划列表
                if (GeneralTool.isNotEmpty(applicationStudentOfferItemList)) {
                    applicationStudentOfferItemList = applicationStudentOfferItemList.stream().filter(KpiStudentOfferItemVo::getIsDirect).collect(Collectors.toList());
                }
                // 过滤定校量学习计划列表
                if (GeneralTool.isNotEmpty(confirmationStudentOfferItemList)) {
                    confirmationStudentOfferItemList = confirmationStudentOfferItemList.stream().filter(KpiStudentOfferItemVo::getIsDirect).collect(Collectors.toList());
                }
                // 过滤成功入学量学习计划列表
                if (GeneralTool.isNotEmpty(successStudentOfferItemList)) {
                    successStudentOfferItemList = successStudentOfferItemList.stream().filter(KpiStudentOfferItemVo::getIsDirect).collect(Collectors.toList());
                }
            }

            // 过滤是否包含后续课程,不包含才处理，不是就不过滤
            if (GeneralTool.isNotEmpty(itemDto.getIsFollow()) && !itemDto.getIsFollow()) {
                // 过滤申请量学习计划列表
                if (GeneralTool.isNotEmpty(applicationStudentOfferItemList)) {

                    applicationStudentOfferItemList = applicationStudentOfferItemList.stream()
                            .filter(list -> {
                                return list.getIsFollow()== false;
                            }) // 只保留 isFollow=false 的数据
                            .collect(Collectors.toList());
                }
                // 过滤定校量学习计划列表
                if (GeneralTool.isNotEmpty(confirmationStudentOfferItemList)) {
                    confirmationStudentOfferItemList = confirmationStudentOfferItemList.stream()
                            .filter(list -> {
                                return list.getIsFollow()== false;
                            }) // 只保留 isFollow=false 的数据
                            .collect(Collectors.toList());
                }
                // 过滤成功入学量学习计划列表  包含的话就要多，不包含的要少
                if (GeneralTool.isNotEmpty(successStudentOfferItemList)) {
                    successStudentOfferItemList = successStudentOfferItemList.stream()
                            .filter(list -> {
                                return list.getIsFollow()== false;
                            }) // 只保留 isFollow=false 的数据
                            .collect(Collectors.toList());
                }
            }




            // 过滤每个分组明细申请量学习计划列表
            if (GeneralTool.isNotEmpty(applicationStudentOfferItemList)) {
                List<KpiStudentOfferItemVo> applicationList = filterOfferItemListByItemDto(itemDto, applicationStudentOfferItemList);
                itemDto.setKpiApplicationStudentOfferItemDtoList(applicationList);
            }
            // 过滤每个分组明细定校量学习计划列表
            if (GeneralTool.isNotEmpty(confirmationStudentOfferItemList)) {
                List<KpiStudentOfferItemVo> confirmationList = filterOfferItemListByItemDto(itemDto, confirmationStudentOfferItemList);
                itemDto.setKpiConfirmationStudentOfferItemDtoList(confirmationList);
            }
            // 过滤每个分组明细成功入学量学习计划列表
            if (GeneralTool.isNotEmpty(successStudentOfferItemList)) {
                List<KpiStudentOfferItemVo> successList = filterOfferItemListByItemDto(itemDto, successStudentOfferItemList);
                itemDto.setKpiSuccessStudentOfferItemDtoList(successList);
            }

            //KPI小计目标
            List<KpiPlanTargetVo> kpiPlanTargetVos = new ArrayList<>();
            int targetSubtotal = 0;
            if (GeneralTool.isNotEmpty(kpiPlanTargets)) {
                kpiPlanTargetVos = BeanCopyUtils.copyListProperties(kpiPlanTargets, KpiPlanTargetVo::new);
                // 每一个明细对应的考核人员kpi目标列表
                List<KpiPlanTargetVo> kpiPlanTargetDtoList = kpiPlanTargetVos.stream()
                        .filter(kpiPlanTargetDto -> kpiPlanTargetDto.getFkKpiPlanGroupItemId().equals(itemDto.getId()))
                        .collect(Collectors.toList());
                itemDto.setKpiPlanTargetDtoList(kpiPlanTargetDtoList);
                //小计
                targetSubtotal = kpiPlanTargetDtoList.stream().mapToInt(i -> GeneralTool.isEmpty(i.getTargetEnrolled()) ? 0 : i.getTargetEnrolled()).sum();
            }
            itemDto.setKpiSubtotal(String.valueOf(targetSubtotal));
            if (GeneralTool.isEmpty(itemDto.getTargetEnrolled())) {
                itemDto.setTargetEnrolled(0);
            }

            //KPI总申请数
            int kpiApplicationCount = 0;
            //KPI总学生数
            int kpiStudentCount = 0;
            // 申请计划id列表，用于申请数、学生数的跳转
            Set<Long> applicationOfferItemIds = new HashSet<>();
            if (GeneralTool.isNotEmpty(itemDto.getKpiApplicationStudentOfferItemDtoList())) {
                kpiApplicationCount = itemDto.getKpiApplicationStudentOfferItemDtoList().size();
                kpiStudentCount = itemDto.getKpiApplicationStudentOfferItemDtoList().stream().map(KpiStudentOfferItemVo::getFkStudentId).collect(Collectors.toSet()).size();
                applicationOfferItemIds = itemDto.getKpiApplicationStudentOfferItemDtoList().stream().map(KpiStudentOfferItemVo::getId).collect(Collectors.toSet());
            }
            itemDto.setKpiApplicationCount(kpiApplicationCount);
            itemDto.setKpiStudentCount(kpiStudentCount);
            itemDto.setKpiApplicationOfferItemIds(applicationOfferItemIds);

            //KPI总交押数/定校数
            int kpiConfirmationCount = 0;
            // 申请计划id列表，用于交押数的跳转
            Set<Long> confirmationOfferItemIds = new HashSet<>();
            if (GeneralTool.isNotEmpty(itemDto.getKpiConfirmationStudentOfferItemDtoList())) {
                kpiConfirmationCount = itemDto.getKpiConfirmationStudentOfferItemDtoList().stream().map(KpiStudentOfferItemVo::getFkStudentId).collect(Collectors.toSet()).size();
                confirmationOfferItemIds = itemDto.getKpiConfirmationStudentOfferItemDtoList().stream().map(KpiStudentOfferItemVo::getId).collect(Collectors.toSet());
            }
            itemDto.setKpiConfirmationCount(kpiConfirmationCount);
            itemDto.setKpiConfirmationOfferItemIds(confirmationOfferItemIds);

            // KPI总代理数
            int kpiAgentCount = 0;
            // 代理id列表，用于kpi方案统计代理数跳转到代理管理
            Set<Long> kpiFkAgentIds = new HashSet<>();
            if (GeneralTool.isNotEmpty(itemDto.getKpiSuccessStudentOfferItemDtoList())) {
                kpiFkAgentIds = itemDto.getKpiSuccessStudentOfferItemDtoList().stream().map(KpiStudentOfferItemVo::getFkAgentId).collect(Collectors.toSet());
                kpiAgentCount = kpiFkAgentIds.size();
            }
            itemDto.setKpiAgentCount(kpiAgentCount);
            itemDto.setKpiFkAgentIds(kpiFkAgentIds);

            // 考核小计
            int assessmentSubtotal = 0;
            // 申请计划id列表，用于考核小计的跳转
            Set<Long> assessmentOfferItemIds = new HashSet<>();
            if (GeneralTool.isNotEmpty(itemDto.getKpiSuccessStudentOfferItemDtoList())) {
                assessmentSubtotal = itemDto.getKpiSuccessStudentOfferItemDtoList().stream().map(KpiStudentOfferItemVo::getFkStudentId).collect(Collectors.toSet()).size();
                assessmentOfferItemIds = itemDto.getKpiSuccessStudentOfferItemDtoList().stream().map(KpiStudentOfferItemVo::getId).collect(Collectors.toSet());
            }
            itemDto.setAssessmentSubtotal(String.valueOf(assessmentSubtotal));
            itemDto.setKpiAssessmentOfferItemIds(assessmentOfferItemIds);

            // 总进度，考核小计/KPI小计
            String allSchedule = calculateRatio(itemDto.getAssessmentSubtotal(), String.valueOf(itemDto.getTargetEnrolled()));
            itemDto.setAllSchedule(allSchedule);

            // kpi总计
            itemDto.setKpiAllTotal(String.valueOf(kpiAllTotal));
            // KPI总占比，KPI小计/KPI总计
            String kpiAllRatio = calculateRatio(String.valueOf(itemDto.getTargetEnrolled()), String.valueOf(kpiAllTotal));
            itemDto.setKpiAllRatio(kpiAllRatio);

            // 考核总占比，总进度*KPI总占比
            itemDto.setAssessmentAllRatio("0.00%");
            if ("-".equals(allSchedule) || "-".equals(kpiAllRatio)) {
                itemDto.setAssessmentAllRatio("-");
            } else {
                BigDecimal assessmentAllRatio = new BigDecimal(allSchedule.substring(0, allSchedule.length() - 1))
                        .multiply(new BigDecimal(kpiAllRatio.substring(0, kpiAllRatio.length() - 1)))
                        .divide(new BigDecimal("100"))
                        .setScale(2, RoundingMode.HALF_UP);
                itemDto.setAssessmentAllRatio(assessmentAllRatio + "%");
            }

            kpiPlanGroupItemDtos.add(itemDto);
        }
        return kpiPlanGroupItemDtos;
    }

    /**
     * 按条件过滤每条分组明细
     *
     * @param itemDto                     每条分组明细
     * @param kpiPlanStudentOfferItemList 申请数/交押数/成功入学数的数据源
     * @return
     */
    private List<KpiStudentOfferItemVo> filterOfferItemListByItemDto(KpiPlanGroupItemStatisticsVo itemDto,
                                                                      List<KpiStudentOfferItemVo> kpiPlanStudentOfferItemList) {
        //学校提供商过滤
        if (GeneralTool.isNotEmpty(itemDto.getFkInstitutionProviderId())) {
            kpiPlanStudentOfferItemList = kpiPlanStudentOfferItemList.stream()
                    .filter(k -> k.getFkInstitutionProviderId().equals(itemDto.getFkInstitutionProviderId())).collect(Collectors.toList());
        }

        //剔除学校
        if (GeneralTool.isNotEmpty(itemDto.getFkInstitutionIds())) {
            String[] split = itemDto.getFkInstitutionIds().split(",");
            Set<Long> institutionIdsExcluding = new HashSet<>();
            for (String s : split) {
                institutionIdsExcluding.add(Long.valueOf(s));
            }
            //不包含(剔除)
            if (GeneralTool.isNotEmpty(itemDto.getInstitutionIncludeType()) && itemDto.getInstitutionIncludeType().equals(0)) {
                kpiPlanStudentOfferItemList = kpiPlanStudentOfferItemList.stream()
                        .filter(k -> !institutionIdsExcluding.contains(k.getFkInstitutionId()))
                        .collect(Collectors.toList());
            }
            //包含
            if (GeneralTool.isNotEmpty(itemDto.getInstitutionIncludeType()) && itemDto.getInstitutionIncludeType().equals(1)) {
                kpiPlanStudentOfferItemList = kpiPlanStudentOfferItemList.stream()
                        .filter(k -> institutionIdsExcluding.contains(k.getFkInstitutionId()))
                        .collect(Collectors.toList());
            }

//            kpiPlanStudentOfferItemList = kpiPlanStudentOfferItemList.stream()
//                    .filter(k -> !institutionIdsExcluding.contains(k.getFkInstitutionId())).collect(Collectors.toList());
        }

        //课程等级过滤
        if (GeneralTool.isNotEmpty(itemDto.getFkMajorLevelIds()) && GeneralTool.isNotEmpty(itemDto.getFkMajorLevelNames())) {
            String[] split = itemDto.getFkMajorLevelIds().split(",");
            String[] fkMajorLevelNames = itemDto.getFkMajorLevelNames().split(",");
            Set<KpiStudentOfferItemVo> offerItemList = new HashSet<>();
            for (String s : split) {
                offerItemList.addAll(
                        kpiPlanStudentOfferItemList.stream()
                                .filter(k -> GeneralTool.isNotEmpty(k.getFkInstitutionCourseMajorLevelIds())
                                        && k.getFkInstitutionCourseMajorLevelIds().contains(s)).collect(Collectors.toSet())
                );
            }
            // 将手动添加的课程等级也拿出来，格式为：等级名称（等级中文名称）
            for (String fkMajorLevelName : fkMajorLevelNames) {
                offerItemList.addAll(
                        kpiPlanStudentOfferItemList.stream()
                                .filter(k -> GeneralTool.isNotEmpty(k.getOldCourseMajorLevelName())
                                        && k.getOldCourseMajorLevelName().contains(fkMajorLevelName)).collect(Collectors.toSet())
                );
            }
            kpiPlanStudentOfferItemList = new ArrayList<>(offerItemList);
        }

        //国家过滤
        Set<Long> areaCountryIds = new HashSet<>();
        if (GeneralTool.isNotEmpty(itemDto.getFkAreaCountryIds())) {
            String[] split = itemDto.getFkAreaCountryIds().split(",");
            for (String s : split) {
                areaCountryIds.add(Long.valueOf(s));
            }
            if (GeneralTool.isNotEmpty(itemDto.getCountryIncludeType()) && itemDto.getCountryIncludeType().equals(0)) {
                kpiPlanStudentOfferItemList = kpiPlanStudentOfferItemList.stream()
                        .filter(k -> !areaCountryIds.contains(k.getFkAreaCountryId()))
                        .collect(Collectors.toList());
            }
            if (GeneralTool.isNotEmpty(itemDto.getCountryIncludeType()) && itemDto.getCountryIncludeType().equals(1)) {
                kpiPlanStudentOfferItemList = kpiPlanStudentOfferItemList.stream()
                        .filter(k -> areaCountryIds.contains(k.getFkAreaCountryId()))
                        .collect(Collectors.toList());
            }
        }
        // 国家维度统计的国家过滤
        if (GeneralTool.isNotEmpty(itemDto.getFkAreaCountryIdsKpi())) {
            Set<String> countryIds = Arrays.stream(itemDto.getFkAreaCountryIdsKpi().split(","))
                    .map(String::trim)
                    .collect(Collectors.toSet());

            kpiPlanStudentOfferItemList = kpiPlanStudentOfferItemList.stream()
                    .filter(k -> countryIds.contains(k.getFkAreaCountryId().toString()))
                    .collect(Collectors.toList());
//            kpiPlanStudentOfferItemList = kpiPlanStudentOfferItemList.stream()
//                    .filter(k -> itemDto.getFkAreaCountryIdKpi().equals(k.getFkAreaCountryId()))
//                    .collect(Collectors.toList());
        }

        //时间设定过滤
        if (GeneralTool.isNotEmpty(itemDto.getStudentCreateTimeStart()) && GeneralTool.isNotEmpty(itemDto.getStudentCreateTimeEnd())) {
            kpiPlanStudentOfferItemList = kpiPlanStudentOfferItemList.stream()
                    .filter(k -> k.getStudentCreateTime().compareTo(itemDto.getStudentCreateTimeStart()) >= 0
                            && k.getStudentCreateTime().compareTo(itemDto.getStudentCreateTimeEnd()) <= 0).collect(Collectors.toList());
        }
        if (GeneralTool.isNotEmpty(itemDto.getIntakeTimeStart()) && GeneralTool.isNotEmpty(itemDto.getIntakeTimeEnd())) {
            kpiPlanStudentOfferItemList = kpiPlanStudentOfferItemList.stream()
                    .filter(k -> k.getDeferOpeningTime().compareTo(itemDto.getIntakeTimeStart()) >= 0
                            && k.getDeferOpeningTime().compareTo(itemDto.getIntakeTimeEnd()) <= 0).collect(Collectors.toList());
        }
        if (GeneralTool.isNotEmpty(itemDto.getStepTimeStart()) && GeneralTool.isNotEmpty(itemDto.getStepTimeEnd())) {
            kpiPlanStudentOfferItemList = kpiPlanStudentOfferItemList.stream()
                    .filter(k -> k.getStepTime().compareTo(itemDto.getStepTimeStart()) >= 0
                            && k.getStepTime().compareTo(itemDto.getStepTimeEnd()) <= 0).collect(Collectors.toList());
        }
        return kpiPlanStudentOfferItemList;
    }

    /**
     * 过滤kpi方案下选择的统计角色的人员
     *
     * @param filterStaffIds              BD：过滤员工ID
     * @param filterOfferItemIds          项目成员：过滤申请方案ID
     * @param filterCountryIds            PM：过滤国家ID
     * @param kpiPlanStudentOfferItemList 申请数/交押数/成功入学数的数据源
     * @return 过滤后的数据源
     */
    private List<KpiStudentOfferItemVo> filterStudentOfferItemListByCountRole(Set<Long> filterStaffIds,
                                                                               Set<Long> filterOfferItemIds,
                                                                               Set<Long> filterCountryIds,
                                                                               List<KpiStudentOfferItemVo> kpiPlanStudentOfferItemList) {
        //BD：对应绑定代理，所对应的学生统计
        if (GeneralTool.isNotEmpty(filterStaffIds)) {
            kpiPlanStudentOfferItemList = kpiPlanStudentOfferItemList.stream()
                    .filter(k -> filterStaffIds.contains(k.getFkStaffId())).collect(Collectors.toList());
        }
        //项目成员：对应绑定的申请方案，所对应的学生统计
        if (GeneralTool.isNotEmpty(filterOfferItemIds)) {
            kpiPlanStudentOfferItemList = kpiPlanStudentOfferItemList.stream()
                    .filter(k -> filterOfferItemIds.contains(k.getId())).collect(Collectors.toList());
        }
        //PM：所对应申请国家
        if (GeneralTool.isNotEmpty(filterCountryIds)) {
            kpiPlanStudentOfferItemList = kpiPlanStudentOfferItemList.stream()
                    .filter(k -> filterCountryIds.contains(k.getFkAreaCountryId())).collect(Collectors.toList());
        }
        return kpiPlanStudentOfferItemList;
    }

    public String getNameSub(String ids, Map<Long, String> targetMap) {
        String[] split = ids.split(",");
        StringBuilder sb = new StringBuilder();
        for (String s : split) {
            sb.append(targetMap.get(Long.valueOf(s))).append(",");
        }
        return sb.toString().substring(0, sb.length() - 1);
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long addKpiPlan(KpiPlanDto kpiPlanDto) {
        if (GeneralTool.isEmpty(kpiPlanDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        KpiPlan kpiPlan = BeanCopyUtils.objClone(kpiPlanDto, KpiPlan::new);
        utilService.setCreateInfo(kpiPlan);
        kpiPlanMapper.insert(kpiPlan);
        return kpiPlan.getId();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateKpiPlan(KpiPlanDto kpiPlanDto) {
        if (GeneralTool.isEmpty(kpiPlanDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        Date studentCreateTimeStart = kpiPlanDto.getStudentCreateTimeStart();
        Date studentCreateTimeEnd = kpiPlanDto.getStudentCreateTimeEnd();
        Date intakeTimeStart = kpiPlanDto.getIntakeTimeStart();
        Date intakeTimeEnd = kpiPlanDto.getIntakeTimeEnd();
        Date stepTimeStart = kpiPlanDto.getStepTimeStart();
        Date stepTimeEnd = kpiPlanDto.getStepTimeEnd();
        Date offerItemCreateTimeStart = kpiPlanDto.getOfferItemCreateTimeStart();
        Date offerItemCreateTimeEnd = kpiPlanDto.getOfferItemCreateTimeEnd();
        //查询对应组别
        List<KpiPlanGroup> groupList = kpiPlanGroupService.list(Wrappers.<KpiPlanGroup>lambdaQuery()
                .eq(KpiPlanGroup::getFkKpiPlanId, kpiPlanDto.getId()));
        if (GeneralTool.isNotEmpty(groupList)) {
            List<Long> groupIds = groupList.stream().map(KpiPlanGroup::getId).collect(Collectors.toList());
            //查询对应KPI方案子项
            List<KpiPlanGroupItem> groupItemlist = kpiPlanGroupItemService.list(Wrappers.<KpiPlanGroupItem>lambdaQuery()
                    .in(KpiPlanGroupItem::getFkKpiPlanGroupId, groupIds));
            if (GeneralTool.isNotEmpty(groupItemlist)) {
                checkKpiPlanTimeUpdate(groupItemlist, 1, studentCreateTimeStart, studentCreateTimeEnd);
                checkKpiPlanTimeUpdate(groupItemlist, 2, intakeTimeStart, intakeTimeEnd);
                checkKpiPlanTimeUpdate(groupItemlist, 3, stepTimeStart, stepTimeEnd);
                checkKpiPlanTimeUpdate(groupItemlist, 4, offerItemCreateTimeStart, offerItemCreateTimeEnd);
            }
        }

        KpiPlan kpiPlan = BeanCopyUtils.objClone(kpiPlanDto, KpiPlan::new);
        utilService.setUpdateInfo(kpiPlan);
        kpiPlanMapper.updateById(kpiPlan);
    }


    /**
     * 修改KPI方案时间校验
     *
     * <AUTHOR>
     * @DateTime 2024/4/25 14:17
     */
    public void checkKpiPlanTimeUpdate(List<KpiPlanGroupItem> groupItemlist, Integer timeType, Date startTime, Date endTime) {
        Set<Long> fkInstitutionProviderIds = groupItemlist.stream().map(KpiPlanGroupItem::getFkInstitutionProviderId).collect(Collectors.toSet());
        Map<Long, String> providerNameMap = institutionCenterClient.getInstitutionProviderNamesByIds(fkInstitutionProviderIds).getData();
        List<KpiPlanGroupItem> itemList = new ArrayList<>();
        String typeName = "";
        switch (timeType) {
            case 1:
                typeName = "学生创建时间";
                break;
            case 2:
                typeName = "入学时间";
                break;
            case 3:
                typeName = "业务步骤登记时间";
                break;
            case 4:
                typeName = "申请计划时间";
                break;
        }
        groupItemlist.forEach(k -> {
            Date checkStartTime = null;
            Date checkEndTime = null;
            switch (timeType) {
                case 1:
                    checkStartTime = k.getStudentCreateTimeStart();
                    checkEndTime = k.getStudentCreateTimeEnd();
                    break;
                case 2:
                    checkStartTime = k.getIntakeTimeStart();
                    checkEndTime = k.getIntakeTimeEnd();
                    break;
                case 3:
                    checkStartTime = k.getStepTimeStart();
                    checkEndTime = k.getStepTimeEnd();
                    break;
                case 4:
                    checkStartTime = k.getOfferItemCreateTimeStart();
                    checkEndTime = k.getOfferItemCreateTimeEnd();
                    break;
            }


            if (GeneralTool.isNotEmpty(checkStartTime) && GeneralTool.isNotEmpty(checkEndTime)) {
                if (GeneralTool.isEmpty(startTime) || GeneralTool.isEmpty(endTime)) {
                    itemList.add(k);
                } else if (!isPeriodAContainsPeriod(startTime, endTime, checkStartTime, checkEndTime)) {
                    itemList.add(k);
                }
            }
        });
        if (GeneralTool.isNotEmpty(itemList)) {
            StringBuilder sb = new StringBuilder();
            String errorMsg = null;
            if (GeneralTool.isNotEmpty(startTime) && GeneralTool.isNotEmpty(endTime)) {
                errorMsg = typeName + "范围" + DateUtil.formatDate(startTime) + "-" + DateUtil.formatDate(endTime) + "，已经不能覆盖以下设置：";
            } else {
                errorMsg = typeName + "范围为空，不能覆盖以下设置：";
            }

            sb.append(errorMsg);
            String finalTypeName = typeName;
            itemList.forEach(k -> {
                Date sTime = null;
                Date eTime = null;
                switch (timeType) {
                    case 1:
                        sTime = k.getStudentCreateTimeStart();
                        eTime = k.getStudentCreateTimeEnd();
                        break;
                    case 2:
                        sTime = k.getIntakeTimeStart();
                        eTime = k.getIntakeTimeEnd();
                        break;
                    case 3:
                        sTime = k.getStepTimeStart();
                        eTime = k.getStepTimeEnd();
                        break;
                    case 4:
                        sTime = k.getOfferItemCreateTimeStart();
                        eTime = k.getOfferItemCreateTimeEnd();
                        break;
                }
                if (GeneralTool.isNotEmpty(sTime) && GeneralTool.isNotEmpty(eTime)) {
                    sb.append(providerNameMap.get(k.getFkInstitutionProviderId()) + "提供商：" + finalTypeName + "范围" + DateUtil.formatDate(sTime) + "-" + DateUtil.formatDate(eTime) + ";");
                } else {
                    sb.append(providerNameMap.get(k.getFkInstitutionProviderId()) + "提供商：" + finalTypeName + "范围为空;");
                }
            });
            throw new GetServiceException(sb.toString());
        }
    }

    public boolean isPeriodAContainsPeriod(Date startA, Date endA, Date startB, Date endB) {
        return startA.compareTo(startB) <= 0 && endA.compareTo(endB) >= 0;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        //查询是否存在组别，存在不允许删除
        List<KpiPlanGroup> groupList = kpiPlanGroupService.list(Wrappers.<KpiPlanGroup>lambdaQuery().eq(KpiPlanGroup::getFkKpiPlanId, id).last("limit 1"));
        if (GeneralTool.isNotEmpty(groupList)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail_plan_group_exist"));
        }
        KpiPlan kpiPlan = kpiPlanMapper.selectById(id);
        if (GeneralTool.isEmpty(kpiPlan)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }
        int delete = kpiPlanMapper.deleteById(id);
        if (delete < 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }
        // 删除KPI方案下的考核人员
        kpiPlanStaffService.remove(Wrappers.<KpiPlanStaff>lambdaQuery().eq(KpiPlanStaff::getFkKpiPlanId, id));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long copyKpiPlan(Long fkKpiPlanId) {
        if (GeneralTool.isEmpty(fkKpiPlanId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        //根据ID查询KPI方案信息
        KpiPlan kpiPlan = kpiPlanMapper.selectById(fkKpiPlanId);
        //复制KPI方案
        if (GeneralTool.isNotEmpty(kpiPlan)) {
            kpiPlan.setId(null);
            kpiPlan.setGmtModified(null);
            kpiPlan.setGmtModifiedUser(null);
            kpiPlan.setTitle("【复制】" + kpiPlan.getTitle());
            utilService.setCreateInfo(kpiPlan);
            kpiPlanMapper.insert(kpiPlan);
        }

        // 查询当前方案下的所有员工
        List<KpiPlanStaff> kpiPlanStaffList = kpiPlanStaffService.list(Wrappers.<KpiPlanStaff>lambdaQuery()
                .eq(KpiPlanStaff::getFkKpiPlanId, fkKpiPlanId));

        // key: 原考核人员ID，value: 新考核人员ID
        Map<Long, Long> copyStaffIdMap = Maps.newHashMap();

        // 查询当前方案下的所有方案组别
        List<KpiPlanGroup> kpiPlanGroupList = kpiPlanGroupService.list(Wrappers.<KpiPlanGroup>lambdaQuery()
                .eq(KpiPlanGroup::getFkKpiPlanId, fkKpiPlanId));
        if (GeneralTool.isNotEmpty(kpiPlanGroupList)) {
            /**
             * 复制方案组别
             */
            for (KpiPlanGroup kpiPlanGroup : kpiPlanGroupList) {
                KpiPlanGroup group = BeanCopyUtils.objClone(kpiPlanGroup, KpiPlanGroup::new);
                group.setId(null);
                group.setFkKpiPlanId(kpiPlan.getId());
                group.setGmtModified(null);
                group.setGmtModifiedUser(null);
                utilService.setCreateInfo(group);
                kpiPlanGroupService.save(group);

                // 查询KPI方案组别子项
                List<KpiPlanGroupItem> kpiPlanGroupItemList = kpiPlanGroupItemService.list(Wrappers.<KpiPlanGroupItem>lambdaQuery()
                        .eq(KpiPlanGroupItem::getFkKpiPlanGroupId, kpiPlanGroup.getId()));
                if (GeneralTool.isNotEmpty(kpiPlanGroupItemList)) {
                    /**
                     * 复制KPI方案组别子项
                     */
                    for (KpiPlanGroupItem kpiPlanGroupItem : kpiPlanGroupItemList) {
                        KpiPlanGroupItem item = BeanCopyUtils.objClone(kpiPlanGroupItem, KpiPlanGroupItem::new);
                        item.setId(null);
                        item.setFkKpiPlanId(kpiPlan.getId());
                        item.setFkKpiPlanGroupId(group.getId());
                        item.setGmtModified(null);
                        item.setGmtModifiedUser(null);
                        utilService.setCreateInfo(item);
                        kpiPlanGroupItemService.save(item);

                        /**
                         * 复制员工
                         */
                        if (GeneralTool.isNotEmpty(kpiPlanStaffList)) {
                            for (KpiPlanStaff kpiPlanStaff : kpiPlanStaffList) {
                                KpiPlanStaff planStaff = BeanCopyUtils.objClone(kpiPlanStaff, KpiPlanStaff::new);
                                planStaff.setId(null);
                                planStaff.setFkKpiPlanId(kpiPlan.getId());
                                planStaff.setGmtModified(null);
                                planStaff.setGmtModifiedUser(null);
                                utilService.setCreateInfo(planStaff);

                                List<KpiPlanStaff> kpiPlanStaffQuery = kpiPlanStaffService.list(Wrappers.<KpiPlanStaff>lambdaQuery()
                                        .eq(KpiPlanStaff::getFkKpiPlanId, kpiPlan.getId())
                                        .eq(KpiPlanStaff::getFkStaffIdAdd, planStaff.getFkStaffIdAdd())
                                        .eq(KpiPlanStaff::getCountRole, planStaff.getCountRole())
                                        .eq(KpiPlanStaff::getCountMode, planStaff.getCountMode())
                                        .eq(KpiPlanStaff::getFkStaffId, planStaff.getFkStaffId())
                                        .eq(KpiPlanStaff::getViewOrder, planStaff.getViewOrder())
                                );
                                if (GeneralTool.isNotEmpty(kpiPlanStaffQuery)) { // 正常情况下只有一条数据
                                    planStaff = kpiPlanStaffQuery.get(0);
                                } else {
                                    kpiPlanStaffService.save(planStaff);
                                    copyStaffIdMap.put(kpiPlanStaff.getId(), planStaff.getId());
                                }

                                /**
                                 * 复制员工目标KPI值
                                 */
                                // 查询员工KPI目标
                                List<KpiPlanTarget> kpiPlanTargetList = kpiPlanTargetService.list(Wrappers.<KpiPlanTarget>lambdaQuery()
                                        .eq(KpiPlanTarget::getFkKpiPlanGroupItemId, kpiPlanGroupItem.getId())
                                        .eq(KpiPlanTarget::getFkKpiPlanStaffId, kpiPlanStaff.getId()));
                                if (GeneralTool.isNotEmpty(kpiPlanTargetList)) {
                                    for (KpiPlanTarget kpiPlanTarget : kpiPlanTargetList) {
                                        kpiPlanTarget.setId(null);
                                        kpiPlanTarget.setFkKpiPlanGroupItemId(item.getId());
                                        kpiPlanTarget.setFkKpiPlanStaffId(planStaff.getId());
                                        kpiPlanTarget.setGmtModified(null);
                                        kpiPlanTarget.setGmtModifiedUser(null);
                                        utilService.setCreateInfo(kpiPlanTarget);
                                    }
                                    kpiPlanTargetService.saveBatch(kpiPlanTargetList);
                                }
                            }
                        }
                    }
                }
            }
        }

        /**
         * 复制考核人员标签
         */
        if (GeneralTool.isNotEmpty(kpiPlanStaffList)) {
            Set<Long> kpiPlanStaffIds = kpiPlanStaffList.stream().map(KpiPlanStaff::getId).collect(Collectors.toSet());
            // 查询当前方案下的所有员工对应的标签
            List<KpiPlanStaffLabel> kpiPlanStaffLabelList = kpiPlanStaffLabelService.list(Wrappers.<KpiPlanStaffLabel>lambdaQuery()
                    .in(KpiPlanStaffLabel::getFkKpiPlanStaffId, kpiPlanStaffIds));
            if (GeneralTool.isNotEmpty(kpiPlanStaffLabelList)) {
                for (KpiPlanStaffLabel kpiPlanStaffLabel : kpiPlanStaffLabelList) {
                    kpiPlanStaffLabel.setId(null);
                    kpiPlanStaffLabel.setFkKpiPlanStaffId(copyStaffIdMap.get(kpiPlanStaffLabel.getFkKpiPlanStaffId()));
                    kpiPlanStaffLabel.setGmtModified(null);
                    kpiPlanStaffLabel.setGmtModifiedUser(null);
                    utilService.setCreateInfo(kpiPlanStaffLabel);
                }
                kpiPlanStaffLabelService.saveBatch(kpiPlanStaffLabelList);
            }
        }
        return kpiPlan.getId();
    }

    @Override
    public void exportKpiAgentRankExcel(KpiPlanStatisticsDto kpiPlanStatisticsDto) {
        Map<String, String> headerMap = RequestHeaderHandler.getHeaderMap();
        UserInfo user = SecureUtil.getUser();
        String locale = SecureUtil.getLocale();
        asyncExportService.asyncExportKpiAgentRankExcel(headerMap, locale, user, kpiPlanStatisticsDto);
    }

    /**
     * 获取KPI方案数据时间戳之和
     *
     * @param fkKpiPlanId kpi方案id
     * @return
     */
    @Override
    public JSONObject getSumTimeKpiData(Long fkKpiPlanId) {
        JSONObject result = new JSONObject();
        String key = CacheKeyConstants.KPI_PLAN_STATISTICS_KEY + fkKpiPlanId;
        if (getRedis.exists(key)) {
            result.put("status", 2);
            result.put("time", null);
        } else {
            String sumTimeKpiData = kpiPlanMapper.getSumTimeKpiData(fkKpiPlanId);
            result.put("status", 1);
            result.put("time", sumTimeKpiData);
        }
        return result;
    }

    @Override
    public void downloadTemplateFile(HttpServletResponse response) {
        String fileName = "KpiTemplate.xlsx";
        //设置文件路径
        response.setHeader("content-type", "application/octet-stream");
        response.setContentType("application/octet-stream");
        response.setCharacterEncoding("UTF-8");

        // 输出流
        OutputStream outputStream = null;
        InputStream inputStream = null;
        try {
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
            outputStream = response.getOutputStream();
            inputStream = TemplateExcelUtils.class.getClassLoader().getResourceAsStream("template/" + fileName);
            byte[] bytes = IOUtils.toByteArray(inputStream);
            outputStream.write(bytes);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (inputStream != null) {
                    inputStream.close();
                }
                if (outputStream != null) {
                    outputStream.flush();
                    outputStream.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseBo importKpiData(MultipartFile file, Long fkKpiPlanId) {
        if (GeneralTool.isEmpty(fkKpiPlanId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }

        InputStream inputStream = null;
        try {
            inputStream = file.getInputStream();
            ExcelReader reader = ExcelUtil.getReader(inputStream, 0);
            reader.addHeaderAlias("Group", "fkKpiPlanGroupName");
            reader.addHeaderAlias("Institution", "institution");
            reader.addHeaderAlias("Description", "description");
            reader.addHeaderAlias("Country", "country");
            reader.addHeaderAlias("Target", "targetEnrolled");
            // 读取文件信息
            List<KpiPlanGroupItemImportVo> kpiPlanGroupItemImportDtos = reader.read(0, 1, KpiPlanGroupItemImportVo.class);
            // 按组别名称进行分组
            Map<String, List<KpiPlanGroupItemImportVo>> groupItemMap = kpiPlanGroupItemImportDtos.stream()
                    .collect((Collectors.groupingBy(KpiPlanGroupItemImportVo::getFkKpiPlanGroupName)));
            // 获取当前KPi方案组别最大的view_order
            KpiPlanGroup maxView = kpiPlanGroupService.getOne(Wrappers.<KpiPlanGroup>lambdaQuery()
                    .select(KpiPlanGroup::getViewOrder)
                    .eq(KpiPlanGroup::getFkKpiPlanId, fkKpiPlanId)
                    .orderByDesc(KpiPlanGroup::getViewOrder)
                    .last("limit 1"));
            Integer maxViewOrder = GeneralTool.isEmpty(maxView) ? 0 : maxView.getViewOrder() + 1;

            // 批量插入组别明细数据
            List<KpiPlanGroupItem> kpiPlanGroupItemList = new ArrayList<>();

            for (Map.Entry<String, List<KpiPlanGroupItemImportVo>> entry : groupItemMap.entrySet()) {
                String groupName = entry.getKey();
                List<KpiPlanGroupItemImportVo> groupItemList = entry.getValue();

                // 组别信息
                KpiPlanGroup kpiPlanGroup = new KpiPlanGroup();
                kpiPlanGroup.setFkKpiPlanId(fkKpiPlanId);
                kpiPlanGroup.setGroupName(groupName);
                kpiPlanGroup.setViewOrder(maxViewOrder++);
                utilService.setCreateInfo(kpiPlanGroup);
                kpiPlanGroupService.save(kpiPlanGroup);

                // 组别明细信息
                for (int i = 0; i < groupItemList.size(); i++) {
                    KpiPlanGroupItemImportVo groupItem = groupItemList.get(i);
                    KpiPlanGroupItem kpiPlanGroupItem = new KpiPlanGroupItem();
                    kpiPlanGroupItem.setFkKpiPlanId(fkKpiPlanId);
                    kpiPlanGroupItem.setFkKpiPlanGroupId(kpiPlanGroup.getId());
                    kpiPlanGroupItem.setTargetEnrolled(groupItem.getTargetEnrolled());
                    StringJoiner sj = new StringJoiner("\n");
                    if (GeneralTool.isNotEmpty(groupItem.getInstitution())) {
                        sj.add(groupItem.getInstitution());
                    }
                    if (GeneralTool.isNotEmpty(groupItem.getDescription())) {
                        sj.add(groupItem.getDescription());
                    }
                    if (GeneralTool.isNotEmpty(groupItem.getCountry())) {
                        sj.add(groupItem.getCountry());
                    }
                    kpiPlanGroupItem.setDescription(sj.toString());
                    kpiPlanGroupItem.setViewOrder(i);
                    kpiPlanGroupItemList.add(kpiPlanGroupItem);
                }
            }
            if (GeneralTool.isNotEmpty(kpiPlanGroupItemList)) {
                kpiPlanGroupItemService.saveBatch(kpiPlanGroupItemList, DEFAULT_BATCH_SIZE);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseBo.error(ErrorCodeEnum.INVALID_PARAM.getCode(), "导入文件异常");
        } finally {
            try {
                if (inputStream != null) {
                    inputStream.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        ResponseBo responseBo = new ResponseBo(true);
        responseBo.setCode(ErrorCodeEnum.REQUEST_OK.getCode());
        responseBo.setMessage("数据导入成功");
        return responseBo;
    }

    @Override
    public List<KpiPlanDto> getKpiPlanSelect(String fkCompanyIds) {
        if (GeneralTool.isEmpty(fkCompanyIds)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        LambdaQueryWrapper<KpiPlan> wrapper = Wrappers.lambdaQuery();
        if (GeneralTool.isNotEmpty(fkCompanyIds)) {
            String[] companyIds = fkCompanyIds.split(",");
            wrapper.and(w -> {
                for (String id : companyIds) {
                    String trimmedId = id.trim();
                    w.or().apply("FIND_IN_SET({0}, fk_company_ids ) > 0", trimmedId);
                }
            });
        }
        List<KpiPlan> kpiPlans = kpiPlanMapper.selectList(wrapper);
        if (GeneralTool.isEmpty(kpiPlans)) {
            return Collections.emptyList();
        }
        List<KpiPlanDto> kpiPlanDtos = BeanCopyUtils.copyListProperties(kpiPlans, KpiPlanDto::new);
//        Set<Long> kpiPlanIds = kpiPlans.stream().map(KpiPlan::getId).collect(Collectors.toSet());
//        // 获取每个KPI方案下，所有的设置人与被设置人id
//        List<KpiPlanStaff> kpiPlanStaffs = kpiPlanStaffService.list(Wrappers.<KpiPlanStaff>lambdaQuery()
//                .in(KpiPlanStaff::getFkKpiPlanId, kpiPlanIds));
//        // key：kpi方案Id value：kpi方案下【设置人及被设置人及2者对应业务上司】
//        Map<Long, Set<Long>> staffIdsMap = this.getIsKpiPlanStatistics(kpiPlanStaffs);
//        // 返回当前登录人能够进入统计的KPI方案列表
//        kpiPlanDtos = kpiPlanDtos.stream().filter(kpiPlanDto -> {
//            Set<Long> statisticsStaffIds = staffIdsMap.get(kpiPlanDto.getId());
//            return GeneralTool.isNotEmpty(statisticsStaffIds) && statisticsStaffIds.contains(SecureUtil.getStaffId());
//        }).collect(Collectors.toList());
        return kpiPlanDtos;
    }

    /**
     * 定时任务，执行KPI方案统计
     */
    @Override
    public void kpiPlanStatistics() {
        // 获取开启定时任务的KPI方案
        List<KpiPlan> kpiPlanList = kpiPlanMapper.selectList(Wrappers.<KpiPlan>lambdaQuery()
                .eq(KpiPlan::getIsEnableScheduledCount, 1));
        asyncStatisticsService.batchAddKpiPlanTaskResult(kpiPlanList);
    }

    @Override
    public CompanyVo getKpiPlanCompanySelect(Long kpiPlanId) {
        if (GeneralTool.isEmpty(kpiPlanId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        CompanyVo data = null;
        KpiPlan kpiPlan = kpiPlanMapper.selectById(kpiPlanId);
        if(GeneralTool.isNotEmpty(kpiPlan)){
            // 1. 获取并处理公司ID字符串
            String companyIdsStr = kpiPlan.getFkCompanyIds();
            Set<Long> companyIdSet = Collections.emptySet();

            // 2. 转换公司ID为Long集合
            if (StringUtils.isNotBlank(companyIdsStr)) {
                companyIdSet = Arrays.stream(companyIdsStr.split(","))
                        .map(String::trim)
                        .filter(s -> !s.isEmpty())  // 过滤空字符串
                        .map(Long::parseLong)
                        .collect(Collectors.toSet());
            }
             data = permissionCenterClient.getCompanyVo().getData();
            Set<Long> finalCompanyIdSet = companyIdSet;
            List<CompanyVo> filteredChildCompanies = data.getChildCompanyDto().stream()
                    .filter(childCompany -> finalCompanyIdSet.contains(childCompany.getId()))
                    .collect(Collectors.toList());
            data.setChildCompanyDto(filteredChildCompanies);

            // 4. 如果顶级公司不在允许列表中，清空其属性(保留childCompanyDto)
            if (!companyIdSet.contains(data.getId())) {
                CompanyVo minimalParent = new CompanyVo();
                minimalParent.setId(data.getId()); // 保留ID
                minimalParent.setName(data.getName()); // 保留名称(可选)
                minimalParent.setChildCompanyDto(data.getChildCompanyDto()); // 保留过滤后的子公司

                // 清空其他属性
                minimalParent.setCompanyIcon(null);
                minimalParent.setFkParentCompanyId(null);
                minimalParent.setNum(null);
                minimalParent.setNameChn(null);
                minimalParent.setShortName(null);
                minimalParent.setShortNameChn(null);
                minimalParent.setViewOrder(null);
                minimalParent.setDepartmentData(null);

                data = minimalParent;
            }
        }
        return data;
    }

    @Override
    public KpiStaffIdAddVo findFkStaffIdAdd(KpiPlanStatisticsDto kpiPlanStatisticsVo) {
        KpiStaffIdAddVo kpiStaffIdAddVo = new KpiStaffIdAddVo();
        Long fkStaffIdAdd = 0L;
        kpiStaffIdAddVo.setFkStaffIdAdd(fkStaffIdAdd);
        kpiStaffIdAddVo.setView(true);
        // 指定人员的业务下属
        Set<Long> staffFollowerIds = Sets.newHashSet();
        Result<List<Long>> followerIdsResult = permissionCenterClient.getStaffFollowerIds(kpiPlanStatisticsVo.getRootFkStaffId());
        if (followerIdsResult.isSuccess() && GeneralTool.isNotEmpty(followerIdsResult.getData())) {
            List<Long> followerIds = followerIdsResult.getData();
            staffFollowerIds.addAll(followerIds);
        }
        staffFollowerIds.removeIf(Objects::isNull);
        // 获取考核人员列表（指定人员添加的考核人员）
        List<KpiPlanStaff> staffList = kpiPlanStaffService.list(Wrappers.<KpiPlanStaff>lambdaQuery()
                .eq(KpiPlanStaff::getFkKpiPlanId, kpiPlanStatisticsVo.getFkKpiPlanId())
                .and(wrapper -> wrapper
                        // 指定人员添加的考核人员
                        .or(w1 -> w1.eq(KpiPlanStaff::getFkStaffIdAdd,kpiPlanStatisticsVo.getRootFkStaffId() ))
                        // 指定人员自己（团队）
                        .or(w2 -> w2.eq(KpiPlanStaff::getFkStaffId, kpiPlanStatisticsVo.getRootFkStaffId())
                                .eq(KpiPlanStaff::getCountMode, ProjectExtraEnum.TEAM.key))
                )
                .orderByDesc(KpiPlanStaff::getCountMode)
                .orderByDesc(KpiPlanStaff::getViewOrder));
        // 获取考核人员列表（个人）
        if (GeneralTool.isEmpty(staffList)) {
            staffList = kpiPlanStaffService.list(Wrappers.<KpiPlanStaff>lambdaQuery()
                    .eq(KpiPlanStaff::getFkKpiPlanId, kpiPlanStatisticsVo.getFkKpiPlanId())
                    .and(wrapper -> wrapper
                            // 指定人员自己（团队）
                            .or(w2 -> w2.eq(KpiPlanStaff::getFkStaffId, kpiPlanStatisticsVo.getRootFkStaffId())
                                    .eq(KpiPlanStaff::getCountMode, ProjectExtraEnum.PERSONAGE.key))
                    )
                    .orderByDesc(KpiPlanStaff::getCountMode)
                    .orderByDesc(KpiPlanStaff::getViewOrder));
        }


        if (GeneralTool.isNotEmpty(staffList)) {
              fkStaffIdAdd = kpiPlanStatisticsVo.getRootFkStaffId();
            kpiStaffIdAddVo.setFkStaffIdAdd(fkStaffIdAdd);
            kpiStaffIdAddVo.setView(true);
            return kpiStaffIdAddVo;
        }

        if (GeneralTool.isEmpty(staffList)) {
            // 获取该KPI方案下所有考核人员
            List<KpiPlanStaff> kpiPlanAllStaffList = kpiPlanStaffService.list(Wrappers.<KpiPlanStaff>lambdaQuery()
                    .eq(KpiPlanStaff::getFkKpiPlanId, kpiPlanStatisticsVo.getFkKpiPlanId()));
            Set<Long> staffIds = kpiPlanAllStaffList.stream().map(KpiPlanStaff::getFkStaffId).collect(Collectors.toSet());
            // 获取根节点集合
            Set<Long> rootStaffIds = kpiPlanAllStaffList.stream()
                    .filter(staff -> !staffIds.contains(staff.getFkStaffIdAdd()))
                    .map(KpiPlanStaff::getFkStaffId)
                    .collect(Collectors.toSet());
            List<KpiPlanStaffVo> kpiPlanStaffDtoList = BeanCopyUtils.copyListProperties(kpiPlanAllStaffList, KpiPlanStaffVo::new);
            // 构建树形结构
            List<KpiPlanStaffTreeVo> kpiPlanStaffTreeDtoList = kpiPlanStaffService.buildTree(kpiPlanStaffDtoList, rootStaffIds);
            // 获取指定人员下属添加的考核人员列表，然后返回该列表的最小层级
            Integer minLevel = kpiPlanStaffService.getMinLevel(kpiPlanStaffTreeDtoList, staffFollowerIds);
            // 返回考核人员树最低层级的考核人员列表
            staffList = kpiPlanStaffService.getStaffTreeDtoByLevel(kpiPlanStaffTreeDtoList, minLevel);
        }


        if(GeneralTool.isEmpty(staffList)){
            fkStaffIdAdd = kpiPlanStatisticsVo.getRootFkStaffId();
            kpiStaffIdAddVo.setFkStaffIdAdd(fkStaffIdAdd);
            kpiStaffIdAddVo.setView(true);
            return kpiStaffIdAddVo;
        }

        Set<Long> staffIds = new HashSet<>();
        //目标设置人
        if(GeneralTool.isNotEmpty(staffList)){
            if (GeneralTool.isNotEmpty(staffList) && GeneralTool.isNotEmpty(staffFollowerIds)) {
                Set<Long> followerIdSet = new HashSet<>(staffFollowerIds);

                fkStaffIdAdd = staffList.stream()
                        .filter(staff -> followerIdSet.contains(staff.getFkStaffId()))
                        .map(KpiPlanStaff::getFkStaffIdAdd)
                        .findFirst()
                        .orElse(null);
            }
//          for(KpiPlanStaff staff:staffList){
//              for(Long staffId:staffFollowerIds){
//                  if(staff.getFkStaffId().equals(staffId)){
//                      fkStaffIdAdd =staff.getFkStaffIdAdd();
//                      break;
//                  }
//              }
//          }
        }

        //目标设置人上司
//        if (GeneralTool.isNotEmpty(fkStaffIdAdd)&&kpiPlanStatisticsVo.getRootFkStaffId().equals(fkStaffIdAdd)||staffFollowerIds.contains(fkStaffIdAdd)) {
//            return fkStaffIdAdd;
//        }
        //第一层考核人员上司
        if(GeneralTool.isNotEmpty(staffList)){
            staffIds = staffList.stream().map(KpiPlanStaff::getFkStaffId).collect(Collectors.toSet());
        }

        boolean hasFollower = staffIds.stream().anyMatch(staffFollowerIds::contains);
        if (hasFollower) {
            kpiStaffIdAddVo.setFkStaffIdAdd(fkStaffIdAdd);
            kpiStaffIdAddVo.setView(false);
            return kpiStaffIdAddVo;
        }
        //非考核人员和非目标设置人和上司
        return kpiStaffIdAddVo;
    }
}
