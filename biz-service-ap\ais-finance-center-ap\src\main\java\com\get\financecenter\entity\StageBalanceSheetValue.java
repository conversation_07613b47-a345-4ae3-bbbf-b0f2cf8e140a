package com.get.financecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@TableName("m_stage_balance_sheet_value")
public class StageBalanceSheetValue extends BaseEntity implements Serializable {

    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;

    @ApiModelProperty(value = "年")
    private Integer year;

    @ApiModelProperty(value = "月")
    private Integer month;

    @ApiModelProperty(value = "资产负债表类型Id")
    private Long fkBalanceSheetTypeId;

    @ApiModelProperty(value = "科目Id")
    private Long fkAccountingItemId;

    @ApiModelProperty(value = "加减方向：1/-1")
    private Integer directionValue;

    @ApiModelProperty(value = "期初数")
    private BigDecimal amountOpeningBalance;

    @ApiModelProperty(value = "借方发生额")
    private BigDecimal amountDr;

    @ApiModelProperty(value = "贷方发生额")
    private BigDecimal amountCr;

    @ApiModelProperty(value = "期末数")
    private BigDecimal amountClosingBalance;

    @ApiModelProperty(value = "是否累加：0否/1是")
    private Boolean isSum;

}
