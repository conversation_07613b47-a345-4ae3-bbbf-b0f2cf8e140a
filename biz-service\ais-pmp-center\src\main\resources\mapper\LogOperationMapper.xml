<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.get.pmpcenter.mapper.LogOperationMapper">

    <select id="selectProviderLogs" resultType="com.get.pmpcenter.vo.common.LogRecordVo">
        SELECT lo.fk_table_name,
        lo.fk_table_id,
        lo.operation_name,
        lo.operation_description,
        lo.gmt_create,
        lo.gmt_create_user,
        ipc.name AS planName
        FROM log_operation lo
        LEFT JOIN m_institution_provider_commission_plan ipc
        on ipc.id = lo.fk_table_id
        <where>
            lo.fk_table_name in ('m_institution_provider_commission_plan','m_institution_provider_contract')
            <if test="id != null ">
                AND lo.fk_institution_provider_contract_id =
                #{id}
            </if>
        </where>
        order by lo.gmt_create desc
    </select>

    <select id="selectAgentLogs" resultType="com.get.pmpcenter.vo.common.LogRecordVo">
        SELECT lo.fk_table_name,
        lo.fk_table_id,
        lo.operation_name,
        lo.operation_description,
        lo.gmt_create,
        lo.gmt_create_user,
        p.name AS planName
        FROM log_operation lo
        LEFT JOIN m_agent_commission_plan p
        on p.id = lo.fk_table_id
        <where>
            lo.fk_table_name = 'm_agent_commission_plan'
            <if test="id != null ">
                AND lo.fk_table_id =
                #{id}
            </if>
        </where>
        order by lo.gmt_create desc
    </select>

    <select id="commissionLogRecordPage" resultType="com.get.pmpcenter.vo.common.LogRecordVo">
        SELECT
        lo.id,
        lo.fk_table_name,
        lo.fk_table_id,
        lo.operation_name,
        lo.operation_description,
        lo.gmt_create,
        lo.gmt_create_user,
        CASE
        WHEN lo.fk_table_name = 'm_institution_provider_commission_plan' THEN ipc.name
        WHEN lo.fk_table_name = 'm_agent_commission_plan' THEN apc.name
        END AS planName
        FROM
        log_operation lo
        LEFT JOIN
        m_institution_provider_commission_plan ipc
        ON lo.fk_table_name = 'm_institution_provider_commission_plan' AND lo.fk_table_id = ipc.id
        LEFT JOIN
        m_agent_commission_plan apc
        ON lo.fk_table_name = 'm_agent_commission_plan' AND lo.fk_table_id = apc.id
        <where>
             lo.operation_description NOT LIKE '%所学校%'
            <if test="param.type != null and  param.type == 1">
                AND lo.fk_table_name = 'm_institution_provider_commission_plan'
                AND lo.operation_name in ('INSERT','UPDATE','DELETE')
            </if>
            <if test="param.type != null and param.type == 2">
                AND lo.fk_table_name = 'm_agent_commission_plan'
                AND lo.operation_name in ('INSERT','UPDATE','DELETE','UPDATE_PLAN','INSERT_PLAN','DELETE_PLAN')
            </if>
            AND (
            (lo.fk_table_name = 'm_institution_provider_commission_plan' AND lo.fk_table_id =
            #{param.providerCommissionPlanId})
            <if test="param.agentCommissionPlanId != null and param.agentCommissionPlanId > 0">
                OR
                (lo.fk_table_name = 'm_agent_commission_plan' AND lo.fk_table_id = #{param.agentCommissionPlanId})
            </if>
            )
        </where>
        order by lo.gmt_create desc
    </select>

</mapper>