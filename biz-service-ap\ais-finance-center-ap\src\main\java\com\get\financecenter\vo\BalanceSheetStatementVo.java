package com.get.financecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 资产负债表Vo
 */
@Data
public class BalanceSheetStatementVo {

    @ApiModelProperty(value = "资产Vo")
    private List<BalanceSheetStatementItemVo> assetsVoList;

    @ApiModelProperty(value = "资产期初数总计")
    private BigDecimal assetsAmountOpeningBalanceTotal;

    @ApiModelProperty(value = "资产借方发生额总计")
    private BigDecimal assetsAmountDrTotal;

    @ApiModelProperty(value = "资产贷方发生额总计")
    private BigDecimal assetsAmountCrTotal;

    @ApiModelProperty(value = "资产期末数总计")
    private BigDecimal assetsAmountClosingBalanceTotal;



    @ApiModelProperty(value = "负债Vo")
    private List<BalanceSheetStatementItemVo> liabilitiesVoList;

    @ApiModelProperty(value = "负债期初数合计")
    private BigDecimal liabilitiesAmountOpeningBalanceTotal;

    @ApiModelProperty(value = "负债借方发生额合计")
    private BigDecimal liabilitiesAmountDrTotal;

    @ApiModelProperty(value = "负债贷方发生额合计")
    private BigDecimal liabilitiesAmountCrTotal;

    @ApiModelProperty(value = "负债期末数合计")
    private BigDecimal liabilitiesAmountClosingBalanceTotal;




    @ApiModelProperty(value = "权益Vo")
    private List<BalanceSheetStatementItemVo> equityVoList;

    @ApiModelProperty(value = "未分配利润期初数")
    private BigDecimal undistributedProfitAmountOpeningBalance = BigDecimal.ZERO;

    @ApiModelProperty(value = "未分配利润借方发生额")
    private BigDecimal undistributedProfitAmountDr = BigDecimal.ZERO;

    @ApiModelProperty(value = "未分配利润贷方发生额")
    private BigDecimal undistributedProfitAmountCr = BigDecimal.ZERO;

    @ApiModelProperty(value = "未分配利润期末数")
    private BigDecimal undistributedProfitAmountClosingBalance = BigDecimal.ZERO;

    @ApiModelProperty(value = "权益期初数合计")
    private BigDecimal equityAmountOpeningBalanceTotal = BigDecimal.ZERO;

    @ApiModelProperty(value = "权益借方发生额合计")
    private BigDecimal equityAmountDrTotal;

    @ApiModelProperty(value = "权益贷方发生额合计")
    private BigDecimal equityAmountCrTotal;

    @ApiModelProperty(value = "权益期末数合计")
    private BigDecimal equityAmountClosingBalanceTotal;









    @ApiModelProperty(value = "负债及所有者权益总计期初数")
    private BigDecimal totalLiabilitiesAndEquityAmountOpeningBalance;

    @ApiModelProperty(value = "负债及所有者权益总计借方发生额")
    private BigDecimal totalLiabilitiesAndEquityAmountDr;

    @ApiModelProperty(value = "负债及所有者权益总计贷方发生额")
    private BigDecimal totalLiabilitiesAndEquityAmountCr;

    @ApiModelProperty(value = "负债及所有者权益总计期末数")
    private BigDecimal totalLiabilitiesAndEquityAmountClosingBalance;

}
