package com.get.pmpcenter.vo.institution;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/2/12  15:55
 * @Version 1.0
 * 供应商佣金计划详情
 */
@Data
public class ProviderCommissionPlanVo {

    @ApiModelProperty(value = "佣金计划Id")
    private Long id;

    @ApiModelProperty(value = "学校提供商Id")
    private Long fkInstitutionProviderId;

    @ApiModelProperty(value = "学校提供商合同Id")
    private Long fkInstitutionProviderContractId;

    @ApiModelProperty(value = "方案名称")
    private String name;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "方案有效时间（开始）")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startTime;

    @ApiModelProperty(value = "方案有效时间（结束）")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endTime;

    @ApiModelProperty(value = "有效时间是否无时间限制：0否/1是")
    private Integer isTimeless;

    @ApiModelProperty(value = "是否激活：0否/1是")
    private Integer isActive;

    @ApiModelProperty(value = "绑定学校数量")
    private Integer institutionCount = 0;

    @ApiModelProperty(value = "佣金方案适用学生说明-英文")
    private String territory;

    @ApiModelProperty(value = "佣金方案适用学生说明-中文")
    private String territoryChn;

    @ApiModelProperty(value = "方案摘要（用户内部查看）-英文")
    private String summary;

    @ApiModelProperty(value = "方案摘要（用户内部查看）-中文")
    private String summaryChn;

    @ApiModelProperty(value = "佣金方案专业说明-英文")
    private String course;

    @ApiModelProperty(value = "佣金方案专业说明（中文）")
    private String courseChn;

    @ApiModelProperty(value = "创建用户(登录账号)")
    private String gmtCreateUser;

    @ApiModelProperty(value = "佣金方案下的适用国家/区域规则说明")
    List<PlanTerritoryVo> planTerritoryList;

    @ApiModelProperty(value = "是否锁定：0否/1是")
    private Integer isLocked;

    @ApiModelProperty(value = "锁定权限:0-方案未锁定,没有锁定权限;1-方案未锁定,有锁定权限;2-方案已锁定,没有解锁权限;3-方案已锁定,有解锁权限")
    private Integer lockPermission;

    @ApiModelProperty(value = "审批状态：0未提交/1待审批/2通过/3拒绝")
    private Integer approvalStatus;

    @ApiModelProperty(value = "审核权限:0-方案未提交审核,没有权限提交审核;1-方案未提交审核,有权限提交审核;2-方案已提交审核但没有审核权限;3-方案已提交审核且有审核权限;")
    private Integer approvalPermission;

    @ApiModelProperty(value = "当前登录账号")
    private String currentLoginId;

    @ApiModelProperty(value = "审批中的审批类型(仅当方案在审批中才会有值):single-佣金明细审批;mass-批量审批")
    private String pendingApprovalType;

    @ApiModelProperty(value = "是否有佣金明细:true-有/false-无")
    private Boolean hasCommission = Boolean.FALSE;

    @ApiModelProperty(value = "创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date gmtCreate;

    @ApiModelProperty(value = "学校提供商名称")
    private String institutionProviderName;

    @ApiModelProperty(value = "是否续约中:0否/1是")
    private Integer isRenewal ;
}
