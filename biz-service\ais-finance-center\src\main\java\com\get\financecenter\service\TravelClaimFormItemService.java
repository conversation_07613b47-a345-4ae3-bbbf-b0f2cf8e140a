package com.get.financecenter.service;

import com.get.common.result.Page;
import com.get.financecenter.dto.SearchActivityDataDto;
import com.get.financecenter.vo.ActivityFinancialSummaryVo;
import com.get.financecenter.vo.TravelClaimFormAndItemVo;
import com.get.financecenter.vo.TravelClaimFormItemVo;
import java.util.List;
import javax.validation.Valid;

public interface TravelClaimFormItemService {
    /**
     * 表单明细对象
     *
     * @param id
     * @return
     */
    List<TravelClaimFormItemVo> getDtoByTravelClaimFormId(Long id);

    /**
     * 根据活动信息获取费用报销单子项
     * @param searchActivityDataDto
     * @return
     */
    List<TravelClaimFormItemVo> getDtoBySearchActivityDataDto(SearchActivityDataDto searchActivityDataDto);

    List<TravelClaimFormAndItemVo> getAllTravelClaimFormItemByActivityData(SearchActivityDataDto searchActivityDataDto);
    /**
     * 根据活动信息分页查询差旅费用报销单列表数据
     * @param searchActivityDataDto
     * @param page
     * @return
     */
    List<TravelClaimFormAndItemVo> getTravelClaimFormByActivityData(@Valid SearchActivityDataDto searchActivityDataDto, Page page);

    /**
     * 根据活动信息查询费用
     * @param searchActivityDataDto
     * @return
     */
    ActivityFinancialSummaryVo getActivityFinancialSummary(SearchActivityDataDto searchActivityDataDto);
}
