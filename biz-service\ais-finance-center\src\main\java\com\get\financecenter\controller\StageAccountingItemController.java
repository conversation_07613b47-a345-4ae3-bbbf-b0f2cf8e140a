package com.get.financecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ListResponseBo;
import com.get.common.result.ResponseBo;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.redis.lock.RedisLock;
import com.get.financecenter.dto.ReCalculateSubjectBalanceSummaryDto;
import com.get.financecenter.dto.StageAccountingItemDto;
import com.get.financecenter.service.StageAccountingItemService;
import com.get.financecenter.vo.StageAccountingItemVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "科目余额汇总表管理")
@RestController
@RequestMapping("finance/stageAccountingItem")
public class StageAccountingItemController {

    @Resource
    private StageAccountingItemService stageAccountingItemService;

    @ApiOperation(value = "生成并查询科目汇总表", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.ADD, description = "财务中心/财务报表/生成并查询科目余额汇总表")
    @PostMapping("createStageAccountingItem")
    @RedisLock(value = "fzh:createProfitAndLossStatement", waitTime = 30L)
    public ResponseBo createStageAccountingItem(@RequestBody @Validated StageAccountingItemDto stageAccountingItemDto) {
        List<StageAccountingItemVo> stageAccountingItemVo = stageAccountingItemService.createStageAccountingItem(stageAccountingItemDto);
        return new ListResponseBo(stageAccountingItemVo);
    }

    @ApiOperation(value = "重新统计科目余额汇总表", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.ADD, description = "财务中心/财务报表/重新统计科目余额汇总表")
    @PostMapping("reCalculateSubjectBalanceSummary")
    @RedisLock(value = "fzh:reCalculateSubjectBalanceSummary", waitTime = 60L)
    public ResponseBo reCalculateSubjectBalanceSummary(@RequestBody @Validated ReCalculateSubjectBalanceSummaryDto reCalculateSubjectBalanceSummaryDto) {
        stageAccountingItemService.reCalculateSubjectBalanceSummary(reCalculateSubjectBalanceSummaryDto);
        return ResponseBo.ok();
    }


}
