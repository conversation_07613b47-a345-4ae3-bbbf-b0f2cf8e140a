package com.get.financecenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.financecenter.dto.SearchActivityDataDto;
import com.get.financecenter.entity.TravelClaimFormItem;
import com.get.financecenter.vo.TravelClaimFormAndItemVo;
import java.math.BigDecimal;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface TravelClaimFormItemMapper extends BaseMapper<TravelClaimFormItem> {

    /**
     * 根据活动信息查询差旅费用明细
     * @param searchActivityDataDto
     * @param statusList
     * @return
     */
    List<TravelClaimFormAndItemVo> getAllTravelClaimFormItemByActivityData(@Param("searchActivityDataDto") SearchActivityDataDto searchActivityDataDto, @Param("statusList") List<Integer> statusList);

    /**
     * 根据活动信息查询差旅费用
     * @param searchActivityDataDto
     * @param pages
     * @return
     */
    List<TravelClaimFormAndItemVo> getTravelClaimFormActivityData(@Param("searchActivityDataDto") SearchActivityDataDto searchActivityDataDto, IPage<TravelClaimFormAndItemVo> pages);

    /**
     * 根据活动信息查询差旅费用总金额
     * @param searchActivityDataDto
     * @param statusList
     * @return
     */
    BigDecimal getClaimedEventCostByActivityData(@Param("searchActivityDataDto") SearchActivityDataDto searchActivityDataDto, @Param("statusList") List<Integer> statusList);

    String getCurrencyTypeByActivityData(@Param("searchActivityDataDto") SearchActivityDataDto searchActivityDataDto, @Param("statusList") List<Integer> statusList);
}