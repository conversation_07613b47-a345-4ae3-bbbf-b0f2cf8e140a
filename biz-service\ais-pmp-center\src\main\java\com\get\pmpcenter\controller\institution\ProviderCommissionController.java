package com.get.pmpcenter.controller.institution;

import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.secure.utils.SecureUtil;
import com.get.permissioncenter.vo.workbench.WorkbenchApprovalVo;
import com.get.pmpcenter.dto.agent.PmpWorkbenchApprovalDto;
import com.get.pmpcenter.dto.common.*;
import com.get.pmpcenter.dto.institution.*;
import com.get.pmpcenter.entity.InstitutionProviderCommissionPlanApproval;
import com.get.pmpcenter.enums.ApprovalTypeEnum;
import com.get.pmpcenter.mapper.InstitutionCenterMapper;
import com.get.pmpcenter.mapper.MajorLevelCustomMajorLevelMapper;
import com.get.pmpcenter.service.InstitutionProviderCommissionPlanService;
import com.get.pmpcenter.service.InstitutionProviderCommissionService;
import com.get.pmpcenter.service.InstitutionProviderContractApprovalService;
import com.get.pmpcenter.service.MajorLevelCustomService;
import com.get.pmpcenter.vo.common.*;
import com.get.pmpcenter.vo.institution.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 学校提供商合同管理
 */
@Slf4j
@Api(tags = "学校提供商合同佣金方案管理")
@RestController
@RequestMapping("/providerCommission")
public class ProviderCommissionController {

    @Autowired
    private InstitutionProviderCommissionPlanService commissionPlanService;
    @Autowired
    private MajorLevelCustomMajorLevelMapper majorLevelMapper;
    @Autowired
    private InstitutionProviderCommissionService commissionService;
    @Autowired
    private MajorLevelCustomService majorLevelCustomService;
    @Autowired
    private InstitutionProviderContractApprovalService contractApprovalService;
    @Autowired
    private InstitutionCenterMapper institutionCenterMapper;


    @ApiOperation(value = "新增编辑佣金方案")
    @PostMapping("/saveProviderCommissionPlan")
    public ResponseBo<String> saveProviderCommissionPlan(@RequestBody @Valid SaveProviderCommissionPlanDto commissionPlanDto) {
        commissionPlanService.saveProviderCommissionPlan(commissionPlanDto, Boolean.TRUE);
        String message = LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_SAVE_SUCCESS", "保存成功");
        return new ResponseBo<>(message);
    }

    @ApiOperation(value = "删除佣金方案")
    @PostMapping("/deleteProviderCommissionPlan")
    public ResponseBo<String> deleteProviderCommissionPlan(@RequestBody @Valid IdDto idDto) {
        commissionPlanService.deleteProviderCommissionPlan(idDto.getId(), Boolean.TRUE);
        String message = LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_SAVE_SUCCESS", "保存成功");
        return new ResponseBo<>(message);
    }

    @ApiOperation(value = "佣金方案详情", notes = "根据佣金方案id获取佣金方案详细信息")
    @GetMapping("/getProviderCommissionPlanDetail/{id}")
    public ResponseBo<ProviderCommissionPlanDetailVo> getProviderCommissionPlanDetail(@PathVariable("id") Long id) {
        return new ResponseBo<>(commissionPlanService.getProviderCommissionPlanDetail(id));
    }

    @ApiOperation(value = "学校列表", notes = "根据学校提供商id获取学校列表")
    @GetMapping("/getInstitutionList")
    public ResponseBo<List<InstitutionVo>> getInstitutionList(@ApiParam("学校提供商id") Long institutionProviderId) {
        return new ResponseBo<>(commissionPlanService.getInstitutionList(institutionProviderId));
    }

    @ApiOperation(value = "国家列表(适用地区列表)", notes = "国家列表(适用地区列表)")
    @GetMapping("/getCountryList")
    public ResponseBo<List<CountryVo>> getCountryList() {
        return new ResponseBo<>(commissionPlanService.getCountryList());
    }

    @ApiOperation(value = "课程等级列表", notes = "课程等级列表(可以查询全部以及通用和非通用)")
    @GetMapping("/majorLevelList")
    public ResponseBo<List<MajorLevelVo>> majorLevelList(@ApiParam("1:通用课程等级;0:自定义课程等级;不传查询全部课程等级") Integer isGeneral) {
        return new ResponseBo<>(majorLevelMapper.selectMajorLevel(isGeneral));
    }

    @ApiOperation(value = "课程等级树", notes = "课程等级树")
    @GetMapping("/getMajorLevelTree")
    public ResponseBo<List<MajorLevelTreeVo>> getMajorLevelTree() {
        return new ResponseBo<>(majorLevelCustomService.getMajorLevelTree());
    }

    @ApiOperation(value = "新增编辑佣金方案明细")
    @PostMapping("/saveProviderCommission")
    public ResponseBo<ProviderCommissionVo> saveProviderCommission(@RequestBody @Valid SaveProviderCommissionDto saveProviderCommissionDto) {
//        commissionService.saveProviderCommission(saveProviderCommissionDto, Boolean.TRUE);
//        String message = LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_SAVE_SUCCESS", "保存成功");
        return new ResponseBo<>(commissionService.saveProviderCommission(saveProviderCommissionDto, Boolean.TRUE));
    }

    @ApiOperation(value = "佣金方案明细列表", notes = "根据佣金方案id获取佣金方案明细列表信息")
    @GetMapping("/getProviderCommissionList")
    public ResponseBo<ProviderCommissionVo> getProviderCommissionList(@ApiParam("佣金方案ID") Long providerCommissionPlanId) {
        return new ResponseBo<>(commissionService.getProviderCommissionAndPermission(providerCommissionPlanId));
    }

    @ApiOperation(value = "获取最新佣金方案的适用地区规则", notes = "根据合同id获取最新佣金方案的适用地区规则")
    @GetMapping("/getLastPlanTerritory")
    public ResponseBo<List<PlanTerritoryDto>> getLastPlanTerritory(@ApiParam("佣金方案ID") Long contractId) {
        return new ResponseBo<>(commissionPlanService.getLastPlanTerritory(contractId));
    }

    @ApiOperation(value = "获取货货币列表", notes = "获取货货币列表")
    @GetMapping("/getCurrencyTypes")
    public ResponseBo<List<CurrencyTypeVo>> getCurrencyTypes() {
        List<CurrencyTypeVo> list = new ArrayList<>();
        list.add(new CurrencyTypeVo("%", "%"));
        list.addAll(majorLevelMapper.getCurrencyTypes(ProjectExtraEnum.PUBLIC_PMP.key.toString()));
        return new ResponseBo<>(list);
    }

    @ApiOperation(value = "复制佣金方案")
    @PostMapping("/copyProviderCommissionPlan")
    public ResponseBo<String> copyProviderCommissionPlan(@RequestBody @Valid SaveProviderCommissionPlanDto commissionPlanDto) {
        commissionPlanService.copyProviderCommissionPlan(commissionPlanDto);
        String message = LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_SAVE_SUCCESS", "保存成功");
        return new ResponseBo<>(message);
    }

    @ApiOperation(value = "佣金方案详情-包含佣金明细", notes = "根据提供商佣金方案id获取佣金方案详细信息(包含佣金明细)")
    @GetMapping("/getProviderCommissionPlanInfo")
    public ResponseBo<ProviderCommissionPlanInfoVo> getProviderCommissionPlanInfo(Long providerCommissionPlanId) {
        return new ResponseBo<>(commissionPlanService.getProviderCommissionPlanInfo(providerCommissionPlanId));
    }

    @ApiOperation(value = "锁定/解锁学校提供商佣金方案")
    @PostMapping("/lockPlan")
    public ResponseBo<String> lockPlan(@RequestBody @Valid LockPlanDto lockPlanDto) {
        commissionPlanService.lockPlan(lockPlanDto);
        String message = LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_SAVE_SUCCESS", "保存成功");
        return new ResponseBo<>(message);
    }

    @ApiOperation(value = "提交学校提供商佣金方案审批")
    @PostMapping("/submitProviderPlanApproval")
    public ResponseBo<String> submitProviderPlanApproval(@RequestBody @Valid SubmitPlanApprovalDto approvalDto) {
        commissionPlanService.submitProviderPlanApproval(approvalDto);
        String message = LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_SUBMIT_SUCCESS", "提交成功");
        return new ResponseBo<>(message);
    }

    @ApiOperation(value = "审批学校提供商佣金方案")
    @PostMapping("/approvalProviderPlan")
    public ResponseBo<String> approvalProviderPlan(@RequestBody @Valid ApprovalPlanDto approvalPlanDto) {
        commissionPlanService.approvalProviderPlan(approvalPlanDto);
        String message = LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_APPROVE_SUCCESS", "审批成功");
        return new ResponseBo<>(message);
    }

    @ApiOperation(value = "学校提供商佣金方案审批人列表", notes = "学校提供商佣金方案审批人列表")
    @GetMapping("/getStaffList")
    public ResponseBo<List<StaffVo>> getStaffList() {
        return new ResponseBo<>(contractApprovalService.getStaffList());
    }

    @ApiOperation(value = "学校提供商佣金方案审批记录列表", notes = "根据合同id获取学校提供商佣金方案审批记录列表")
    @GetMapping("/getApprovalList")
    public ResponseBo<List<InstitutionProviderCommissionPlanApproval>> getApprovalList(Long contractId, Long planId, String planName) {
        return new ResponseBo<>(commissionPlanService.getApprovalList(contractId, planId, planName));
    }

    @ApiOperation(value = "获取供应商合同下的佣金计划列表-包含国家/区域规则", notes = "获取供应商合同下的佣金计划列表-包含国家/区域规则")
    @GetMapping("/getProviderCommissionPlanAndTerritoryList")
    public ResponseBo<List<ProviderCommissionPlanVo>> getProviderCommissionPlanAndTerritoryList(Long contractId) {
        return new ResponseBo<>(commissionPlanService.getProviderCommissionPlanAndTerritoryList(contractId));
    }

    @ApiOperation(value = "获取批量审批列表", notes = "获取批量审批列表")
    @GetMapping("/getBatchApprovalList")
    public ResponseBo<List<BatchApprovalVo>> getBatchApprovalList(Long contractId) {
        return new ResponseBo<>(commissionPlanService.getBatchApprovalList(contractId));
    }

    @ApiOperation(value = "提交批量审批")
    @PostMapping("/submitBatchApproval")
    public ResponseBo<String> submitBatchApproval(@RequestBody @Valid BatchApprovalDto approvalDto) {
        commissionPlanService.submitBatchApproval(approvalDto);
        String message = LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_SUBMIT_SUCCESS", "提交成功");
        return new ResponseBo<>(message);
    }

    @ApiOperation(value = "审核批量提交")
    @PostMapping("/examineBatchApproval")
    public ResponseBo<String> examineBatchApproval(@RequestBody @Valid ExamineBatchApprovalDto examineBatchApprovalDto) {
        commissionPlanService.examineBatchApproval(examineBatchApprovalDto);
        String message = LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_APPROVE_SUCCESS", "审批成功");
        return new ResponseBo<>(message);
    }

    @ApiOperation(value = "获取工作台审核列表-分页")
    @PostMapping("/getPmpWorkbenchApprovalPage")
    public ResponseBo<WorkbenchApprovalVo> getPmpWorkbenchApprovalPage(@RequestBody SearchBean<PmpWorkbenchApprovalDto> page) {
        List<WorkbenchApprovalVo> list = commissionPlanService.getWorkbenchApprovalPage(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(list, p);
    }

    @ApiOperation(value = "根据学校获取方案列表")
    @PostMapping("/institutionPlanPage")
    public ResponseBo<InstitutionPlanVo> institutionPlanPage(@RequestBody SearchBean<IdDto> page) {
        List<InstitutionPlanVo> list = commissionPlanService.institutionPlanPage(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(list, p);
    }

    @ApiOperation(value = "审核类型列表-工作台", notes = "审核类型列表")
    @GetMapping("/approvalTypeList")
    public ResponseBo<List<EnumOptionVo>> approvalTypeList() {
        List<EnumOptionVo> list = Arrays.stream(ApprovalTypeEnum.values())
                .map(e -> new EnumOptionVo(e.getCode(), e.getMsg(), e.getEnMsg()))
                .collect(Collectors.toList());
        return new ResponseBo<>(list);
    }

    @ApiOperation(value = "上下架合同端佣金方案")
    @PostMapping("/updatePlanStatus")
    public ResponseBo<String> updatePlanStatus(@RequestBody @Valid UpdatePlanStatusDto statusDto) {
        commissionPlanService.updatePlanStatus(statusDto);
        if (statusDto.getIsActive().equals(1)) {
            String message = LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_PUBLISH_SUCCESS", "上架成功");
            return new ResponseBo<>(message);
        }
        String message = LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_UNPUBLISH_SUCCESS", "下架成功");
        return new ResponseBo<>(message);
    }

    @ApiOperation(value = "上下架合同端佣金方案明细")
    @PostMapping("/updateCommissionStatus")
    public ResponseBo<String> updateCommissionStatus(@RequestBody @Valid UpdateCommissionStatusDto statusDto) {
        commissionService.updateCommissionStatus(statusDto);
        if (statusDto.getIsActive().equals(1)) {
            String message = LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_PUBLISH_SUCCESS", "上架成功");
            return new ResponseBo<>(message);
        }
        String message = LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_UNPUBLISH_SUCCESS", "下架成功");
        return new ResponseBo<>(message);
    }

    @ApiOperation(value = "上一版本的佣金方案明细列表", notes = "根据佣金方案id获取上一版本的佣金方案明细列表")
    @GetMapping("/getOldVersionProviderCommission")
    public ResponseBo<ProviderCommissionVo> getOldVersionProviderCommission(@ApiParam("佣金方案ID") Long providerCommissionPlanId) {
        return new ResponseBo<>(commissionService.getOldVersionProviderCommission(providerCommissionPlanId));
    }

    @ApiOperation(value = "学校类型列表", notes = "学校类型列表")
    @GetMapping("/institutionTypeList")
    public ResponseBo<List<InstitutionTypeVo>> institutionTypeList() {
        return new ResponseBo<>(institutionCenterMapper.institutionTypeList());
    }

    @ApiOperation(value = "大区列表", notes = "大区列表")
    @GetMapping("/regionList")
    public ResponseBo<List<RegionVo>> regionList() {
        return new ResponseBo<>(institutionCenterMapper.regionList());
    }

    @ApiOperation(value = "更新方案续约中状态")
    @PostMapping("/updatePlanRenewalStatus")
    public ResponseBo<String> updatePlanRenewalStatus(@RequestBody @Valid UpdatePlanRenewalStatusDto renewalStatusDto) {
        commissionPlanService.updatePlanRenewalStatus(renewalStatusDto);
        String message = LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_SAVE_SUCCESS", "保存成功");
        return new ResponseBo<>(message);
    }
}
