package com.get.salecenter.service.impl;

import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.DateUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.entity.MajorLevel;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.institutioncenter.vo.InstitutionProviderVo;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.salecenter.dao.sale.KpiPlanGroupMapper;
import com.get.salecenter.dto.KpiPlanGroupDto;
import com.get.salecenter.dto.KpiPlanGroupSearchDto;
import com.get.salecenter.dto.KpiPlanStaffDto;
import com.get.salecenter.entity.*;
import com.get.salecenter.service.*;
import com.get.salecenter.vo.*;
import com.google.common.collect.Sets;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-16
 */
@Service
public class KpiPlanGroupServiceImpl extends ServiceImpl<KpiPlanGroupMapper, KpiPlanGroup> implements KpiPlanGroupService {

    @Resource
    private KpiPlanService kpiPlanService;
    @Resource
    private KpiPlanGroupMapper kpiPlanGroupMapper;

    @Resource
    private KpiPlanGroupItemService kpiPlanGroupItemService;

    @Resource
    private KpiPlanStaffService kpiPlanStaffService;

    @Resource
    private IInstitutionCenterClient institutionCenterClient;

    @Resource
    private KpiPlanTargetService kpiPlanTargetService;

    @Resource
    private IPermissionCenterClient permissionCenterClient;

    @Resource
    private UtilService utilService;

    @Override
    public KpiPlanGroupResultVo datas(KpiPlanGroupSearchDto searchVo) {
        if (GeneralTool.isEmpty(searchVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }
        Long fkKpiPlanId = searchVo.getFkKpiPlanId();
        if (GeneralTool.isEmpty(fkKpiPlanId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        // 国家过滤
//        Set<Long> filterCountryIdList = searchVo.getFkAreaCountryIdList();

        KpiPlanGroupResultVo kpiPlanGroupResultDto = new KpiPlanGroupResultVo();
        //查询方案信息
        KpiPlan kpiPlan = kpiPlanService.getById(fkKpiPlanId);
        if (GeneralTool.isEmpty(kpiPlan)) {
            return kpiPlanGroupResultDto;
        }
        kpiPlanGroupResultDto.setFkCompanyIds(kpiPlan.getFkCompanyIds());
        // 查询KPI方案下所有组别
        List<KpiPlanGroup> kpiPlanGroups = kpiPlanGroupMapper.selectList(Wrappers.<KpiPlanGroup>lambdaQuery()
                .eq(KpiPlanGroup::getFkKpiPlanId, fkKpiPlanId)
                .orderByDesc(KpiPlanGroup::getViewOrder));
        if (GeneralTool.isEmpty(kpiPlanGroups)) {
            return kpiPlanGroupResultDto;
        }

        // 查询组别下所有子项明细
        Set<Long> groupIds = kpiPlanGroups.stream().map(KpiPlanGroup::getId).collect(Collectors.toSet());
        List<KpiPlanGroupItem> groupItemList = com.google.common.collect.Lists.newArrayList();
        if (GeneralTool.isNotEmpty(groupIds)) {
            groupItemList = kpiPlanGroupItemService.list(Wrappers.<KpiPlanGroupItem>lambdaQuery()
                    .in(KpiPlanGroupItem::getFkKpiPlanGroupId, groupIds)
                    .orderByDesc(KpiPlanGroupItem::getViewOrder));
        }

        // 指定维度人员
        Long rootFkStaffId = searchVo.getRootFkStaffId();

        // 返回当前指定人员的目标设置人Id，用于前端页面的跳转
        KpiPlanStaff addStaff = kpiPlanStaffService.getOne(Wrappers.<KpiPlanStaff>lambdaQuery()
                .select(KpiPlanStaff::getFkStaffIdAdd)
                .eq(KpiPlanStaff::getFkKpiPlanId, fkKpiPlanId)
                .eq(KpiPlanStaff::getCountMode, ProjectExtraEnum.TEAM.key)
                .eq(KpiPlanStaff::getFkStaffId, rootFkStaffId)
                .last("limit 1"));
        if (GeneralTool.isNotEmpty(addStaff)) {
            kpiPlanGroupResultDto.setFkStaffIdAdd(addStaff.getFkStaffIdAdd());
        }

        // 指定人员的业务下属
        Set<Long> staffFollowerIds = Sets.newHashSet();
        Result<List<Long>> followerIdsResult = permissionCenterClient.getStaffFollowerIds(rootFkStaffId);
        if (followerIdsResult.isSuccess() && GeneralTool.isNotEmpty(followerIdsResult.getData())) {
            List<Long> followerIds = followerIdsResult.getData();
            staffFollowerIds.addAll(followerIds);
        }
        staffFollowerIds.removeIf(Objects::isNull);
        //staffFollowerIds.add(rootFkStaffId);
        // 获取考核人员列表（指定人员添加的考核人员）
        List<KpiPlanStaff> staffList = kpiPlanStaffService.list(Wrappers.<KpiPlanStaff>lambdaQuery()
                .eq(KpiPlanStaff::getFkKpiPlanId, fkKpiPlanId)
                .and(wrapper -> wrapper
                        // 指定人员添加的考核人员
                        .or(w1 -> w1.eq(KpiPlanStaff::getFkStaffIdAdd, rootFkStaffId))
                        // 指定人员自己（团队）
                        .or(w2 -> w2.eq(KpiPlanStaff::getFkStaffId, rootFkStaffId)
                                .eq(KpiPlanStaff::getCountMode, ProjectExtraEnum.TEAM.key))
                )
                .orderByDesc(KpiPlanStaff::getCountMode)
                .orderByDesc(KpiPlanStaff::getViewOrder));

        if(GeneralTool.isEmpty(staffList)){
            staffList = kpiPlanStaffService.list(Wrappers.<KpiPlanStaff>lambdaQuery()
                    .eq(KpiPlanStaff::getFkKpiPlanId, fkKpiPlanId)
                    .and(wrapper -> wrapper
                            // 指定人员添加的考核人员
                            .or(w1 -> w1.eq(KpiPlanStaff::getFkStaffIdAdd, rootFkStaffId))
                            // 指定人员自己（个人）
                            .or(w2 -> w2.eq(KpiPlanStaff::getFkStaffId, rootFkStaffId)
                                    .eq(KpiPlanStaff::getCountMode, ProjectExtraEnum.PERSONAGE.key))
                    )
                    .orderByDesc(KpiPlanStaff::getCountMode)
                    .orderByDesc(KpiPlanStaff::getViewOrder));
        }


        if (GeneralTool.isNotEmpty(staffList)) { // 能找到考核人员，过滤组别明细
            /**
             * 过滤组别明细
             */
            if (GeneralTool.isNotEmpty(groupItemList)) {
                Set<Long> groupItemIds = groupItemList.stream().map(KpiPlanGroupItem::getId).collect(Collectors.toSet());
                // 查询当前指定人员（团队）是否有KPI目标值
                List<KpiPlanStaff> kpiPlanStaffs = kpiPlanStaffService.list(Wrappers.<KpiPlanStaff>lambdaQuery()
                        .eq(KpiPlanStaff::getFkKpiPlanId, fkKpiPlanId)
                        .eq(KpiPlanStaff::getFkStaffId, rootFkStaffId)
                        .eq(KpiPlanStaff::getCountMode, ProjectExtraEnum.TEAM.key));
                List<KpiPlanTarget> kpiPlanTargets = Lists.newArrayList();
                if (GeneralTool.isNotEmpty(kpiPlanStaffs)) {
                    Set<Long> kpiPlanStaffIds = kpiPlanStaffs.stream().map(KpiPlanStaff::getId).collect(Collectors.toSet());
                    kpiPlanTargets = kpiPlanTargetService.list(Wrappers.<KpiPlanTarget>lambdaQuery()
                            .in(KpiPlanTarget::getFkKpiPlanGroupItemId, groupItemIds)
                            .in(KpiPlanTarget::getFkKpiPlanStaffId, kpiPlanStaffIds)
                            .ne(KpiPlanTarget::getTargetEnrolled, 0));
                }

                if (GeneralTool.isNotEmpty(kpiPlanTargets)) { // 当前指定人员（团队）有KPI目标值时才去做组别明细的过滤
                    // 需要显示的组别明细id
                    Set<Long> showGroupItemIds = kpiPlanTargets.stream().map(KpiPlanTarget::getFkKpiPlanGroupItemId).collect(Collectors.toSet());
                    // 剔除不需要显示的组别明细
                    groupItemList = groupItemList.stream().filter(item -> showGroupItemIds.contains(item.getId())).collect(Collectors.toList());
                    // 剔除没有显示组别明细的组别
                    Set<Long> showGroupIds = groupItemList.stream().map(KpiPlanGroupItem::getFkKpiPlanGroupId).collect(Collectors.toSet());
                    kpiPlanGroups = kpiPlanGroups.stream().filter(group -> showGroupIds.contains(group.getId())).collect(Collectors.toList());
                    if (GeneralTool.isEmpty(kpiPlanGroups)) {
                        return kpiPlanGroupResultDto;
                    }
                }
            }
        } else { // 没有找到考核人员，返回考核人员树最低层级的考核人员列表
            // 获取该KPI方案下所有考核人员
            List<KpiPlanStaff> kpiPlanAllStaffList = kpiPlanStaffService.list(Wrappers.<KpiPlanStaff>lambdaQuery()
                    .eq(KpiPlanStaff::getFkKpiPlanId, fkKpiPlanId));
            if (GeneralTool.isNotEmpty(kpiPlanAllStaffList)) {
                Set<Long> staffIds = kpiPlanAllStaffList.stream().map(KpiPlanStaff::getFkStaffId).collect(Collectors.toSet());
                // 获取根节点集合
                Set<Long> rootStaffIds = kpiPlanAllStaffList.stream()
                        .filter(staff -> !staffIds.contains(staff.getFkStaffIdAdd()))
                        .map(KpiPlanStaff::getFkStaffId)
                        .collect(Collectors.toSet());
                List<KpiPlanStaffVo> kpiPlanStaffDtoList = BeanCopyUtils.copyListProperties(kpiPlanAllStaffList, KpiPlanStaffVo::new);
                // 构建树形结构
                List<KpiPlanStaffTreeVo> kpiPlanStaffTreeDtoList = kpiPlanStaffService.buildTree(kpiPlanStaffDtoList, rootStaffIds);
                // 获取指定人员下属添加的考核人员列表，然后返回该列表的最小层级
                Integer minLevel = kpiPlanStaffService.getMinLevel(kpiPlanStaffTreeDtoList, staffFollowerIds);
                // 返回考核人员树最低层级的考核人员列表
                 staffList = kpiPlanStaffService.getStaffTreeDtoByLevel(kpiPlanStaffTreeDtoList, minLevel);
            }
        }

        // 组装考核人员相关信息
        List<KpiPlanStaffVo> kpiPlanStaffDtos = Lists.newArrayList();
        if (GeneralTool.isNotEmpty(staffList)) {
            // 排序
            staffList = staffList.stream()
                    .sorted(Comparator.comparing(KpiPlanStaff::getCountMode, Comparator.reverseOrder())
                            .thenComparing(KpiPlanStaff::getViewOrder, Comparator.reverseOrder()))
                    .collect(Collectors.toList());
            // 找到指定人员（团队）
            Optional<KpiPlanStaff> specificStaff = staffList.stream()
                    .filter(staff -> Objects.equals(staff.getFkStaffId(), rootFkStaffId)
                            && Objects.equals(staff.getCountMode(), ProjectExtraEnum.TEAM.key))
                    .findFirst();
            // 如果找到了找到指定人员（团队），则将其移动到第一个位置
            if (specificStaff.isPresent()) {
                KpiPlanStaff targetStaff = specificStaff.get();
                staffList.remove(targetStaff);
                staffList.add(0, targetStaff);
            }

            Set<Long> fkStaffIds = staffList.stream().map(KpiPlanStaff::getFkStaffId).collect(Collectors.toSet());
            Map<Long, String> staffNamesMap = permissionCenterClient.getStaffChnNameByIds(fkStaffIds);
            for (KpiPlanStaff kpiPlanStaff : staffList) {
                KpiPlanStaffVo kpiPlanStaffDto = BeanCopyUtils.objClone(kpiPlanStaff, KpiPlanStaffVo::new);
                kpiPlanStaffDto.setStaffName(staffNamesMap.get(kpiPlanStaffDto.getFkStaffId()));
                kpiPlanStaffDto.setCountRoleName(ProjectExtraEnum.getValueByKey(kpiPlanStaffDto.getCountRole(), ProjectExtraEnum.COUNT_ROLE));
                kpiPlanStaffDto.setCountModeName(ProjectExtraEnum.getValueByKey(kpiPlanStaffDto.getCountMode(), ProjectExtraEnum.COUNT_MODE));
                kpiPlanStaffDtos.add(kpiPlanStaffDto);
            }
            //只看自己和自己下属的数据
           // kpiPlanStaffDtos = kpiPlanStaffDtos.stream().filter(staff -> staffFollowerIds.contains(staff.getFkStaffId())).collect(Collectors.toList());
            kpiPlanGroupResultDto.setKpiPlanStaffDtoList(kpiPlanStaffDtos);
        }

        List<KpiPlanGroupVo> kpiPlanGroupDtos = BeanCopyUtils.copyListProperties(kpiPlanGroups, KpiPlanGroupVo::new);
        List<KpiPlanGroupOrItemVo> groupOrItemDtos = new ArrayList<>();
        if (GeneralTool.isNotEmpty(groupItemList)) {
            // 国家过滤
//            if (GeneralTool.isNotEmpty(filterCountryIdList)) {
//                itemList = itemList.stream().filter(item -> {
//                    boolean flag = false;
//                    // 国家维度
//                    if (GeneralTool.isNotEmpty(item.getFkAreaCountryIdKpi())) {
//                        flag = filterCountryIdList.contains(item.getFkAreaCountryIdKpi());
//                    }
//                    // 提供商维度
//                    if (GeneralTool.isNotEmpty(item.getFkAreaCountryIds())) {
//                        Set<Long> itemCountryIds = Arrays.stream(item.getFkAreaCountryIds().split(","))
//                                .map(Long::valueOf)
//                                .collect(Collectors.toSet());
//                        if (GeneralTool.isNotEmpty(item.getCountryIncludeType())) {
//                            if (item.getCountryIncludeType().equals(0)) { // 剔除的国家
//                                flag = !itemCountryIds.containsAll(filterCountryIdList);
//                            }
//                            if (item.getCountryIncludeType().equals(1)) { // 包含的国家
//                                // itemCountryIds中只要有一个元素满足条件，则返回true；否则返回false。
//                                flag = itemCountryIds.stream().anyMatch(filterCountryIdList::contains);
//                            }
//                        }
//                    } else { // 代表所有国家
//                        flag = true;
//                    }
//                    return flag;
//                }).collect(Collectors.toList());
//            }

            // 设置子项相关属性名称
            List<KpiPlanGroupItemVo> kpiPlanGroupItemDtos = getKpiPlanGroupItemDtos(groupItemList, kpiPlanStaffDtos);
            Map<Long, List<KpiPlanGroupItemVo>> itemMap = kpiPlanGroupItemDtos.stream().collect(Collectors.groupingBy(KpiPlanGroupItemVo::getFkKpiPlanGroupId));
            kpiPlanGroupDtos.forEach(g -> {
                KpiPlanGroupOrItemVo groupOrItemDto = BeanCopyUtils.objClone(g, KpiPlanGroupOrItemVo::new);
                groupOrItemDtos.add(groupOrItemDto);
                List<KpiPlanGroupItemVo> itemDtos = itemMap.get(g.getId());
                if(GeneralTool.isNotEmpty(itemDtos)){
                    itemDtos.forEach(i->{
                        KpiPlanGroupOrItemVo groupOrItemDto2 = BeanCopyUtils.objClone(i, KpiPlanGroupOrItemVo::new);
                        groupOrItemDtos.add(groupOrItemDto2);
                    });
                }

            });
        }

        if(GeneralTool.isEmpty(groupOrItemDtos)){
            List<KpiPlanGroupOrItemVo> kpiPlanGroupOrItemVos = BeanCopyUtils.copyListProperties(kpiPlanGroupDtos, KpiPlanGroupOrItemVo::new);
            kpiPlanGroupResultDto.setKpiPlanGrouporItemDtoList(kpiPlanGroupOrItemVos);
        }else{
            kpiPlanGroupResultDto.setKpiPlanGrouporItemDtoList(groupOrItemDtos);
        }
        return kpiPlanGroupResultDto;
    }


    /**
     * 设置组别明细相关属性内容
     *
     * @param groupItemList    组别明细
     * @param kpiPlanStaffDtos 考核人员列表
     * @return
     */
    private List<KpiPlanGroupItemVo> getKpiPlanGroupItemDtos(List<KpiPlanGroupItem> groupItemList,
                                                              List<KpiPlanStaffVo> kpiPlanStaffDtos) {
        //查询所有子项的学校提供商
        Map<Long, String> providerNameMap = new HashMap<>();
        Set<Long> fkInstitutionProviderIds = groupItemList.stream().map(KpiPlanGroupItem::getFkInstitutionProviderId).collect(Collectors.toSet());
        if (GeneralTool.isNotEmpty(fkInstitutionProviderIds)) {
            Result<Map<Long, InstitutionProviderVo>> providerMapResult = institutionCenterClient.getInstitutionProviderMapByIds(fkInstitutionProviderIds);
            if (providerMapResult.isSuccess() && providerMapResult.getData() != null) {
                Map<Long, InstitutionProviderVo> data = providerMapResult.getData();
                for (Map.Entry<Long, InstitutionProviderVo> entry : data.entrySet()) {
                    InstitutionProviderVo institutionProviderVo = entry.getValue();
                    providerNameMap.put(entry.getKey(), institutionProviderVo.getName() + "（" + institutionProviderVo.getNameChn() + "）");
                }
            }
        }

        //查询所有子项的专业等级、申请国家、剔除提供商下的学校
        Map<Long, String> majorLevelNameMap = new HashMap<>();
        Map<Long, String> areaCountryNameMap = new HashMap<>();
        Map<Long, String> institutionNameMap = new HashMap<>();
        Set<Long> fkMajorLevelIds = new HashSet<>();
        Set<Long> fkAreaCountryIds = new HashSet<>();
        Set<Long> fkInstitutionIds = new HashSet<>();
        for (KpiPlanGroupItem item : groupItemList) {
            //专业等级
            if(GeneralTool.isNotEmpty(item.getFkMajorLevelIds())){
                String[] split = item.getFkMajorLevelIds().split(",");
                for (String s : split){
                    fkMajorLevelIds.add(Long.valueOf(s));
                }
            }
            //申请国家
            if(GeneralTool.isNotEmpty(item.getFkAreaCountryIds())){
                String[] split = item.getFkAreaCountryIds().split(",");
                for (String s : split){
                    fkAreaCountryIds.add(Long.valueOf(s));
                }
            }
            // 增加国家维度的统计
            if (GeneralTool.isNotEmpty(item.getFkAreaCountryIdsKpi())) {
                // 按逗号分割字符串
                String[] ids = item.getFkAreaCountryIdsKpi().split(",");
                for (String idStr : ids) {
                        // 去除空格并转换为Long
                        Long id = Long.parseLong(idStr.trim());
                        // 使用add方法添加到Set
                        fkAreaCountryIds.add(id);
                }

            }
            //剔除提供商下的学校
            if(GeneralTool.isNotEmpty(item.getFkInstitutionIds())){
                String[] split = item.getFkInstitutionIds().split(",");
                for (String s : split){
                    fkInstitutionIds.add(Long.valueOf(s));
                }
            }
        }

        if (GeneralTool.isNotEmpty(fkMajorLevelIds)) {
            Result<Map<Long, MajorLevel>> result = institutionCenterClient.getMajorLevelByIds(fkMajorLevelIds);
            if (result.isSuccess()) {
                Map<Long, MajorLevel> data = result.getData();
                for (Map.Entry<Long, MajorLevel> entry : data.entrySet()) {
                    MajorLevel majorLevel = entry.getValue();
                    majorLevelNameMap.put(entry.getKey(), majorLevel.getLevelName() + "（" + majorLevel.getLevelNameChn() + "）");
                }
            }
        }
        if(GeneralTool.isNotEmpty(fkAreaCountryIds)){
            Result<Map<Long, String>> countryNameResult = institutionCenterClient.getCountryFullNamesByIds(fkAreaCountryIds);
            if (countryNameResult.isSuccess() && countryNameResult.getData() != null) {
                areaCountryNameMap = countryNameResult.getData();
            }
        }
        if(GeneralTool.isNotEmpty(fkInstitutionIds)){
            Result<Map<Long, String>> institutionNameResult = institutionCenterClient.getInstitutionNamesByIds(fkInstitutionIds);
            if (institutionNameResult.isSuccess() && institutionNameResult.getData() != null) {
                institutionNameMap = institutionNameResult.getData();
            }
        }

        //查询所有子项对应的KPI目标设置
        Set<Long> itemIds = groupItemList.stream().map(KpiPlanGroupItem::getId).collect(Collectors.toSet());
        List<KpiPlanTarget> kpiPlanTargets = kpiPlanTargetService.list(Wrappers.<KpiPlanTarget>lambdaQuery()
                .in(KpiPlanTarget::getFkKpiPlanGroupItemId, itemIds));

        List<KpiPlanGroupItemVo> kpiPlanGroupItemVos = BeanCopyUtils.copyListProperties(groupItemList, KpiPlanGroupItemVo::new);
        for (KpiPlanGroupItemVo itemDto : kpiPlanGroupItemVos) {
            //学校/集团，如果提供商名称为空，则显示国家名称（国家维度统计）
            if (GeneralTool.isNotEmpty(itemDto.getFkInstitutionProviderId())) {
                itemDto.setFkInstitutionProviderName(providerNameMap.get(itemDto.getFkInstitutionProviderId()));
            } else {
                // 将逗号分隔的字符串拆分为多个ID，并获取对应的名称
                if (GeneralTool.isNotEmpty(itemDto.getFkAreaCountryIdsKpi())) {
                    Map<Long, String> finalAreaCountryNameMap = areaCountryNameMap;
                    String names = Arrays.stream(itemDto.getFkAreaCountryIdsKpi().split(","))
                            .map(String::trim)
                            .filter(s -> !s.isEmpty())
                            .map(id -> finalAreaCountryNameMap.getOrDefault(Long.parseLong(id), ""))
                            .collect(Collectors.joining(", "));

                    itemDto.setFkInstitutionProviderName(names);
                }

//                if (GeneralTool.isNotEmpty(itemDto.getFkAreaCountryIdKpi())) {
//                    itemDto.setFkInstitutionProviderName(areaCountryNameMap.get(itemDto.getFkAreaCountryIdKpi()));
//                }
            }

            //课程等级
            if(GeneralTool.isNotEmpty(itemDto.getFkMajorLevelIds())){
                itemDto.setFkMajorLevelNames(getNameSub(itemDto.getFkMajorLevelIds(),majorLevelNameMap));
            }

            //国家
            if(GeneralTool.isNotEmpty(itemDto.getFkAreaCountryIds())){
                if(GeneralTool.isNotEmpty(itemDto.getCountryIncludeType())){
                    if(itemDto.getCountryIncludeType().equals(0)){
                        itemDto.setFkAreaCountryNames("不包含国家：" + getNameSub(itemDto.getFkAreaCountryIds(),areaCountryNameMap));
                    }
                    if(itemDto.getCountryIncludeType().equals(1)){
                        itemDto.setFkAreaCountryNames(getNameSub(itemDto.getFkAreaCountryIds(),areaCountryNameMap));
                    }
                }

            }else{
                itemDto.setFkAreaCountryNames("所有国家");
                if (GeneralTool.isNotEmpty(itemDto.getFkAreaCountryIdsKpi())) {
                    // 当是以国家维度时，不显示名称
                    itemDto.setFkAreaCountryNames("");
                }
            }

            //剔除学校
            if(GeneralTool.isNotEmpty(itemDto.getFkInstitutionIds())){
                if(GeneralTool.isNotEmpty(itemDto.getInstitutionIncludeType())){
                    if(itemDto.getInstitutionIncludeType().equals(0)){
                        itemDto.setFkInstitutionIdsExcludingNames("不包含学校："+getNameSub(itemDto.getFkInstitutionIds(),institutionNameMap));
                    }
                    if(itemDto.getInstitutionIncludeType().equals(1)){
                        itemDto.setFkInstitutionIdsExcludingNames(getNameSub(itemDto.getFkInstitutionIds(),institutionNameMap));
                    }
                }
            }

            //时间设定
            StringBuilder timeSet = new StringBuilder();
            if (GeneralTool.isNotEmpty(itemDto.getStudentCreateTimeStart()) && GeneralTool.isNotEmpty(itemDto.getStudentCreateTimeEnd())) {
                timeSet.append("学生创建时间：")
                        .append(DateUtil.formatDate(itemDto.getStudentCreateTimeStart()))
                        .append("至")
                        .append(DateUtil.formatDate(itemDto.getStudentCreateTimeEnd()));
            }
            if (GeneralTool.isNotEmpty(itemDto.getIntakeTimeStart()) && GeneralTool.isNotEmpty(itemDto.getIntakeTimeEnd())) {
                if (timeSet.length() > 0) {
                    timeSet.append("，");
                }
                timeSet.append("入学时间：")
                        .append(DateUtil.formatDate(itemDto.getIntakeTimeStart()))
                        .append("至")
                        .append(DateUtil.formatDate(itemDto.getIntakeTimeEnd()));
            }
            if (GeneralTool.isNotEmpty(itemDto.getOfferItemCreateTimeStart()) && GeneralTool.isNotEmpty(itemDto.getOfferItemCreateTimeEnd())) {
                if (timeSet.length() > 0) {
                    timeSet.append("，");
                }
                timeSet.append("申请创建时间：")
                        .append(DateUtil.formatDate(itemDto.getOfferItemCreateTimeStart()))
                        .append("至")
                        .append(DateUtil.formatDate(itemDto.getOfferItemCreateTimeEnd()));
            }
            if (GeneralTool.isNotEmpty(itemDto.getStepTimeStart()) && GeneralTool.isNotEmpty(itemDto.getStepTimeEnd())) {
                if (timeSet.length() > 0) {
                    timeSet.append("，");
                }
                timeSet.append("业务步骤登记时间：")
                        .append(DateUtil.formatDate(itemDto.getStepTimeStart()))
                        .append("至")
                        .append(DateUtil.formatDate(itemDto.getStepTimeEnd()));
            }
            itemDto.setTimeSet(timeSet.toString());

            //KPI小计目标
            List<KpiPlanTargetVo> kpiPlanTargetVos = new ArrayList<>();
            if(GeneralTool.isNotEmpty(kpiPlanTargets)){
                kpiPlanTargetVos = BeanCopyUtils.copyListProperties(kpiPlanTargets, KpiPlanTargetVo::new);
                //小计
                int targetSubtotal = kpiPlanTargetVos.stream().filter(kpiPlanTargetDto -> kpiPlanTargetDto.getFkKpiPlanGroupItemId().equals(itemDto.getId()))
                        .mapToInt(i -> GeneralTool.isEmpty(i.getTargetEnrolled()) ? 0 : i.getTargetEnrolled()).sum();
                itemDto.setKpiSubtotal(targetSubtotal +"（" + itemDto.getTargetEnrolled() + "）");
            }
            //员工KPI
            List<KpiPlanTargetVo> kpiPlanTargetDtoResultList = new ArrayList<>();
            if (GeneralTool.isNotEmpty(kpiPlanStaffDtos)) {
                for (KpiPlanStaffVo staffDto : kpiPlanStaffDtos) {
                    KpiPlanTargetVo targetDto = new KpiPlanTargetVo();
                    targetDto.setTargetEnrolled(0);
                    targetDto.setFkKpiPlanGroupItemId(itemDto.getId());
                    targetDto.setFkKpiPlanStaffId(staffDto.getId());
                    if (GeneralTool.isNotEmpty(kpiPlanTargetVos)) {
                        List<KpiPlanTargetVo> targetList = kpiPlanTargetVos.stream()
                                // fkKpiPlanStaffId，唯一
                                .filter(k -> k.getFkKpiPlanGroupItemId().equals(itemDto.getId())
                                        && k.getFkKpiPlanStaffId().equals(staffDto.getId()))
                                .collect(Collectors.toList());
                        if (GeneralTool.isNotEmpty(targetList)) {
                            targetDto = targetList.get(0);
                        }
                    }
                    // 前端需要的考核人员信息
                    targetDto.setFkStaffId(staffDto.getFkStaffId());
                    targetDto.setCountMode(staffDto.getCountMode());
                    kpiPlanTargetDtoResultList.add(targetDto);
                }
            }
            itemDto.setKpiPlanTargetDtoList(kpiPlanTargetDtoResultList);

        }
        return kpiPlanGroupItemVos;
    }

    private String getNameSub(String ids, Map<Long, String> targetMap) {
        String[] split = ids.split(",");
        StringJoiner sj = new StringJoiner(",");
        for (String s : split) {
            sj.add(targetMap.get(Long.valueOf(s)));
        }
        return sj.toString();
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long addKpiPlanGroup(KpiPlanGroupDto kpiPlanGroupDto) {
        if (GeneralTool.isEmpty(kpiPlanGroupDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        KpiPlanGroup kpiPlanGroup = BeanCopyUtils.objClone(kpiPlanGroupDto, KpiPlanGroup::new);
        //获取最大排序
        KpiPlanGroup maxView = kpiPlanGroupMapper.selectOne(Wrappers.<KpiPlanGroup>lambdaQuery()
                .select(KpiPlanGroup::getViewOrder)
                .eq(KpiPlanGroup::getFkKpiPlanId, kpiPlanGroupDto.getFkKpiPlanId())
                .orderByDesc(KpiPlanGroup::getViewOrder)
                .last("limit 1"));
        Integer viewOrder = GeneralTool.isEmpty(maxView) ? 0 : maxView.getViewOrder() + 1;
        kpiPlanGroup.setViewOrder(viewOrder);
        utilService.setCreateInfo(kpiPlanGroup);
        kpiPlanGroupMapper.insert(kpiPlanGroup);
        return kpiPlanGroup.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        List<KpiPlanGroupItem> list = kpiPlanGroupItemService.list(Wrappers.<KpiPlanGroupItem>lambdaQuery()
                .eq(KpiPlanGroupItem::getFkKpiPlanGroupId, id).last("limit 1"));
        if(GeneralTool.isNotEmpty(list)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail_group_item_exist"));
        }

        KpiPlanGroup kpiPlanGroup = kpiPlanGroupMapper.selectById(id);
        if (GeneralTool.isEmpty(kpiPlanGroup)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }
        int delete = kpiPlanGroupMapper.deleteById(id);
        if(delete < 0){
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }

    }

    @Override
    public void updateKpiPlanGroup(KpiPlanGroupDto kpiPlanGroupDto) {
        if (GeneralTool.isEmpty(kpiPlanGroupDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        KpiPlanGroup kpiPlanGroup = kpiPlanGroupMapper.selectById(kpiPlanGroupDto.getId());
        if (GeneralTool.isEmpty(kpiPlanGroup)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        kpiPlanGroup = BeanCopyUtils.objClone(kpiPlanGroupDto, KpiPlanGroup::new);
        utilService.setUpdateInfo(kpiPlanGroup);
        int update = kpiPlanGroupMapper.updateById(kpiPlanGroup);
        if (update <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void movingOrder(Long fkKpiPlanId, Integer start, Integer end) {
        if (GeneralTool.isEmpty(fkKpiPlanId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        LambdaQueryWrapper<KpiPlanGroup> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(KpiPlanGroup::getFkKpiPlanId,fkKpiPlanId);
        if (end > start){
            lambdaQueryWrapper.between(KpiPlanGroup::getViewOrder,start,end).orderByDesc(KpiPlanGroup::getViewOrder);
        }else {
            lambdaQueryWrapper.between(KpiPlanGroup::getViewOrder,end,start).orderByDesc(KpiPlanGroup::getViewOrder);

        }
        List<KpiPlanGroup> kpiPlanGroups = list(lambdaQueryWrapper);

        //从下上移 列表倒序排序
        List<KpiPlanGroup> updateList = Lists.newArrayList();
        if (end > start){
            int finalEnd = end;
            List<KpiPlanGroup> sortedList = Lists.newArrayList();
            KpiPlanGroup policy = kpiPlanGroups.get(kpiPlanGroups.size() - 1);
            sortedList.add(policy);
            kpiPlanGroups.remove(kpiPlanGroups.size() - 1);
            sortedList.addAll(kpiPlanGroups);
            for (KpiPlanGroup kpiPlanGroup : sortedList) {
                kpiPlanGroup.setViewOrder(finalEnd);
                finalEnd--;
            }
            updateList.addAll(sortedList);
        }else {
            int finalStart = start;
            List<KpiPlanGroup> sortedList = Lists.newArrayList();
            KpiPlanGroup policy = kpiPlanGroups.get(0);
            kpiPlanGroups.remove(0);
            sortedList.addAll(kpiPlanGroups);
            sortedList.add(policy);
            for (KpiPlanGroup kpiPlanGroup : sortedList) {
                kpiPlanGroup.setViewOrder(finalStart);
                finalStart--;
            }
            updateList.addAll(sortedList);
        }

        if (GeneralTool.isNotEmpty(updateList)){
            updateList.forEach(kpiPlanGroup -> utilService.setUpdateInfo(kpiPlanGroup));
            boolean batch = updateBatchById(updateList);
            if (!batch){
                throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
            }
        }
    }

    @Override
    public List<KpiPlanGroupVo> getGroupSelect(Long fkKpiPlanId) {
        if (GeneralTool.isEmpty(fkKpiPlanId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        List<KpiPlanGroup> kpiPlanGroupList = kpiPlanGroupMapper.selectList(Wrappers.<KpiPlanGroup>lambdaQuery()
                .eq(KpiPlanGroup::getFkKpiPlanId, fkKpiPlanId)
                .orderByDesc(KpiPlanGroup::getViewOrder));
        if (GeneralTool.isEmpty(kpiPlanGroupList)) {
            return Collections.emptyList();
        }
        return BeanCopyUtils.copyListProperties(kpiPlanGroupList, KpiPlanGroupVo::new);
    }

}
