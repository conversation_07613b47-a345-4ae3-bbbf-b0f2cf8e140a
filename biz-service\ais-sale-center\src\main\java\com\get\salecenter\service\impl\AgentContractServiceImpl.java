package com.get.salecenter.service.impl;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.common.cache.CacheNames;
import com.get.common.consts.LoggerModulesConsts;
import com.get.common.eunms.FileTypeEnum;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.GetDateUtil;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.redis.cache.GetRedis;
import com.get.core.secure.utils.GetAuthInfo;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.CollectionUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.filecenter.dto.FileDto;
import com.get.filecenter.feign.IFileCenterClient;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.permissioncenter.vo.CompanyConfigAnalysisVo;
import com.get.permissioncenter.vo.ConfigVo;
import com.get.permissioncenter.vo.StaffVo;
import com.get.remindercenter.enums.EmailTemplateEnum;
import com.get.salecenter.dao.occ.OccMapper;
import com.get.salecenter.dao.sale.AgentContractMapper;
import com.get.salecenter.dao.sale.RAgentContractSignatureMapper;
import com.get.salecenter.dto.AgentContractCompanyDto;
import com.get.salecenter.dto.AgentContractDto;
import com.get.salecenter.dto.CommentDto;
import com.get.salecenter.dto.ContactPersonDto;
import com.get.salecenter.dto.CreateAgentContractDto;
import com.get.salecenter.dto.EmailSendContext;
import com.get.salecenter.dto.MediaAndAttachedDto;
import com.get.salecenter.dto.query.AgentContractQueryDto;
import com.get.salecenter.entity.Agent;
import com.get.salecenter.entity.AgentCompany;
import com.get.salecenter.entity.AgentContract;
import com.get.salecenter.entity.AgentContractAccount;
import com.get.salecenter.entity.RAgentContractSignature;
import com.get.salecenter.entity.RenewalConfig;
import com.get.salecenter.entity.SaleComment;
import com.get.salecenter.entity.SaleContactPerson;
import com.get.salecenter.enums.AgentAppFromEnum;
import com.get.salecenter.enums.AgentContractApprovalStatusEnum;
import com.get.salecenter.enums.ContactPersonTypeEnum;
import com.get.salecenter.enums.ContractTemplateModeEnum;
import com.get.salecenter.enums.MiniProgramPageEnum;
import com.get.salecenter.service.IAgentCompanyService;
import com.get.salecenter.service.IAgentContractAccountService;
import com.get.salecenter.service.IAgentContractCompanyService;
import com.get.salecenter.service.IAgentContractService;
import com.get.salecenter.service.IAgentContractTypeService;
import com.get.salecenter.service.IAgentService;
import com.get.salecenter.service.IAppAgentService;
import com.get.salecenter.service.ICommentService;
import com.get.salecenter.service.ICompanyRelationService;
import com.get.salecenter.service.IContactPersonService;
import com.get.salecenter.service.IDeleteService;
import com.get.salecenter.service.IMediaAndAttachedService;
import com.get.salecenter.utils.DocUtils;
import com.get.salecenter.utils.EmailSenderUtils;
import com.get.salecenter.utils.MyStringUtils;
import com.get.salecenter.utils.VerifyDataPermissionsUtils;
import com.get.salecenter.utils.sale.SecureEncryptUtil;
import com.get.salecenter.vo.AgencyAppdixVo;
import com.get.salecenter.vo.AgentContractSelect;
import com.get.salecenter.vo.AgentContractTypeVo;
import com.get.salecenter.vo.AgentContractVo;
import com.get.salecenter.vo.AgentVo;
import com.get.salecenter.vo.AppAgentVo;
import com.get.salecenter.vo.CommentVo;
import com.get.salecenter.vo.CompanyTreeVo;
import com.get.salecenter.vo.ContactPersonVo;
import com.get.salecenter.vo.MediaAndAttachedVo;
import com.get.salecenter.vo.MediaAppdixVo;
import com.get.workflowcenter.feign.IWorkflowCenterClient;
import com.get.workflowcenter.vo.ActRuTaskVo;
import com.get.workflowcenter.vo.HiCommentFeignVo;
import com.google.common.collect.Maps;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONArray;
import net.sf.json.JsonConfig;
import org.apache.batik.transcoder.TranscoderInput;
import org.apache.batik.transcoder.TranscoderOutput;
import org.apache.batik.transcoder.image.PNGTranscoder;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.FileItemFactory;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.lang.reflect.Field;
import java.net.HttpURLConnection;
import java.net.URL;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.Year;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static com.get.common.cache.CacheNames.SYS_CACHE;

/**
 * <AUTHOR>
 * @DATE: 2020/9/14
 * @TIME: 14:17
 * @Description:
 **/
@Slf4j
@Service
public class AgentContractServiceImpl extends ServiceImpl<AgentContractMapper, AgentContract> implements IAgentContractService {
    @Resource
    private AgentContractMapper contractMapper;
    @Resource
    private VerifyDataPermissionsUtils verifyDataPermissionsUtils;
    @Resource
    private UtilService utilService;
    @Resource
    private IAgentContractTypeService contractTypeService;
    @Resource
    private IMediaAndAttachedService attachedService;
    @Resource
    private IAgentContractCompanyService contractCompanyService;
    @Resource
    private ICompanyRelationService companyRelationService;
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Lazy
    @Resource
    private IAgentService agentService;
    @Resource
    @Lazy
    private IAgentContractAccountService agentContractAccountService;
    @Resource
    private ICommentService commentService;
    @Lazy
    @Resource
    private IAgentCompanyService agentCompanyService;
    @Resource
    private IDeleteService deleteService;
    @Resource
    private IContactPersonService personService;
    @Resource
    private IWorkflowCenterClient workflowCenterClient;
    @Resource
    private IFileCenterClient fileCenterClient;
    @Resource
    private OccMapper occMapper;
    @Resource
    private GetRedis getRedis;

    @Resource
    private RAgentContractSignatureMapper rAgentContractSignatureMapper;
    
    @Resource
    private EmailSenderUtils emailSenderUtils;

    @Resource
    private IAppAgentService appAgentService;

    @Resource
    private SecureEncryptUtil secureEncryptUtil;

    @Override
    public List<AgentContractVo> getAgentContractDtos(AgentContractQueryDto contractVo, Page page) {
        if (GeneralTool.isEmpty(contractVo.getSelectStatus())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }
        if (GeneralTool.isNotEmpty(contractVo.getFkCompanyId())) {
            if (!SecureUtil.validateCompany(contractVo.getFkCompanyId())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
            }
        }
        Long staffId = SecureUtil.getStaffId();
        //员工id + 业务下属员工ids
        List<Long> staffFollowerIds = permissionCenterClient.getStaffFollowerIds(staffId).getData().stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());
        staffFollowerIds.add(staffId);
        //去重
        staffFollowerIds = staffFollowerIds.stream().distinct().collect(Collectors.toList());

//        LambdaQueryWrapper<AgentContract> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if ("2".equals(contractVo.getSelectStatus())) {
            List<Long> personalHistoryTasks = new ArrayList<>();
            Result<List<Long>> result = workflowCenterClient.getPersonalHistoryTasks(contractVo.getProcdkey());
            if (result.isSuccess() && result.getData() != null) {
                personalHistoryTasks = result.getData();
            }
            if (personalHistoryTasks == null || personalHistoryTasks.size() == 0) {
                return null;
            }
            contractVo.setFormIds(personalHistoryTasks);
        }
//        if (GeneralTool.isEmpty(contractVo) || GeneralTool.isEmpty(contractVo.getFkCompanyId())) {
//            //获取当前登录用户所属公司
//            List<Long> contractIds = getContractIdsByCompanyId();
//            lambdaQueryWrapper.in(AgentContract::getId,contractIds);
//        }
//        if (GeneralTool.isEmpty(contractVo) || GeneralTool.isEmpty(contractVo.getFkAreaCountryId())) {
//            List<Long> contractIds = getContractIdsByCountryId();
//            lambdaQueryWrapper.in(AgentContract::getId,contractIds);
//        }
//        if (GeneralTool.isNotEmpty(contractVo)) {
//            if (GeneralTool.isNotEmpty(contractVo.getFkCompanyId())) {
//                List<Long> contractList = queryByCompanyId(contractVo);
//                lambdaQueryWrapper.in(AgentContract::getId,contractList);
//            }
//            if (GeneralTool.isNotEmpty(contractVo.getFkAreaCountryId())) {
//                List<Long> contractIds = queryByCountryId(contractVo);
//                lambdaQueryWrapper.in(AgentContract::getId,contractIds);
//            }
//            if (GeneralTool.isNotEmpty(contractVo.getFkAgentId())) {
//                lambdaQueryWrapper.eq(AgentContract::getFkAgentId,contractVo.getFkAgentId());
//            }
//            if (GeneralTool.isNotEmpty(contractVo.getFkAgentContractTypeId())) {
//                lambdaQueryWrapper.eq(AgentContract::getFkAgentContractTypeId,contractVo.getFkAgentContractTypeId());
//            }
//            if (GeneralTool.isNotEmpty(contractVo.getIsActive())) {
//                lambdaQueryWrapper.eq(AgentContract::getIsActive,contractVo.getIsActive());
//            }
//            if (GeneralTool.isNotEmpty(contractVo.getKeyWord())) {
//                lambdaQueryWrapper.like(AgentContract::getContractNum,contractVo.getKeyWord());
//            }
//        }
//        if ("0".equals(contractVo.getSelectStatus())) {
//            //如果代理id 不是空 就是查代理的合同列表
//            if (contractVo.getFkAgentId() == null) {
//                lambdaQueryWrapper.eq(AgentContract::getGmtCreateUser, contractVo.getGmtCreateUser());
//            }
//        }
//        lambdaQueryWrapper.orderByDesc(AgentContract::getGmtCreate);
//        IPage<AgentContract> pages = contractMapper.selectPage(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(),page.getShowCount())),lambdaQueryWrapper);

        String configJson = "";
        String type;
        Integer count;
        Object config = getRedis.get(SYS_CACHE + CacheNames.AGENT_CONTRACT_CONFIG_CODE + SecureUtil.getStaffId());
        if (GeneralTool.isEmpty(config)) {
            ConfigVo configVo = permissionCenterClient.getConfigByKey(ProjectKeyEnum.AGENT_CONTRACT_CONFIG.key).getData();
            if (GeneralTool.isEmpty(configVo) || GeneralTool.isEmpty(configVo.getValue1())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("lack_of_configuration"));
            }
            getRedis.setEx(SYS_CACHE + CacheNames.AGENT_CONTRACT_CONFIG_CODE + SecureUtil.getStaffId(), configVo.getValue1(), (long) 60 * 60);
            configJson = configVo.getValue1();
        } else {
            configJson = (String) config;
        }
        JSONObject jsonObject = JSONObject.parseObject(configJson);
        if (GeneralTool.isEmpty(jsonObject.getString("type"))) {
            type = "week";
        } else {
            type = jsonObject.getString("type");
        }
        if (GeneralTool.isEmpty(jsonObject.getInteger("count"))) {
            count = 4;
        } else {
            count = jsonObject.getInteger("count");
        }
        String advanceType = StringUtils.upperCase(type);

        IPage<AgentContract> pages = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<AgentContractVo> agentContractVoList = contractMapper.getAgentContracts(pages, contractVo, staffFollowerIds, SecureUtil.getCompanyIds(), count, advanceType);
        page.setAll((int) pages.getTotal());
        if (GeneralTool.isEmpty(agentContractVoList)) {
            return Collections.emptyList();
        }
        List<AgentContractVo> newAgentContractVos = new ArrayList<>();
        //创建人ids
        Set<String> gmtCreateUsers = agentContractVoList.stream().map(AgentContractVo::getGmtCreateUser).collect(Collectors.toSet());
        Map<String, Long> staffIdAndGmtCreateUserMap = new HashMap<>();
        Map<String, Long> companyIdAndGmtCreateUserMap = new HashMap<>();
        //根据创建表的账号找对应的人（批量）
        List<StaffVo> staffByCreateUsers = new ArrayList<>();
        if (GeneralTool.isNotEmpty(gmtCreateUsers)) {
//            staffByCreateUsers = permissionCenterClient.getStaffsByCreateUsers(gmtCreateUsers);
            Result<List<StaffVo>> result = permissionCenterClient.getStaffByCreateUsers(gmtCreateUsers);
            if (result.isSuccess() && result.getData() != null) {
                staffByCreateUsers = result.getData();
            }
        }
        for (StaffVo staffVo : staffByCreateUsers) {
            staffIdAndGmtCreateUserMap.put(staffVo.getLoginId(), staffVo.getId());
            companyIdAndGmtCreateUserMap.put(staffVo.getLoginId(), staffVo.getFkCompanyId());
        }
        List<Long> ids = agentContractVoList.stream().map(AgentContractVo::getId).collect(Collectors.toList());
        List<MediaAndAttachedVo> attachedDtos = attachedService.getMediaAndAttachedDtos(ids, TableEnum.SALE_CONTRACT.key, FileTypeEnum.SALE_CONTRACT_FILE.key);
        Map<Long, List<MediaAndAttachedVo>> map = new HashMap<>();
        if (GeneralTool.isNotEmpty(attachedDtos)) {
            map = attachedDtos.stream().collect(Collectors.groupingBy(MediaAndAttachedVo::getFkTableId));
        }
        for (AgentContractVo agentContractVo : agentContractVoList) {
            Result<ActRuTaskVo> result = workflowCenterClient.getContractTaskDataByBusinessKey(String.valueOf(agentContractVo.getId()), "m_agent_contract");
            if (result.isSuccess() && result.getData() != null) {
                ActRuTaskVo taskVersionByBusinessKey = result.getData();
                agentContractVo.setTaskVersion(taskVersionByBusinessKey.getTaskVersion());
                agentContractVo.setTaskId(taskVersionByBusinessKey.getId());
                agentContractVo.setProcInstId(taskVersionByBusinessKey.getProcInstId());
                agentContractVo.setFkTableParentId(agentContractVo.getFkAgentContractIdRevoke());
                agentContractVo.setMediaAndAttachedDtos(map.get(agentContractVo.getId()));
                //设置申请人id
                if (StringUtils.isNotBlank(agentContractVo.getGmtCreateUser())) {
                    agentContractVo.setFkStaffId(staffIdAndGmtCreateUserMap.get(agentContractVo.getGmtCreateUser()));
                }
                //待修改和上面
//                int signOrGet = workflowCenterClient.getSignOrGet(taskVersionByBusinessKey.getId(),
//                        taskVersionByBusinessKey.getTaskVersion());
//                agentContractVo.setSignOrGetStatus(signOrGet);
                Result<Integer> integerResult = workflowCenterClient.getSignOrGet(taskVersionByBusinessKey.getId(),
                        taskVersionByBusinessKey.getTaskVersion());
                if (integerResult.isSuccess() && integerResult.getData() != null) {
                    agentContractVo.setSignOrGetStatus(integerResult.getData());
                }
            } else {
                agentContractVo.setSignOrGetStatus(2);
            }

//            主要用于创建合同时，作为合同初始公司Id
            agentContractVo.setFkcompanyId(companyIdAndGmtCreateUserMap.get(agentContractVo.getGmtCreateUser()));
            newAgentContractVos.add(agentContractVo);
        }
        //设置名称
        setPropertiesName(newAgentContractVos);
        // 构造合同状态
        this.buildContractApprovalStatus(newAgentContractVos);
        
        // 根据前端传递的合同状态进行过滤
        if (contractVo.getContractApprovalStatus() == null) {
            return newAgentContractVos;

        }

        // 过滤出匹配指定状态的合同
        newAgentContractVos = newAgentContractVos.stream()
            .filter(agentContractVo -> Objects.equals(
                agentContractVo.getContractApprovalStatus(),
                contractVo.getContractApprovalStatus()))
            .collect(Collectors.toList());

        return newAgentContractVos;
    }

    /**
     * 构建合同审批状态
     * 根据代理状态和合同信息计算实际的合同审批状态，并设置到合同VO对象中
     *
     * @param newAgentContractVos 代理合同VO列表
     */
    private void buildContractApprovalStatus(List<AgentContractVo> newAgentContractVos) {
        if (CollectionUtil.isEmpty(newAgentContractVos)) {
            return;
        }
        List<Long> agentIds = newAgentContractVos.stream()
                .map(AgentContractVo::getFkAgentId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(agentIds)) {
            return;
        }
        List<Agent> agents = this.agentService.listByIds(agentIds);
        if (CollectionUtil.isEmpty(agents)) {
            return;
        }

        // 构建代理ID到Agent对象的映射，提高查找效率
        Map<Long, Agent> agentMap = agents.stream()
                .collect(Collectors.toMap(Agent::getId, agent -> agent));

        // 为每个合同设置审批状态
        for (AgentContractVo agentContractVo : newAgentContractVos) {
            if (agentContractVo.getFkAgentId() == null) {
                continue;
            }
            
            Agent agent = agentMap.get(agentContractVo.getFkAgentId());
            if (agent != null) {
                try {
                    // 调用枚举类的静态方法计算实际的合同审批状态
                    // isAgentStatus=false 表示这是合同管理模块的状态显示
                    AgentContractApprovalStatusEnum actualStatus = 
                        AgentContractApprovalStatusEnum.getActualContractStatus(
                            agentContractVo.getContractApprovalStatus(),
                            agentContractVo.getStartTime(),
                            agentContractVo.getEndTime()
                        );
                    
                    // 设置计算后的状态到合同VO中
                    if (actualStatus != null) {
                        agentContractVo.setContractApprovalStatus(actualStatus.getCode());
                    }
                } catch (Exception e) {
                    log.error("计算合同审批状态失败，代理ID：{}，合同ID：{}", 
                        agentContractVo.getFkAgentId(), agentContractVo.getId(), e);
                    // 发生异常时保持原状态，不影响其他数据的处理
                }
            }
        }
    }

    /**
     * Author Cream
     * Description : //代理合同续约
     * Date 2023/8/3 10:19
     * Params:
     * Return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void renewal(List<Long> contractIds) {
        if (GeneralTool.isEmpty(contractIds)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("please_select_the_contract_to_renew"));
        }
        ConfigVo data = permissionCenterClient.getConfigByKey(ProjectKeyEnum.AGENT_CONTRACT_CONFIG.key).getData();
        String val = data.getValue1();
        if (StringUtils.isNotBlank(val)) {
            RenewalConfig renewalConfig = JSONObject.parseObject(JSON.toJSONString(JSON.parse(val)), RenewalConfig.class);
            RenewalConfig.Renewal renewal = renewalConfig.getRenewal();
            Map<String, Integer> map = renewal.getCount();
            String renewalType = renewal.getType();
            List<AgentContract> agentContracts = contractMapper.selectBatchIds(contractIds);
            Map<Long, Long> relationByContractIds = contractCompanyService.getAgentContractCompanyId(contractIds);
            for (AgentContract contract : agentContracts) {
                Long id = contract.getId();
                Long companyId = relationByContractIds.get(id);
                if (GeneralTool.isNotEmpty(companyId)) {
                    Integer time = map.get(String.valueOf(companyId));
                    Date endTime = contract.getEndTime();
                    if (GeneralTool.isNotEmpty(time) && GeneralTool.isNotEmpty(endTime)) {
                        switch (renewalType) {
                            case "day":
                                endTime = DateUtils.addDays(endTime, time);
                                break;
                            case "month":
                                endTime = DateUtils.addMonths(endTime, time);
                                break;
                            case "year":
                                endTime = DateUtils.addYears(endTime, time);
                                break;
                            default:
                                throw new GetServiceException(LocaleMessageUtils.getMessage("illegal_renewal_type"));
                        }
                        contract.setEndTime(endTime);
                        utilService.setUpdateInfo(contract);
                    }
                }
            }
            if (GeneralTool.isNotEmpty(agentContracts)) {
                updateBatchById(agentContracts);
            }
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long addContract(AgentContractDto contractVo) {
        AgentContract agentContract = createContract(contractVo);
        Long companyId;
        if (GeneralTool.isNotEmpty(contractVo.getFkCompanyId())) {
            companyId = contractVo.getFkCompanyId();
        } else {
            companyId = SecureUtil.getFkCompanyId();
        }
        //插入中间表
        AgentContractCompanyDto relation = new AgentContractCompanyDto();
        relation.setFkAgentContractId(agentContract.getId());
        relation.setFkCompanyId(companyId);
        contractCompanyService.addRelation(relation);

        return agentContract.getId();
    }

    @Override
    public AgentContractVo updateAgentContract(AgentContractDto agentContractDto) {
        AgentContract agentContract = BeanCopyUtils.objClone(agentContractDto, AgentContract::new);
        utilService.updateUserInfoToEntity(agentContract);
        contractMapper.updateById(agentContract);
        return findAgentContractById(agentContract.getId());
    }

    @Override
    public AgentContractVo findAgentContractById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        verifyDataPermissionsUtils.verifyByBusinessId(id, VerifyDataPermissionsUtils.AGENT_CONTRACT_O);
        AgentContract agentContract = contractMapper.selectById(id);
        AgentContractVo agentContractVo = BeanCopyUtils.objClone(agentContract, AgentContractVo::new);
        HiCommentFeignVo hiComment = new HiCommentFeignVo();
        Result<HiCommentFeignVo> hiCommentFeignDtoResult = workflowCenterClient.getHiComment(id, TableEnum.SALE_CONTRACT.key);
        if (hiCommentFeignDtoResult.isSuccess() && GeneralTool.isNotEmpty(hiCommentFeignDtoResult.getData())) {
            hiComment = hiCommentFeignDtoResult.getData();
        }
        agentContractVo.setAgreeButtonType(hiComment.getAgreeButtonType());
        agentContractVo.setRefuseButtonType(hiComment.getRefuseButtonType());

        if (GeneralTool.isNotEmpty(agentContractVo)) {
            if (GeneralTool.isNotEmpty(agentContractVo.getFkAgentContractTypeId())) {
                agentContractVo.setTypeName(getTypeMap().get(agentContractVo.getFkAgentContractTypeId()));
            }

            if (GeneralTool.isNotEmpty(agentContractVo.getFkAgentId())) {
                AgentVo agentVo = agentService.findAgentById(agentContractVo.getFkAgentId());
                agentContractVo.setAgentName(agentVo.getName());
            }
//            ActRuTaskVo taskVersionByBusinessKey =
//                    workflowCenterClient.getContractTaskDataByBusinessKey(String.valueOf(agentContractVo.getId()), "m_agent_contract");
            Result<ActRuTaskVo> result = workflowCenterClient.getContractTaskDataByBusinessKey(String.valueOf(agentContractVo.getId()), "m_agent_contract");
            StaffVo staffVo = new StaffVo();
            if (result.isSuccess() && result.getData() != null) {
                ActRuTaskVo taskVersionByBusinessKey = result.getData();
                agentContractVo.setTaskVersion(taskVersionByBusinessKey.getTaskVersion());
                agentContractVo.setTaskId(taskVersionByBusinessKey.getId());
                agentContractVo.setProcInstId(taskVersionByBusinessKey.getProcInstId());
                //待修改和上面
//                int signOrGet = workflowCenterClient.getSignOrGet(taskVersionByBusinessKey.getId(),
//                        taskVersionByBusinessKey.getTaskVersion());
//                agentContractVo.setSignOrGetStatus(signOrGet);
                Result<Integer> integerResult = workflowCenterClient.getSignOrGet(taskVersionByBusinessKey.getId(),
                        taskVersionByBusinessKey.getTaskVersion());
                if (integerResult.isSuccess() && integerResult.getData() != null) {
                    agentContractVo.setSignOrGetStatus(integerResult.getData());
                }
                //设置申请人id
                if (StringUtils.isNotBlank(agentContractVo.getGmtCreateUser())) {
//                    StaffVo staffByCreateUser = permissionCenterClient.getStaffByCreateUser(agentContractVo.getGmtCreateUser());
                    Set<String> gmtCreateUsers = new HashSet<>();
                    gmtCreateUsers.add(agentContractVo.getGmtCreateUser());
                    Result<List<StaffVo>> listResult = permissionCenterClient.getStaffByCreateUsers(gmtCreateUsers);
                    if (listResult.isSuccess() && GeneralTool.isNotEmpty(listResult.getData())) {
                        staffVo = listResult.getData().get(0);
                        agentContractVo.setFkStaffId(staffVo.getId());
                    }

                }
            } else {
                agentContractVo.setSignOrGetStatus(2);
            }

            //公司id
//            StaffVo staffByCreateUser = permissionCenterClient.getStaffByCreateUser(agentContractVo.getGmtCreateUser());
            agentContractVo.setFkcompanyId(staffVo.getFkCompanyId());

            //获取配置
            String configJson = doGetAgentContractConfig();

            //获取解析配置
            JSONObject agentContractConfigJsonObject = doParseAgentContractConfig(configJson);

            //设置是否延期属性
            doSetIsRenewal(agentContractConfigJsonObject, agentContractVo);
        }
        this.buildContractApprovalStatus(Arrays.asList(agentContractVo));
        return agentContractVo;
    }

    @SneakyThrows
    @Override
    public void createAgentContractDocx(Long contractId, Long agentId, Long contractVsion, Integer contractTemplateMode, HttpServletResponse response) {
        Map<String, Object> dataMap = buildParameters(contractId, agentId, contractVsion, contractTemplateMode);
        if (contractVsion == 0) {
            DocUtils.createDoc(dataMap, response, "/contract.ftl", "contract");
        } else if (contractVsion == 2) {
            DocUtils.createDoc(dataMap, response, "/contractVi.ftl", "contract");
        } else {
            DocUtils.createDoc(dataMap, response, "/contractEng.ftl", "contract");
        }

    }


    @Override
    public void createAgentContractPdf(Long contractId, Long agentId, Long contractVsion, Integer contractTemplateMode, HttpServletResponse response) {
        Map<String, Object> dataMap = buildParameters(contractId, agentId, contractVsion, contractTemplateMode);
        try {
            String fileName = "contract" + "-" + dataMap.get("contractNum");
            DocUtils.createPdf(dataMap, response, "/htiContractTemplate.pdf", fileName);
        } catch (Exception e) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("file_export_fail"));
        }


    }

    @Override
    public void createAgentContractPdfForOthers(CreateAgentContractDto createAgentContractDto, HttpServletResponse response) {
        if (GeneralTool.isEmpty(createAgentContractDto)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_is_empty"));
        }
        if (GeneralTool.isEmpty(createAgentContractDto.getSecret())){
            throw new GetServiceException(LocaleMessageUtils.getMessage("lack_of_necessary_parameters") + "secret");
        }
        if (GeneralTool.isEmpty(createAgentContractDto.getEncryptContractId())){
            throw new GetServiceException(LocaleMessageUtils.getMessage("lack_of_necessary_parameters") + "encryptContractId");
        }
        if (GeneralTool.isEmpty(createAgentContractDto.getEncryptAgentId())){
            throw new GetServiceException(LocaleMessageUtils.getMessage("lack_of_necessary_parameters") + "encryptAgentId");
        }
        if (GeneralTool.isEmpty(createAgentContractDto.getContractVsion())){
            throw new GetServiceException(LocaleMessageUtils.getMessage("lack_of_necessary_parameters") + "contractVsion");
        }
        if (GeneralTool.isEmpty(createAgentContractDto.getContractTemplateMode())){
            throw new GetServiceException(LocaleMessageUtils.getMessage("lack_of_necessary_parameters") + "contractTemplateMode");
        }
        Long contractId = null;
        try {
            String contractIdToDecrypt = secureEncryptUtil.decrypt(createAgentContractDto.getEncryptContractId(), createAgentContractDto.getSecret());
            contractId = Long.valueOf(contractIdToDecrypt);
        } catch (Exception e) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("decryption_failed") + "encryptContractId" + e);
        }

        Long agentId = null;
        try {
            String agentIdToDecrypt = secureEncryptUtil.decrypt(createAgentContractDto.getEncryptAgentId(), createAgentContractDto.getSecret());
            agentId = Long.valueOf(agentIdToDecrypt);
        } catch (Exception e) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("decryption_failed") + "encryptAgentId");
        }

        createAgentContractPdf(contractId, agentId, createAgentContractDto.getContractVsion(), createAgentContractDto.getContractTemplateMode(), response);
    }


    public Map<String, Object> buildParameters(Long contractId, Long agentId, Long contractVsion, Integer contractTemplateMode) {
        Agent agent = agentService.getIaeAgentById(agentId);
        if (agent == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("this_feature_is_not_currently_available") + "For" + agentId);
        }
        AgentContract contract = contractMapper.selectById(contractId);
        RAgentContractSignature rAgentContractSignature = rAgentContractSignatureMapper.selectOne(new LambdaQueryWrapper<RAgentContractSignature>().eq(RAgentContractSignature::getFkAgentContractId, contractId));
        Map<String, Object> dataMap = new HashMap<>(8);
        if (contract != null) {
            dataMap.put("bankAccount", "");
            dataMap.put("bankAccountNum", "");
            dataMap.put("bankName", "");
            dataMap.put("bankAddress", "");
            dataMap.put("bankCodeType", "");
            AgentContractAccount account = agentContractAccountService.getFirstAgentContractAccount(agentId);
            if (account != null) {
                dataMap.put("bankAccount", account.getBankAccount());
                dataMap.put("bankAccountNum", account.getBankAccountNum());
                dataMap.put("bankName", account.getBankName());
                dataMap.put("bankAddress", account.getBankAddress());
                if ("SwiftCode".equals(account.getBankCodeType())) {
                    dataMap.put("bankCodeType", account.getBankCode());
                }
            } else {
                throw new GetServiceException(LocaleMessageUtils.getMessage("cannot_find_preferred_account"));
            }
            dataMap.put("contractNum", contract.getContractNum());
            String nature = agent.getNature();
            int flag = StringUtils.isBlank(nature) ? 0 : Integer.parseInt(nature);
            String agentName = agent.getName();
            if (ProjectExtraEnum.AGENT_NATURE_PERSONAL_ACCOUNT_COMPANY.key == flag
                    || ProjectExtraEnum.AGENT_NATURE_PERSON.key == flag
                    || ProjectExtraEnum.AGENT_NATURE_STUDIO.key == flag) {
                if (agentName.startsWith("个人代理")) {
                    agentName = agent.getPersonalName();
                }
            }

            dataMap.put("agentName", agentName);
            dataMap.put("legalPerson", agent.getLegalPerson());
            dataMap.put("taxCode", agent.getTaxCode());
            dataMap.put("idCardNum", agent.getIdCardNum());
            dataMap.put("address", agent.getAddress());
            //联系人信息
//            contractVsion      0 中文 1 英文 2 越南
//            contractTemplateMode 0 旧合同 1 新合同
            if (contractVsion ==0 && contractTemplateMode == 1 ) {
                //签名
                dataMap.put("personalSignature", "");
                dataMap.put("companySignature", "");
                dataMap.put("contractSignatureTime", "");
               if (GeneralTool.isNotEmpty(rAgentContractSignature)){
                   String signature = rAgentContractSignature.getSignature();
                   if (GeneralTool.isNotEmpty(signature)) {
                       if (ProjectExtraEnum.AGENT_NATURE_COMPANY.key == flag) {
                           try {
                               byte[] bytes = svgToPng(signature);
                               dataMap.put("companySignature", bytes);
                           } catch (Exception e) {
                               throw new RuntimeException(e);
                           }
                       } else {
                           try {
                               byte[] bytes = svgToPng(signature);
                               dataMap.put("personalSignature", bytes);
                           } catch (Exception e) {
                               throw new RuntimeException(e);
                           }
                       }
                   }

               }

                ContactPersonDto contactPersonDto = new ContactPersonDto();
                contactPersonDto.setFkTableId(agentId);
                List<ContactPersonVo> contactPersonDatas = personService.getContactPersonDtos(contactPersonDto, null);
                //企业负责人
                dataMap.put("adminContact", "");
                dataMap.put("adminContactPhone", "");
                dataMap.put("email", "");
                //佣金负责人
                dataMap.put("commissionContact", "");
                dataMap.put("commissionContactPhone", "");
                //紧急联系人
                dataMap.put("emergencyContact", "");
                dataMap.put("emergencyContactPhone", "");

                if (GeneralTool.isNotEmpty(contactPersonDatas)) {
                    for (ContactPersonVo contactPersonData : contactPersonDatas) {
                        if (contactPersonData.getFkContactPersonTypeKey().contains("CONTACT_AGENT_ADMIN")) {
                            if (StringUtils.isEmpty((String) dataMap.get("adminContact"))) {
                                dataMap.put("adminContact", contactPersonData.getName());
                                String adminContactPhone = "+" +contactPersonData.getMobileAreaCode() + " " +contactPersonData.getMobile();
//                                dataMap.put("aMobileAreaCode", contactPersonData.getMobileAreaCode());
                                dataMap.put("adminContactPhone", adminContactPhone);
                                //多邮箱的情况，只取第一个邮箱
                                String email = contactPersonData.getEmail();
                                if (StringUtils.isNotEmpty(email)) {
                                    String[] emailParts = email.split("; ");
                                    for (String part : emailParts) {
                                        String trimmedEmail = part.trim();
                                        if (StringUtils.isNotBlank(trimmedEmail)) {
                                            dataMap.put("email", trimmedEmail);
                                            break; // 仅保留第一个有效邮箱
                                        }
                                    }
                                }

                            }
                        } else if (contactPersonData.getFkContactPersonTypeKey().contains("CONTACT_AGENT_COMMISSION")) {
                            if (StringUtils.isEmpty((String) dataMap.get("commissionContact"))) {
                                dataMap.put("commissionContact", contactPersonData.getName());
                                String commissionContactPhone = "+" +contactPersonData.getMobileAreaCode() + " " +contactPersonData.getMobile();
                                dataMap.put("commissionContactPhone",commissionContactPhone);
                            }
                        } else if (contactPersonData.getFkContactPersonTypeKey().contains("CONTACT_AGENT_EMERGENCY")) {
                            if (StringUtils.isEmpty((String) dataMap.get("emergencyContact"))) {
                                dataMap.put("emergencyContact", contactPersonData.getName());
                                String emergencyContactPhone = "+" +contactPersonData.getMobileAreaCode() + " " +contactPersonData.getMobile();
                                dataMap.put("emergencyContactPhone", emergencyContactPhone);
                            }
                        }

                    }
                }

                String time = "";
                String contractSignatureTime = "";
                SimpleDateFormat simpleDateFormat;
                simpleDateFormat = new SimpleDateFormat("yyyy年MM月dd日");
                if (GeneralTool.isNotEmpty(contract.getStartTime())) {
                    time += simpleDateFormat.format(contract.getStartTime());
                }
                if (GeneralTool.isNotEmpty(contract.getEndTime())) {
                    time += "至" + simpleDateFormat.format(contract.getEndTime());
                }
                if (GeneralTool.isNotEmpty(rAgentContractSignature)){
                    if (GeneralTool.isNotEmpty(rAgentContractSignature.getGmtCreate())) {
                        contractSignatureTime = simpleDateFormat.format(rAgentContractSignature.getGmtCreate());
                    }
                    dataMap.put("contractSignatureTime", contractSignatureTime);
                }

                dataMap.put("time", time);

            }else {
                dataMap.put("personName", "");
                dataMap.put("mobileAreaCode", "");
                dataMap.put("mobile", "");
                dataMap.put("email", "");
                dataMap.put("sPersonName", "");
                dataMap.put("sMobileAreaCode", "");
                dataMap.put("sMobile", "");
                List<ContactPersonVo> personInfo = agentService.getAgentContactPersonInfo(agentId);
                if (GeneralTool.isNotEmpty(personInfo)) {
                    ContactPersonVo personDto = personInfo.get(0);
                    dataMap.put("personName", personDto.getName());
                    dataMap.put("mobileAreaCode", personDto.getMobileAreaCode());
                    dataMap.put("mobile", personDto.getMobile());
                    String email = personDto.getEmail();
                    //多邮箱的情况，只取第一个邮箱
                    if (StringUtils.isNotEmpty(email)) {
                        String[] emailParts = email.split("; ");
                        for (String part : emailParts) {
                            String trimmedEmail = part.trim();
                            if (StringUtils.isNotBlank(trimmedEmail)) {
                                dataMap.put("email", trimmedEmail);
                                break; // 仅保留第一个有效邮箱
                            }
                        }
                    }
                    if (personInfo.size() > 1) {
                        ContactPersonVo p2 = personInfo.get(1);
                        if (p2 != null) {
                            dataMap.put("sPersonName", p2.getName());
                            dataMap.put("sMobileAreaCode", p2.getMobileAreaCode());
                            dataMap.put("sMobile", p2.getMobile() != null ? p2.getMobile() : p2.getEmail());
                        }
                    }
//                else {
//                    List<ContactPersonDto> personDtos = agentService.getContactPersonInfo(agentId);
//                    if (GeneralTool.isNotEmpty(personDtos)) {
//                        ContactPersonDto vo = personDtos.get(0);
//                        if (vo!=null) {
//                            dataMap.put("sPersonName", vo.getName());
//                            dataMap.put("sMobile", vo.getMobile() != null ? vo.getMobile() : vo.getEmail());
//                        }
//                    }
//                }
                }
                String time = "";
                SimpleDateFormat simpleDateFormat;
                if (contractVsion == 0) {
                    simpleDateFormat = new SimpleDateFormat("yyyy年MM月dd日");
                    if (GeneralTool.isNotEmpty(contract.getStartTime())) {
                        time += simpleDateFormat.format(contract.getStartTime());
                    }
                    if (GeneralTool.isNotEmpty(contract.getEndTime())) {
                        time += "至" + simpleDateFormat.format(contract.getEndTime());
                    }

                } else {
                    simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
                    if (GeneralTool.isNotEmpty(contract.getStartTime())) {
                        time += simpleDateFormat.format(contract.getStartTime());
                    }
                    if (GeneralTool.isNotEmpty(contract.getEndTime())) {
                        time += " To " + simpleDateFormat.format(contract.getEndTime());
                    }
                }
                dataMap.put("time", time);
            }

            dataMap.put("year", String.valueOf(LocalDate.now().getYear()));
            for (Map.Entry<String, Object> entry : dataMap.entrySet()) {
                if (GeneralTool.isNull(entry.getValue())) {
                    dataMap.put(entry.getKey(), "");
                }
            }


        }
        return dataMap;

    }

//    处理读取出来的blob类型文件
    private static byte[] svgToPng(String base64Svg) throws Exception {
        // 提取并解码Base64部分
        String svgBase64 = base64Svg.split(",")[1];
        byte[] svgBytes = Base64.getDecoder().decode(svgBase64);

        // 创建转换器
        PNGTranscoder transcoder = new PNGTranscoder();
        TranscoderInput input = new TranscoderInput(new ByteArrayInputStream(svgBytes));

        // 转换到内存
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        TranscoderOutput output = new TranscoderOutput(outputStream);

        // 执行转换
        transcoder.transcode(input, output);

        return outputStream.toByteArray();
    }

    private void doSetIsRenewal(JSONObject agentContractConfigJsonObject, AgentContractVo agentContractVo) {
        String type;
        Integer count;
        Date nowDate = new Date();
        if (GeneralTool.isEmpty(agentContractConfigJsonObject.getString("type"))) {
            type = "week";
        } else {
            type = agentContractConfigJsonObject.getString("type");
        }
        if (GeneralTool.isEmpty(agentContractConfigJsonObject.getInteger("count"))) {
            count = 4;
        } else {
            count = agentContractConfigJsonObject.getInteger("count");
        }

        if (GeneralTool.isEmpty(agentContractVo.getEndTime())) {
            agentContractVo.setIsRenewal(0);
        } else {
            Date startDate = agentContractVo.getEndTime();
            if ("week".equals(type)) {
                startDate = GetDateUtil.getDateAfterWeeks(startDate, count * (-1));
            }
            if ("day".equals(type)) {
                startDate = GetDateUtil.getDateAfterDays(startDate, count * (-1));
            }
            if ("month".equals(type)) {
                startDate = GetDateUtil.getDateAfterMonths(startDate, count * (-1));
            }
            if ("year".equals(type)) {
                startDate = GetDateUtil.getDateAfterYears(startDate, count * (-1));
            }

            if (nowDate.after(agentContractVo.getEndTime()) || (nowDate.before(agentContractVo.getEndTime()) && nowDate.after(startDate))) {
                agentContractVo.setIsRenewal(1);
            } else {
                agentContractVo.setIsRenewal(0);
            }
        }
    }

    @Override
    public Map<Long, String> findContractNumByAgentContractIds(Set<Long> ids) {
        Map<Long, String> map = new HashMap<>();
        if (GeneralTool.isEmpty(ids)) {
            return map;
        }
//        Example example = new Example(AgentContract.class);
//        example.createCriteria().andIn("id", ids);
//        List<AgentContract> agentContracts = contractMapper.selectByExample(example);

        List<AgentContract> agentContracts = contractMapper.selectList(Wrappers.<AgentContract>lambdaQuery().in(AgentContract::getId, ids));
        if (GeneralTool.isEmpty(agentContracts)) {
            return map;
        }
        for (AgentContract agentContract : agentContracts) {
            map.put(agentContract.getId(), agentContract.getContractNum());
        }
        return map;
    }

    @Override
    public void deleteAgentContract(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        deleteService.deleteValidateContact(id);
        contractMapper.deleteById(id);
    }

    @Override
    public List<MediaAndAttachedVo> getAgentContractMedia(MediaAndAttachedDto attachedVo, Page page) {
        if (GeneralTool.isEmpty(attachedVo.getFkTableId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        verifyDataPermissionsUtils.verifyByBusinessId(attachedVo.getFkTableId(), VerifyDataPermissionsUtils.AGENT_CONTRACT_O);
        attachedVo.setFkTableName(TableEnum.SALE_CONTRACT.key);
        return attachedService.getMediaAndAttachedDto(attachedVo, page);

    }

    @Override
    public List<MediaAndAttachedVo> addAgentContractMedia(List<MediaAndAttachedDto> mediaAttachedVos) {
        if (GeneralTool.isEmpty(mediaAttachedVos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_vo_null"));
        }
        List<MediaAndAttachedVo> mediaAndAttachedVos = new ArrayList<>();
        for (MediaAndAttachedDto mediaAndAttachedDto : mediaAttachedVos) {
            //设置插入的表
            mediaAndAttachedDto.setFkTableName(TableEnum.SALE_CONTRACT.key);
            mediaAndAttachedVos.add(attachedService.addMediaAndAttached(mediaAndAttachedDto));
        }
        return mediaAndAttachedVos;
    }

    @Override
    public void editAgentContractCompany(List<AgentContractCompanyDto> contractCompanyVo) {
        contractCompanyService.editAgentContractCompany(contractCompanyVo);
    }

    @Override
    public List<CompanyTreeVo> getContractCompanyRelation(Long contractId) {
        return companyRelationService.getContractCompanyRelation(contractId);
    }

    @Override
    public List<AgentContractTypeVo> getContractType() {
        return contractTypeService.getAllAgentContractTypes();
    }

    @Override
    public List<AgentContractSelect> getAllAgentContract() {
//        Example example = new Example(AgentContract.class);
//        Example.Criteria criteria = example.createCriteria();

//        //获取当前登录用户所属公司
//        List<Long> contractIds = getContractIdsByCompanyId();
//        criteria.andIn("id", contractIds);
//
//        //国家
//        List<Long> ids = getContractIdsByCountryId();
//        criteria.andIn("id", ids);
        LambdaQueryWrapper<AgentContract> agentContractSelectLambdaQueryWrapper = new LambdaQueryWrapper<>();
        Set<Long> contractIds = new HashSet<>();
        //获取当前登录用户所属公司
        List<Long> contractIds_ = getContractIdsByCompanyId();
        if (GeneralTool.isNotEmpty(contractIds_)) {
            contractIds.addAll(contractIds_);
        }
        List<Long> ids = getContractIdsByCountryId();
        if (GeneralTool.isNotEmpty(ids)) {
            contractIds.addAll(ids);
        }
        if (GeneralTool.isNotEmpty(contractIds)) {
            agentContractSelectLambdaQueryWrapper.in(AgentContract::getId, contractIds);
        }
        List<AgentContract> agentContracts = contractMapper.selectList(agentContractSelectLambdaQueryWrapper);
        return agentContracts.stream().map(agentContract -> BeanCopyUtils.objClone(agentContract, AgentContractSelect::new)).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long addApprovedAgentContract(AgentContractDto contractVo) {
        AgentContract contract = createApprovedContract(contractVo);
        // 批量保存代理和公司数据 跟随代理公司关系
        List<Long> companyIds = agentCompanyService.getRelationByAgentId(contractVo.getFkAgentId());
        if (GeneralTool.isNotEmpty(companyIds)) {
            //添加中间表
            for (Long companytId : companyIds) {
                //插入中间表
                AgentContractCompanyDto relation = new AgentContractCompanyDto();
                relation.setFkAgentContractId(contract.getId());
                relation.setFkCompanyId(companytId);
                contractCompanyService.addRelation(relation);
            }
        }
        return contract.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long addAgentContract(AgentContractDto contractVo) {
        AgentContract contract = createContract(contractVo);
        // 批量保存代理和公司数据 跟随代理公司关系
        List<Long> companyIds = agentCompanyService.getRelationByAgentId(contractVo.getFkAgentId());
        if (GeneralTool.isNotEmpty(companyIds)) {
            //添加中间表
            for (Long companytId : companyIds) {
                //插入中间表
                AgentContractCompanyDto relation = new AgentContractCompanyDto();
                relation.setFkAgentContractId(contract.getId());
                relation.setFkCompanyId(companytId);
                contractCompanyService.addRelation(relation);
            }
        }
        return contract.getId();
    }

    @Override
    public Long editComment(CommentDto commentDto) {
        SaleComment comment = BeanCopyUtils.objClone(commentDto, SaleComment::new);
        if (GeneralTool.isNotEmpty(commentDto)) {
            if (GeneralTool.isNotEmpty(commentDto.getId())) {
                comment.setFkTableName(TableEnum.SALE_CONTRACT.key);
                commentService.updateComment(comment);
            } else {
                comment.setFkTableName(TableEnum.SALE_CONTRACT.key);
                commentService.addComment(comment);
            }
        }
        return comment.getId();
    }

    @Override
    public List<CommentVo> getComments(CommentDto commentDto, Page page) {
        commentDto.setFkTableName(TableEnum.SALE_CONTRACT.key);
        return commentService.datas(commentDto, page);
    }

    @Override
    public List<ContactPersonVo> getContactPerson(ContactPersonDto contactPersonVo, Page page) {
        if (GeneralTool.isEmpty(contactPersonVo.getFkTableId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        contactPersonVo.setFkTableName(TableEnum.SALE_AGENT.key);
        contactPersonVo.setIsContractContact(true);
        return personService.getContactPersonDtos(contactPersonVo, page);
    }

    @Override
    public Long addContactPerson(ContactPersonDto contactPersonVo) {
        if (GeneralTool.isEmpty(contactPersonVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        contactPersonVo.setFkTableName(TableEnum.SALE_AGENT.key);
        return personService.addContactPerson(contactPersonVo);
    }

    private final static Integer isNewAndHtiContract = 1;
    private final static Integer isOtherContract = 0;

    private AgentContract createContract(AgentContractDto contractVo) {
        AgentContract agentContract = BeanCopyUtils.objClone(contractVo, AgentContract::new);
        utilService.updateUserInfoToEntity(agentContract);
        //ContractTemplateMode 为空的时候进入这个方法，防止其他地方调用创建合同
        if (GeneralTool.isEmpty(contractVo.getContractTemplateMode())) {
            Long fkAgentId = agentContract.getFkAgentId();
            if (GeneralTool.isEmpty(fkAgentId)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("agent_id_null"));
            }

            //查询代理申请中新申请的合同 根据来源设置 contractTemplateMode 的值
            List<AppAgentVo> appAgentFormDetailByAgentId = appAgentService.getAppAgentFormDetailByAgentId(fkAgentId);
            if (GeneralTool.isNotEmpty(appAgentFormDetailByAgentId) && GeneralTool.isNotEmpty(appAgentFormDetailByAgentId.get(0).getAppFrom())) {
                log.info("进入代理申请-新申请的逻辑 ====== appFrom:{}", appAgentFormDetailByAgentId.get(0).getAppFrom());
                AppAgentVo appAgentVo = appAgentFormDetailByAgentId.get(0);
                setContractTemplateMode(appAgentVo.getAppFrom(), agentContract);

            }else {

                //获取代理联系人类型
//            如果包含新类型的联系人 设置为1 否则设置为0
                log.info("进入代理联系人类型逻辑 ===========================");
                ContactPersonDto contactPersonDto = new ContactPersonDto();
                contactPersonDto.setFkTableId(fkAgentId);
                List<ContactPersonVo> contactPersonDatas = personService.getContactPersonDtos(contactPersonDto, null);
                if (GeneralTool.isNotEmpty(contactPersonDatas)) {
                    log.info("进入代理联系人数据不为空逻辑 ===========================");
                    boolean isNewType = contactPersonDatas
                            .stream()
                            .map(ContactPersonVo::getFkContactPersonTypeKey)
                            .filter(Objects::nonNull)
                            .anyMatch(typeKey-> typeKey.contains("CONTACT_AGENT_ADMIN")||
                                    typeKey.contains("CONTACT_AGENT_COMMISSION")||
                                    typeKey.contains("CONTACT_AGENT_EMERGENCY")
                            );
                    agentContract.setContractTemplateMode(isNewType ? isNewAndHtiContract : isOtherContract);
                    log.info("进入联系人逻辑 设置后的值ContractTemplateMode=== ContractTemplateMode:{}", agentContract.getContractTemplateMode());
                }else {
                    agentContract.setContractTemplateMode(isOtherContract);
                    log.info("没有代理联系人，设置合同模板为0");
                }

            }
            log.info("设置后的值ContractTemplateMode=== ContractTemplateMode:{}", agentContract.getContractTemplateMode());
        }
        Map<Long, CompanyConfigAnalysisVo> configMap = permissionCenterClient.getCompanyConfigAnalysis(ProjectKeyEnum.AGENT_CONTRACT_NUMBER_RULE.key).getData();
        if (GeneralTool.isNotEmpty(configMap) && configMap.containsKey(contractVo.getFkCompanyId())) {
            CompanyConfigAnalysisVo companyConfigAnalysisVo = configMap.get(contractVo.getFkCompanyId());
            String prefix = companyConfigAnalysisVo.getValue1();
            if (!("".equals(prefix))) {
                String contractPrefix = prefix + Year.now().getValue();
                String contractNum = String.valueOf(contractMapper.getFkAgentContractNum(contractPrefix));
                int countContractNum = contractMapper.countContractNum(contractVo.getFkCompanyId());
                agentContract.setContractNum(MyStringUtils.getSpecialContractNum(contractPrefix, contractNum, countContractNum));
            } else {
                agentContract.setContractNum(MyStringUtils.getContractNum());
            }
        }
        agentContract.setStatus(0);
        // 合同管理审批 - 待签署
        agentContract.setContractApprovalStatus(AgentContractApprovalStatusEnum.UNSIGNED.getCode());
        contractMapper.insert(agentContract);
        return agentContract;
    }

    private AgentContract createApprovedContract(AgentContractDto contractVo) {
        AgentContract agentContract = BeanCopyUtils.objClone(contractVo, AgentContract::new);
        utilService.updateUserInfoToEntity(agentContract);
        //ContractTemplateMode 为空的时候进入这个方法，防止其他地方调用创建合同
        if (GeneralTool.isEmpty(contractVo.getContractTemplateMode())) {
            Long fkAgentId = agentContract.getFkAgentId();
            if (GeneralTool.isEmpty(fkAgentId)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("agent_id_null"));
            }

            //查询代理申请中新申请的合同 根据来源设置 contractTemplateMode 的值
            List<AppAgentVo> appAgentFormDetailByAgentId = appAgentService.getAppAgentFormDetailByAgentId(fkAgentId);
            if (GeneralTool.isNotEmpty(appAgentFormDetailByAgentId) && GeneralTool.isNotEmpty(appAgentFormDetailByAgentId.get(0).getAppFrom())) {
                log.info("进入代理申请-新申请的逻辑 ====== appFrom:{}", appAgentFormDetailByAgentId.get(0).getAppFrom());
                AppAgentVo appAgentVo = appAgentFormDetailByAgentId.get(0);
                setContractTemplateMode(appAgentVo.getAppFrom(), agentContract);

            }else {

                //获取代理联系人类型
//            如果包含新类型的联系人 设置为1 否则设置为0
                log.info("进入代理联系人类型逻辑 ===========================");
                ContactPersonDto contactPersonDto = new ContactPersonDto();
                contactPersonDto.setFkTableId(fkAgentId);
                List<ContactPersonVo> contactPersonDatas = personService.getContactPersonDtos(contactPersonDto, null);
                if (GeneralTool.isNotEmpty(contactPersonDatas)) {
                    log.info("进入代理联系人数据不为空逻辑 ===========================");
                    boolean isNewType = contactPersonDatas
                            .stream()
                            .map(ContactPersonVo::getFkContactPersonTypeKey)
                            .filter(Objects::nonNull)
                            .anyMatch(typeKey-> typeKey.contains("CONTACT_AGENT_ADMIN")||
                                    typeKey.contains("CONTACT_AGENT_COMMISSION")||
                                    typeKey.contains("CONTACT_AGENT_EMERGENCY")
                            );
                    agentContract.setContractTemplateMode(isNewType ? isNewAndHtiContract : isOtherContract);
                    log.info("进入联系人逻辑 设置后的值ContractTemplateMode=== ContractTemplateMode:{}", agentContract.getContractTemplateMode());
                }else {
                    agentContract.setContractTemplateMode(isOtherContract);
                    log.info("没有代理联系人，设置合同模板为0");
                }

            }
            log.info("设置后的值ContractTemplateMode=== ContractTemplateMode:{}", agentContract.getContractTemplateMode());
        }
        Map<Long, CompanyConfigAnalysisVo> configMap = permissionCenterClient.getCompanyConfigAnalysis(ProjectKeyEnum.AGENT_CONTRACT_NUMBER_RULE.key).getData();
        if (GeneralTool.isNotEmpty(configMap) && configMap.containsKey(contractVo.getFkCompanyId())) {
            CompanyConfigAnalysisVo companyConfigAnalysisVo = configMap.get(contractVo.getFkCompanyId());
            String prefix = companyConfigAnalysisVo.getValue1();
            if (!("".equals(prefix))) {
                String contractPrefix = prefix + Year.now().getValue();
                String contractNum = String.valueOf(contractMapper.getFkAgentContractNum(contractPrefix));
                int countContractNum = contractMapper.countContractNum(contractVo.getFkCompanyId());
                agentContract.setContractNum(MyStringUtils.getSpecialContractNum(contractPrefix, contractNum, countContractNum));
            } else {
                agentContract.setContractNum(MyStringUtils.getContractNum());
            }
        }
        agentContract.setStatus(0);
        // 合同管理审批 - 待签署
        agentContract.setContractApprovalStatus(AgentContractApprovalStatusEnum.APPROVED.getCode());
        contractMapper.insert(agentContract);
        return agentContract;
    }

    //根据代理申请来源设置合同模板模式
    public void setContractTemplateMode(Integer appFrom, AgentContract agentContract) {
        // 申请来源
//        if (appFrom == null || AgentAppFromEnum.getAgentAppFromEnum(appFrom) == null) {
//            agentContract.setContractTemplateMode(ContractTemplateModeEnum.MPS_MAIN_CONTRACT.getCode());
//        }
        AgentAppFromEnum agentAppFromEnum = AgentAppFromEnum.getAgentAppFromEnum(appFrom);
        switch (agentAppFromEnum) {
            case WEB_APPLY_1:
                agentContract.setContractTemplateMode(ContractTemplateModeEnum.MPS_MAIN_CONTRACT.getCode());
                log.info("进入代理申请-新申请的逻辑 ===设置ContractTemplateMode=== ContractTemplateMode:{}", ContractTemplateModeEnum.MPS_MAIN_CONTRACT.getCode());
                break;
            case WEB_APPLY_2:
            case PARTNER_APPLY_2:
                agentContract.setContractTemplateMode(ContractTemplateModeEnum.PMP_MAIN_CONTRACT.getCode());
                log.info("进入代理申请-新申请的逻辑 ===设置ContractTemplateMode=== ContractTemplateMode:{}", ContractTemplateModeEnum.PMP_MAIN_CONTRACT.getCode());
                break;
            default:
                break;
        }
    }
    /**
     * 设置合同类型名称
     *
     * @param agentContractVos
     */
    private void setPropertiesName(List<AgentContractVo> agentContractVos) {
        if (GeneralTool.isEmpty(agentContractVos)) {
            return;
        }
        //获取代理id集合
        Set<Long> agentIds = agentContractVos.stream().map(AgentContractVo::getFkAgentId).collect(Collectors.toSet());
        //获取代理合同id集合
        Set<Long> agentContractIds = agentContractVos.stream().map(AgentContractVo::getId).collect(Collectors.toSet());
        //根据代理ids获取名称
        Map<Long, String> agentNamesMap = new HashMap<>();
        if (GeneralTool.isNotEmpty(agentIds)) {
            agentNamesMap = agentService.getAgentNamesByIds(agentIds);
        }
        //根据代理合同ids获取公司ids
        Map<Long, Set<Long>> relationByContractIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(agentContractIds)) {
            relationByContractIds = contractCompanyService.getRelationByContractIds(agentContractIds);
        }
        //公司Map
        Map<String, String> companyMap = getCompanyMap();
        //合同类型map
        Map<Long, String> typeMap = getTypeMap();

        String configJson = "";
        String type;
        Integer count;
        Object config = getRedis.get(SYS_CACHE + CacheNames.AGENT_CONTRACT_CONFIG_CODE + SecureUtil.getStaffId());
        if (GeneralTool.isEmpty(config)) {
            ConfigVo configVo = permissionCenterClient.getConfigByKey(ProjectKeyEnum.AGENT_CONTRACT_CONFIG.key).getData();
            if (GeneralTool.isEmpty(configVo) || GeneralTool.isEmpty(configVo.getValue1())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("lack_of_configuration"));
            }
            getRedis.setEx(SYS_CACHE + CacheNames.AGENT_CONTRACT_CONFIG_CODE + SecureUtil.getStaffId(), configVo.getValue1(), (long) 60 * 60);
            configJson = configVo.getValue1();
        } else {
            configJson = (String) config;
        }

        Date nowDate = new Date();
        JSONObject jsonObject = JSONObject.parseObject(configJson);
        if (GeneralTool.isEmpty(jsonObject.getString("type"))) {
            type = "week";
        } else {
            type = jsonObject.getString("type");
        }
        if (GeneralTool.isEmpty(jsonObject.getInteger("count"))) {
            count = 4;
        } else {
            count = jsonObject.getInteger("count");
        }


        for (AgentContractVo agentContractVo : agentContractVos) {
            agentContractVo.setTypeName(typeMap.get(agentContractVo.getFkAgentContractTypeId()));

            if (GeneralTool.isNotEmpty(agentContractVo.getFkAgentId())) {
                agentContractVo.setAgentName(agentNamesMap.get(agentContractVo.getFkAgentId()));
            }
            Set<Long> companyIds = relationByContractIds.get(agentContractVo.getId());
            List<Long> companyIds1 = SecureUtil.getCompanyIds();
            Set<Long> filteredCompanyIds = companyIds.stream()
                    .filter(id -> companyIds1.contains(id))
                    .collect(Collectors.toSet());
            agentContractVo.setCompanyIds(new ArrayList<>(filteredCompanyIds));
            if (GeneralTool.isNotEmpty(filteredCompanyIds)) {
                StringBuilder builder = new StringBuilder();
                for (Long companyId : filteredCompanyIds) {
                    String companyName = companyMap.get(String.valueOf(companyId));
                    builder.append(companyName).append("，");
                }
                agentContractVo.setCompanyName(sub(builder));
            }

            if (GeneralTool.isEmpty(agentContractVo.getEndTime())) {
                agentContractVo.setIsRenewal(0);
            } else {
                Date startDate = agentContractVo.getEndTime();
                if ("week".equals(type)) {
                    startDate = GetDateUtil.getDateAfterWeeks(startDate, count * (-1));
                }
                if ("day".equals(type)) {
                    startDate = GetDateUtil.getDateAfterDays(startDate, count * (-1));
                }
                if ("month".equals(type)) {
                    startDate = GetDateUtil.getDateAfterMonths(startDate, count * (-1));
                }
                if ("year".equals(type)) {
                    startDate = GetDateUtil.getDateAfterYears(startDate, count * (-1));
                }

                if (nowDate.after(agentContractVo.getEndTime()) || (nowDate.before(agentContractVo.getEndTime()) && nowDate.after(startDate))) {
                    agentContractVo.setIsRenewal(1);
                } else {
                    agentContractVo.setIsRenewal(0);
                }
            }

        }
    }

    private Map<Long, String> getTypeMap() {
        List<AgentContractTypeVo> agentContractTypes = contractTypeService.getAllAgentContractTypes();
        return agentContractTypes.stream().collect(Collectors.toMap(AgentContractTypeVo::getId, AgentContractTypeVo::getTypeName));
    }

    private List<Long> getContractIdsByCompanyId() {
        List<Long> companyIds = SecureUtil.getCompanyIdsByStaffId(GetAuthInfo.getStaffId());
        if (GeneralTool.isEmpty(companyIds)) {
            companyIds.add(0L);
        }
        List<Long> contractIds = contractCompanyService.getRelationByCompanyId(companyIds);
        if (GeneralTool.isEmpty(contractIds)) {
            contractIds = new ArrayList<>();
            contractIds.add(0L);
        }
        return contractIds;
    }

    private List<Long> getContractIdsByCountryId() {
        List<Long> countryIds = SecureUtil.getCountryIdsByStaffId(GetAuthInfo.getStaffId());
        if (GeneralTool.isEmpty(countryIds)) {
            countryIds.add(0L);
        }
        List<Long> contractIds = contractMapper.getContractByCountryIds(countryIds);
        if (GeneralTool.isEmpty(contractIds)) {
            contractIds.add(0L);
        }
        return contractIds;
    }

    private List<Long> queryByCompanyId(AgentContractDto contractVo) {
        if (!SecureUtil.validateCompany(contractVo.getFkCompanyId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
        }
        List<Long> queryCompanyIdList = new ArrayList<>();
        queryCompanyIdList.add(contractVo.getFkCompanyId());

        //查询合同ids
        List<Long> contractId = contractCompanyService.getRelationByCompanyId(queryCompanyIdList);
        if (GeneralTool.isEmpty(contractId)) {
            contractId = new ArrayList<>();
            contractId.add(0L);
        }
        return contractId;
    }

    private List<Long> queryByCountryId(AgentContractDto contractVo) {
        if (!SecureUtil.validateCompany(contractVo.getFkCompanyId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
        }
        List<Long> queryContractIdList = new ArrayList<>();
        queryContractIdList.add((long) Math.toIntExact(contractVo.getFkAreaCountryId()));
        return contractMapper.getContractByCountryIds(queryContractIdList);
    }

    private Map<String, String> getCompanyMap() {
//        ListResponseBo responseBo = permissionCenterClient.getAllCompanyDto();
        Map<String, String> companyMap = new HashMap<>(5);
        Result<List<com.get.permissioncenter.vo.tree.CompanyTreeVo>> result = permissionCenterClient.getAllCompanyDto();
        if (result.isSuccess() && CollectionUtil.isNotEmpty(result.getData())) {
            JsonConfig config = new JsonConfig();
            config.setExcludes(new String[]{"departmentTree", "totalNum"});
            JSONArray jsonArray = JSONArray.fromObject(result.getData(), config);
            List<CompanyTreeVo> companyTreeVos = JSONArray.toList(jsonArray, new CompanyTreeVo(), new JsonConfig());
            if (GeneralTool.isNotEmpty(companyTreeVos)) {
                companyMap = companyTreeVos.stream().collect(Collectors.toMap(CompanyTreeVo::getId, CompanyTreeVo::getShortName));
                return companyMap;
            }
        }
        return companyMap;
    }

    /**
     * @return java.lang.String
     * @Description :截取字符串逗号
     * @Param [sb]
     * <AUTHOR>
     */
    private String sub(StringBuilder sb) {
        if (GeneralTool.isEmpty(sb)) {
            return null;
        }
        String substring = null;
        int i = sb.lastIndexOf("，");
        if (i != -1) {
            substring = sb.substring(0, i);
        }
        return substring;
    }

    @Override
    public AgentContractVo getAgentContractById(Long id) {
        AgentContract agentContract = contractMapper.selectById(id);
        return BeanCopyUtils.objClone(agentContract, AgentContractVo::new);

    }

    @Override
    public Boolean updateChangeStatus(AgentContract agentContract) {
        utilService.updateUserInfoToEntity(agentContract);
        contractMapper.updateById(agentContract);
        return true;
    }

    @Override
    public Boolean startContractFlow(String businessKey, String procdefKey, String companyId) throws GetServiceException {
        Result<Boolean> result = workflowCenterClient.startContractFlow(businessKey, procdefKey, companyId);
        if (!result.isSuccess() || (result.isSuccess() && result.getData() != null && result.getData() == false)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("start_error"));
        }
        return result.getData();
    }

    @Override
    public void updateCancellationBusiness(Long id) {
        AgentContract agentContract = contractMapper.selectById(id);
        if (agentContract == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }
        agentContract.setStatus(5);
        utilService.updateUserInfoToEntity(agentContract);
        contractMapper.updateById(agentContract);
    }

    @Override
    public void getUserSubmit(String taskId, String status) {
        workflowCenterClient.getContractUserSubmit(taskId, status);
    }

    @Override
    public Long getStaffByAgentId(Long id) {
        return contractMapper.getStaffByAgentId(id);
    }

    @Override
    public Boolean changeStatus(Integer status, String tableName, Long businessKey) {
        contractMapper.changeStatus(status, tableName, businessKey);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void getRevokeAgentContract(Long id, String summary) {
        AgentContract contract = contractMapper.selectById(id);
        if (GeneralTool.isEmpty(contract)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }
        if (contractMapper.getExistParentId(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("record_exist"));
        }
        AgentContract agentContract1 = new AgentContract();
        Class<? extends AgentContract> target = agentContract1.getClass();
        Class<? extends AgentContract> source = contract.getClass();
        Field[] targetFields = target.getDeclaredFields();
        Field[] sourceFields = source.getDeclaredFields();
        Arrays.stream(sourceFields).forEach(sources -> {
            sources.setAccessible(true);
            Arrays.stream(targetFields).forEach(targets -> {
                targets.setAccessible(true);
                try {
                    if (!"serialVersionUID".equals(sources.getName().toString())) {
                        if (sources.getName().equals(targets.getName())) {
                            targets.set(agentContract1, sources.get(contract));
                            return;
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            });
        });
        agentContract1.setStatus(0);
        agentContract1.setId(null);
        agentContract1.setGmtModified(null);
        agentContract1.setGmtModifiedUser(null);
        agentContract1.setRemark(summary);
        agentContract1.setFkAgentContractIdRevoke(id);
        Long aLong = this.addAgentContract(BeanCopyUtils.objClone(agentContract1, AgentContractDto::new));
        if (GeneralTool.isNotEmpty(aLong)) {
            //查看是否有文件文件
            MediaAndAttachedDto mediaAndAttachedVo = new MediaAndAttachedDto();
            mediaAndAttachedVo.setFkTableName("m_agent_contract");
            mediaAndAttachedVo.setFkTableId(id);
            mediaAndAttachedVo.setTypeKey("sale_contract_file");
            List<MediaAndAttachedVo> mediaAndAttachedDto = attachedService.getMediaAndAttachedDto(mediaAndAttachedVo);
            if (GeneralTool.isNotEmpty(mediaAndAttachedDto)) {
                mediaAndAttachedDto.stream().forEach(mediaAndAttachedDto1 -> {
                    mediaAndAttachedDto1.setId(null);
                    mediaAndAttachedDto1.setFkTableId(aLong);
                    mediaAndAttachedDto1.setGmtModified(null);
                    mediaAndAttachedDto1.setGmtModifiedUser(null);
                    attachedService.addMediaAndAttached(BeanCopyUtils.objClone(mediaAndAttachedDto1, MediaAndAttachedDto::new));
                });
            }
            //查看是否有联系人
            List<SaleContactPerson> contactPersonByFkTableId = personService.getContactPersonByFkTableId("m_agent_contract", id);
            if (GeneralTool.isNotEmpty(contactPersonByFkTableId)) {
                contactPersonByFkTableId.stream().forEach(data -> {
                    data.setFkTableId(aLong);
                    data.setGmtModifiedUser(null);
                    data.setGmtModified(null);
                    data.setId(null);
                    this.addContactPerson(BeanCopyUtils.objClone(data, ContactPersonDto::new));
                });
            }
            //更新原表单的状态
            contract.setStatus(ProjectExtraEnum.REVOKED.key);
            utilService.updateUserInfoToEntity(contract);
            contractMapper.updateById(contract);
        } else {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
        }

    }

    @Override
    public void synchronizeCppAttactment() {
        String prefix = "https://www.geaworld.org/statement";
        List<AgencyAppdixVo> list = occMapper.getAppdixList();
        List<FileDto> fileDtos = null;
        StringBuffer error = new StringBuffer();
        if (list.size() > 0) {
            for (int i = 0; i < list.size(); i++) {
                AgencyAppdixVo aaDto = list.get(i);
                //传入cpp代理id获取bms代理id;
                String agentid = aaDto.getAgencyId();
                Long fkAgentid = contractMapper.getFkAgentidByCppAgId(agentid);
                String id = list.get(i).getAgencyId();
                String fileName = list.get(i).getAgreementName();
                String fileType = LoggerModulesConsts.SALECENTER;
                String url = prefix + "/" + id + "/" + fileName;
                System.out.println("url" + url);
                InputStream ins = getInputStreamByUrl(url);
                MultipartFile[] mfile = new MultipartFile[1];
                MultipartFile multipartFile = null;
                try {
                    multipartFile = getMultipartFile(ins, fileName);
                } catch (Exception e) {
                    error.append(id);
                    continue;
                }
                mfile[0] = multipartFile;
                if (GeneralTool.isEmpty(multipartFile)) {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("文件为空"));
                }
                //时间时分秒当合同_num
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                String contract_num = sdf.format(new Date(System.currentTimeMillis()));
                //插入代理合同
                AgentContractVo acADto = new AgentContractVo();
                acADto.setContractNum(contract_num);
                acADto.setRemark("测试代理附件同步");
                acADto.setFkAgentId(fkAgentid);
                acADto.setIsActive(true);
                acADto.setGmtCreate(aaDto.getUploadTm());
                //acADto.setStartTime(aaDto.getUploadTm());
                acADto.setFkAgentContractTypeId((long) 1);
                acADto.setEndTime(aaDto.getEndTm());
                acADto.setContractApprovalMode(0);
                //插入m_agent_contract并返回table_id
                Long aLong = this.addAgentContract(BeanCopyUtils.objClone(acADto, AgentContractDto::new));


                //String uuId = UUID.randomUUID().toString().replaceAll("-", "");
                Result<List<FileDto>> result = null;
                try {
                    result = fileCenterClient.feignUploadAppendix(mfile, fileType);
                } catch (Exception e) {
                    error.append(id);
                    continue;
                }

                if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                    JSONArray jsonArray = JSONArray.fromObject(result.getData());
                    fileDtos = JSONArray.toList(jsonArray, new FileDto(), new JsonConfig());
                    String uuId = fileDtos.get(0).getFileGuid();
                    MediaAppdixVo maDto = new MediaAppdixVo();
                    maDto.setFkFileGuid(uuId);
                    maDto.setFkTableName("m_agent_contract");
                    maDto.setFkTableId(aLong);
                    maDto.setTypeKey("sale_contract_file");
                    maDto.setRemark("测试代理附件同步");
                    //下标确认
                    maDto.setIndexkey(0);
                    //插入s_media_and_attached
                    attachedService.addMediaAndAttached(BeanCopyUtils.objClone(maDto, MediaAndAttachedDto::new));
                }
            }
        }
        System.out.println("错误代理id：" + error);
        //logger.info("downloadAndUpload上传文件返回JSON：{}", com.alibaba.fastjson.JSONArray.toJSONString(fileDtos));
    }

    /**
     * feign 查询代理有合同信息且当前时间在有效期内，否则返回无效合同的代理名
     *
     * @Date 12:49 2022/7/13
     * <AUTHOR>
     */
    @Override
    public List<String> checkAgentContractByAgentIds(Set<Long> agentIdSet) {
        return contractMapper.getInvalidContractAgentName(agentIdSet);
    }

    public InputStream getInputStreamByUrl(String strUrl) {
        HttpURLConnection conn = null;
        try {
            URL url = new URL(strUrl);
            conn = (HttpURLConnection) url.openConnection();
            conn.setRequestMethod("GET");
            conn.setConnectTimeout(20 * 1000);
            final ByteArrayOutputStream output = new ByteArrayOutputStream();
            IOUtils.copy(conn.getInputStream(), output);
            return new ByteArrayInputStream(output.toByteArray());
        } catch (Exception e) {
            e.getStackTrace();
            /*logger.error("func[{}] e[{}-{}] desc[CPP FileUpload error]", Thread.currentThread().getStackTrace()[1].getMethodName(),
                    e.getMessage(), Arrays.deepToString(e.getStackTrace()));*/
        } finally {
            try {
                if (conn != null) {
                    conn.disconnect();
                }
            } catch (Exception e) {
            }
        }
        return null;
    }

    public MultipartFile getMultipartFile(InputStream inputStream, String fileName) {
        FileItem fileItem = createFileItem(inputStream, fileName);
        return new CommonsMultipartFile(fileItem);
    }

    public FileItem createFileItem(InputStream inputStream, String fileName) {
        FileItemFactory factory = new DiskFileItemFactory(16, null);
        String textFieldName = "file";
        FileItem item = factory.createItem(textFieldName, MediaType.MULTIPART_FORM_DATA_VALUE, true, fileName);
        int bytesRead = 0;
        byte[] buffer = new byte[8192];
        OutputStream os = null;
        //使用输出流输出输入流的字节
        try {
            os = item.getOutputStream();
            while ((bytesRead = inputStream.read(buffer, 0, 8192)) != -1) {
                os.write(buffer, 0, bytesRead);
            }
            inputStream.close();
        } catch (IOException e) {
            //logger.error("Stream copy exception", e);
            throw new IllegalArgumentException("文件上传失败");
        } finally {
            if (os != null) {
                try {
                    os.close();
                } catch (IOException e) {
                    // logger.error("Stream close exception", e);
                }
            }
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    //logger.error("Stream close exception", e);
                }
            }
        }

        return item;
    }

    @Override
    public void synchronizeCppAttactmentInfo() {
        List<AgencyAppdixVo> listAttactment = occMapper.getListAttactment();
        if (GeneralTool.isNotEmpty(listAttactment)) {
            int count = 0;
            //cpp数据
            for (AgencyAppdixVo aad : listAttactment) {
                DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                DateFormat dateFormat2 = new SimpleDateFormat("yyyyMMddHHmmss");
                String endTm = null;
                String endTm2 = null;
                endTm = dateFormat.format(aad.getEndTm());
                endTm2 = dateFormat2.format(aad.getUploadTm());
                //bms代理id
                Long fkAgid = contractMapper.getFkAgentidByCppAgId(aad.getAgencyId());
                if (GeneralTool.isNotEmpty(fkAgid)) {
                    String fkAgentid = fkAgid.toString();
                    //根据结束时间和代理id匹配数据
                    AgentContract ac = contractMapper.getAttByAgencyidAndEndtm(fkAgentid, endTm);
                    if (GeneralTool.isNotEmpty(ac)) {
                        ac.setGmtCreate(aad.getUploadTm());
                        ac.setRemark("本次修改");
                        count += 1;
                        String cNum = endTm2;
                        ac.setContractNum("AGC" + cNum);
                        contractMapper.updateById(ac);
                    }
                }
            }
        }
    }

    /**
     * 获取指定时间到期的代理合同
     *
     * @param formatDate
     * @return
     */
    @Override
    public List<AgentContractVo> getAgentContractsByEndTime(String formatDate) {

        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date = null;
        try {
            date = sf.parse(formatDate);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        List<AgentContract> agentContracts = contractMapper.selectList(Wrappers.<AgentContract>lambdaQuery()
                .eq(AgentContract::getEndTime, date)
                .eq(AgentContract::getIsActive, true));

        if (GeneralTool.isEmpty(agentContracts)) {
            return Collections.emptyList();
        }

        Set<Long> agentIds = agentContracts.stream().map(AgentContract::getFkAgentId).collect(Collectors.toSet());
        List<AgentCompany> agentCompanies = agentCompanyService.list(Wrappers.<AgentCompany>lambdaQuery().in(AgentCompany::getFkAgentId, agentIds));
        Map<Long, Long> agentCompanyMap = Maps.newHashMap();
        if (GeneralTool.isNotEmpty(agentCompanies)) {
            agentCompanyMap = agentCompanies.stream().collect(Collectors.toMap(AgentCompany::getFkAgentId, AgentCompany::getFkCompanyId, (key1, key2) -> key2));
        }
        List<AgentContractVo> agentContractVos = BeanCopyUtils.copyListProperties(agentContracts, AgentContractVo::new);

        for (AgentContractVo agentContractVo : agentContractVos) {
            if (GeneralTool.isNotEmpty(agentCompanyMap) && GeneralTool.isNotEmpty(agentCompanyMap.get(agentContractVo.getFkAgentId()))) {
                agentContractVo.setFkcompanyId(agentCompanyMap.get(agentContractVo.getFkAgentId()));
            }
        }
        return agentContractVos;
    }

    /**
     * 续期合同
     *
     * @param id
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void renewalAgentContract(Long id) {
        //获取代理合同
        AgentContractVo agentContractVo = doGetAgentContractDtoNeedRenewal(id);

        //获取配置
        String configJson = doGetAgentContractConfig();

        //获取解析配置
        JSONObject agentContractConfigJsonObject = doParseAgentContractConfig(configJson);

        //进行延期
        doRenewal(agentContractConfigJsonObject, agentContractVo);
    }

    /**
     * 获取代理合同
     *
     * @param agentIds
     * @return
     */
    @Override
    public Map<Long, List<AgentContract>> getAgentContractByAgentIds(List<Long> agentIds) {
        List<AgentContract> agentContracts = contractMapper.selectList(Wrappers.<AgentContract>lambdaQuery()
                .in(AgentContract::getFkAgentId, agentIds)
                .eq(AgentContract::getIsActive, true));
        return agentContracts.stream().collect(Collectors.groupingBy(AgentContract::getFkAgentId));
    }

    /**
     * 解析配置信息
     *
     * @param configJson
     * @return
     */
    private JSONObject doParseAgentContractConfig(String configJson) {
        if (GeneralTool.isEmpty(configJson)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("missing_required_configuration"));
        }
        return JSONObject.parseObject(configJson);
    }

    /**
     * 根据id获取代理合同信息（被延期的代理合同）
     *
     * @param id
     * @return
     */
    private AgentContractVo doGetAgentContractDtoNeedRenewal(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        AgentContract agentContract = contractMapper.selectById(id);
        AgentContractVo agentContractVo = BeanCopyUtils.objClone(agentContract, AgentContractVo::new);

        assert agentContractVo != null;
        if (StringUtils.isNotBlank(agentContractVo.getGmtCreateUser())) {
            Set<String> gmtCreateUsers = new HashSet<>();
            gmtCreateUsers.add(agentContractVo.getGmtCreateUser());
            Result<List<StaffVo>> listResult = permissionCenterClient.getStaffByCreateUsers(gmtCreateUsers);
            if (listResult.isSuccess() && GeneralTool.isNotEmpty(listResult.getData())) {
                StaffVo staffVo = listResult.getData().get(0);
                agentContractVo.setFkcompanyId(staffVo.getFkCompanyId());
            }
        }
        return agentContractVo;
    }

    /**
     * 延期
     *
     * @param config
     * @param agentContractVo
     */
    private void doRenewal(JSONObject config, AgentContractVo agentContractVo) {
        String type;
        Integer count;
        try {
            JSONObject renewal = config.getJSONObject("renewal");
            type = renewal.getString("type");
            JSONObject companyCount = renewal.getJSONObject("count");
            count = companyCount.getInteger(String.valueOf(agentContractVo.getFkcompanyId()));
        } catch (Exception e) {
            type = "year";
            count = 2;
        }

        if (GeneralTool.isEmpty(agentContractVo.getEndTime())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("end_time_is_not_null"));
        } else {
            Date endTime = agentContractVo.getEndTime();
            if ("week".equals(type)) {
                endTime = GetDateUtil.getDateAfterWeeks(endTime, count);
            }
            if ("day".equals(type)) {
                endTime = GetDateUtil.getDateAfterDays(endTime, count);
            }
            if ("month".equals(type)) {
                endTime = GetDateUtil.getDateAfterMonths(endTime, count);
            }
            if ("year".equals(type)) {
                endTime = GetDateUtil.getDateAfterYears(endTime, count);
            }

            agentContractVo.setEndTime(endTime);
            AgentContract agentContract = BeanCopyUtils.objClone(agentContractVo, AgentContract::new);
            utilService.setUpdateInfo(agentContract);
            int i = contractMapper.updateById(agentContract);
            if (i <= 0) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
            }
        }

    }

    private String doGetAgentContractConfig() {
        String configJson = "";
        Object config = getRedis.get(SYS_CACHE + CacheNames.AGENT_CONTRACT_CONFIG_CODE + SecureUtil.getStaffId());
        if (GeneralTool.isEmpty(config)) {
            ConfigVo configVo = permissionCenterClient.getConfigByKey(ProjectKeyEnum.AGENT_CONTRACT_CONFIG.key).getData();
            if (GeneralTool.isEmpty(configVo) || GeneralTool.isEmpty(configVo.getValue1())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("missing_required_configuration"));
            }
            getRedis.setEx(SYS_CACHE + CacheNames.AGENT_CONTRACT_CONFIG_CODE + SecureUtil.getStaffId(), configVo.getValue1(), (long) 60 * 60);
            configJson = configVo.getValue1();
        } else {
            configJson = (String) config;
        }
        return configJson;
    }

    /**
     * 获取最新有新的合同数据
     *
     * @param fkAgentId
     * @return
     */
    @Override
    public AgentContract latestActiveAgentContract(Long fkAgentId) {
        LambdaQueryWrapper<AgentContract> agentContractLambdaQueryWrapper = new LambdaQueryWrapper<AgentContract>()
                .eq(AgentContract :: getFkAgentId, fkAgentId)
                .eq(AgentContract::getIsActive, true)
                .orderByDesc(AgentContract::getGmtCreate)
                .last("LIMIT 1");
        AgentContract agentContract = this.getOne(agentContractLambdaQueryWrapper);
        return agentContract;
    }

    /**
     * 审批同意
     *
     * @param id
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void agree(Long id) {
        // 参数校验
        if (GeneralTool.isEmpty(id)) {
            log.error("合同ID不能为空");
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }

        // 查询合同信息
        AgentContract agentContract = contractMapper.selectById(id);
        if (GeneralTool.isEmpty(agentContract)) {
            log.error("合同不存在，合同ID：{}", id);
            throw new GetServiceException(LocaleMessageUtils.getMessage("record_not_found"));
        }

        // 检查合同状态是否为待审核
        if (!AgentContractApprovalStatusEnum.PENDING_APPROVAL.getCode()
                .equals(agentContract.getContractApprovalStatus())) {
            log.error("合同状态不是待审核状态，无法进行审批同意操作。合同ID：{}，当前状态：{}", id, agentContract.getContractApprovalStatus());
            throw new GetServiceException(LocaleMessageUtils.getMessage("contract_status_invalid"));
        }

        log.info("开始执行合同审批同意操作。合同ID：{}" , id);

        // 更新合同状态为审核通过
        agentContract.setContractApprovalStatus(AgentContractApprovalStatusEnum.APPROVED.getCode());
        utilService.setUpdateInfo(agentContract);

        // 保存到数据库
        int updateResult = contractMapper.updateById(agentContract);
        if (updateResult <= 0) {
            log.error("合同审批同意操作失败，数据库更新失败。合同ID：{}" , id);
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }

        log.info("合同审批同意操作成功。合同ID：{}" , id);
    }

    /**
     * 发送代理合同未签署提醒邮件
     * 基于公司配置的提醒间隔，给符合条件的未签署合同发送签署提醒邮件
     *
     * <AUTHOR>
     * @date 2025-07-21
     */
    @Override
    public void sendAgentContractUnsignedReminders() {
        try {
            log.info("开始执行代理合同未签署提醒邮件发送任务");

            // 数据校验和获取
            List<AgentContract> agentContracts = this.list(new LambdaQueryWrapper<AgentContract>()
                    .eq(AgentContract::getContractApprovalStatus, AgentContractApprovalStatusEnum.UNSIGNED.getCode()));
            if (ObjectUtil.isNull(agentContracts)) {
                log.info("没有找到未签署的代理合同，跳过提醒邮件发送");
                return;
            }
            
            List<AgentContract> unsignedAgentContracts = agentContracts.stream()
                    .filter(agentContract -> agentContract.getFkAgentId() != null).collect(Collectors.toList());
            if (CollectionUtil.isEmpty(unsignedAgentContracts)) {
                log.info("没有找到有效的未签署代理合同，跳过提醒邮件发送");
                return;
            }
            
            Set<Long> agentIds = unsignedAgentContracts.stream().map(AgentContract::getFkAgentId)
                    .collect(Collectors.toSet());
            if (CollectionUtil.isEmpty(agentIds)) {
                log.info("没有找到有效的代理ID，跳过提醒邮件发送");
                return;
            }

            // 根据公司获取不同公司的提醒间隔
            Map<Long, String> reminderInterval = permissionCenterClient
                    .getCompanyConfigMap(ProjectKeyEnum.REMINDER_EMAIL_AGENT_CONTRACT_EXPIRATION.key, 1).getData();
            Map<Long, StaffVo> staffInfos = this.getStaffInfoSkipEmptyEmailByAgentIds(agentIds);
            if (CollectionUtil.isEmpty(staffInfos)) {
                log.info("没有找到有效的员工信息，跳过提醒邮件发送");
                return;
            }

            // 批量查询联系人
            List<SaleContactPerson> allContactPersons = personService.list(
                    new LambdaQueryWrapper<SaleContactPerson>()
                            .eq(SaleContactPerson::getFkTableName, TableEnum.SALE_AGENT.key)
                            .in(SaleContactPerson::getFkTableId, agentIds)
            );

            // 过滤ADMIN类型联系人并构建映射关系
            Map<Long, List<SaleContactPerson>> agentIdToAdminContactPersons = new HashMap<>();
            if (CollectionUtil.isNotEmpty(allContactPersons)) {
                for (SaleContactPerson contactPerson : allContactPersons) {
                    if (isEligibleContactPersonForAdmin(contactPerson)) {
                        Long agentId = contactPerson.getFkTableId();
                        agentIdToAdminContactPersons.computeIfAbsent(agentId, k -> new ArrayList<>()).add(contactPerson);
                    }
                }
            }

            // 收集需要发送邮件的提醒数据
            List<ReminderEmailData> reminderEmailDataList = new ArrayList<>();

            for (AgentContract agentContract : unsignedAgentContracts) {
                Long fkAgentId = agentContract.getFkAgentId();
                if (!staffInfos.containsKey(fkAgentId)) {
                    continue;
                }
                StaffVo staffVo = staffInfos.get(fkAgentId);
                Long fkCompanyId = staffVo.getFkCompanyId();

                // 获取该公司的提醒间隔配置
                String intervalStr = reminderInterval.get(fkCompanyId);
                if (ObjectUtil.isNull(intervalStr)) {
                    log.warn("公司ID: {} 未配置提醒间隔，跳过处理", fkCompanyId);
                    continue;
                }

                try {
                    int interval = Integer.parseInt(intervalStr);

                    // 获取合同创建时间
                    Date createTime = agentContract.getGmtCreate();
                    if (ObjectUtil.isNull(createTime)) {
                        log.warn("合同ID: {} 创建时间为空，跳过处理", agentContract.getId());
                        continue;
                    }

                    // 转换为LocalDate进行日期计算（忽略具体时间）
                    LocalDate createDate = createTime.toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate();
                    LocalDate currentDate = LocalDate.now();

                    // 计算天数差
                    long daysBetween = ChronoUnit.DAYS.between(createDate, currentDate);

                    // 判断是否是interval的倍数
                    if (daysBetween > 0 && daysBetween % interval == 0) {
                        log.info("合同ID: {}, 代理ID: {}, 公司ID: {}, 创建时间: {}, 间隔天数: {}, " +
                                "已过天数: {}, 符合提醒条件，需要发送邮件提醒",
                                agentContract.getId(), fkAgentId, fkCompanyId,
                                createDate, interval, daysBetween);

                        // 收集需要发送邮件的数据
                        List<SaleContactPerson> adminContactPersons = agentIdToAdminContactPersons.getOrDefault(fkAgentId, new ArrayList<>());
                        reminderEmailDataList.add(new ReminderEmailData(fkAgentId, staffVo, adminContactPersons));
                    } else {
                        log.debug("合同ID: {}, 已过天数: {}, 间隔: {}, 不符合提醒条件",
                                agentContract.getId(), daysBetween, interval);
                    }

                } catch (NumberFormatException e) {
                    log.error("公司ID: {} 的提醒间隔配置格式错误: {}", fkCompanyId, intervalStr, e);
                }
            }

            reminderEmailDataList = reminderEmailDataList.stream().filter(reminderEmailData -> ObjectUtils.isNotNull(reminderEmailData)
                    && CollectionUtil.isNotEmpty(reminderEmailData.getAdminContactPersons())).collect(Collectors.toList());
            // 批量发送邮件
            if (CollectionUtil.isNotEmpty(reminderEmailDataList)) {
                sendReminderEmails(reminderEmailDataList);
                log.info("代理合同未签署提醒邮件发送任务执行完成，共处理 {} 条提醒数据", reminderEmailDataList.size());
            } else {
                log.info("没有找到符合条件的合同需要发送提醒邮件");
            }

        } catch (Exception e) {
            log.error("发送代理合同未签署提醒邮件异常", e);
            throw new GetServiceException(LocaleMessageUtils.getMessage("news_email_send_fail"));
        }
    }

    /**
     * 批量发送提醒邮件
     *
     * @param reminderEmailDataList 提醒邮件数据列表
     */
    private void sendReminderEmails(List<ReminderEmailData> reminderEmailDataList) {
        try {
            log.info("开始批量构建合同未签署提醒邮件，数据数量: {}", reminderEmailDataList.size());

            // 批量构建邮件上下文
            List<EmailSendContext> emailContexts = new ArrayList<>();

            for (ReminderEmailData emailData : reminderEmailDataList) {
                Long agentId = emailData.getAgentId();
                StaffVo staffVo = emailData.getStaffVo();
                List<SaleContactPerson> adminContactPersons = emailData.getAdminContactPersons();

                // 获取用于邮件内容的统一姓名（使用第一个代理ADMIN联系人的姓名）
                String unifiedRecipientName = null;
                if (CollectionUtil.isNotEmpty(adminContactPersons)) {
                    unifiedRecipientName = adminContactPersons.get(0).getName();
                }

                // 构建ADMIN联系人邮件
                if (CollectionUtil.isNotEmpty(adminContactPersons)) {
                    for (SaleContactPerson contactPerson : adminContactPersons) {
                        // 处理一个字段多个邮箱的情况（使用"; "分隔）
                        String emailField = contactPerson.getEmail();
                        if (StringUtils.isBlank(emailField)) {
                            continue;
                        }

                        String[] emailArray = emailField.split("; ");
                        if (ArrayUtil.isEmpty(emailArray) || StringUtils.isBlank(emailArray[0])) {
                            continue;
                        }
                        String email = emailArray[0];
                        EmailSendContext context = buildContractReminderEmailContext(
                            email,
                            unifiedRecipientName,
                            agentId,
                            staffVo.getId()
                        );
                        emailContexts.add(context);
                        log.debug("构建ADMIN联系人提醒邮件上下文，收件人: {}, 代理ID: {}", email, agentId);
                    }
                }

                // 构建BD员工邮件，使用与代理联系人相同的邮件内容
                if (staffVo != null && ObjectUtils.isNotNull(staffVo.getEmail()) && StringUtils.isNotBlank(unifiedRecipientName)) {
                    EmailSendContext context = buildContractReminderEmailContext(
                        staffVo.getEmail(),
                        unifiedRecipientName,
                        agentId,
                        staffVo.getId()
                    );
                    emailContexts.add(context);
                    log.debug("构建BD员工提醒邮件上下文，收件人: {}, 代理ID: {}, 使用统一邮件内容", staffVo.getEmail(), agentId);
                }
            }
            // 批量发送邮件
            if (CollectionUtil.isNotEmpty(emailContexts)) {
                emailSenderUtils.sendBatchEmails(emailContexts, null);
                log.info("成功批量发送合同未签署提醒邮件，邮件数量: {}", emailContexts.size());
            } else {
                log.warn("没有有效的收件人，跳过邮件发送");
            }

        } catch (Exception e) {
            log.error("批量发送合同未签署提醒邮件异常", e);
            // 不抛异常，避免影响主流程
        }
    }

    /**
     * 构建合同未签署提醒邮件上下文
     *
     * @param recipientEmail 收件人邮箱
     * @param recipientName 收件人姓名
     * @param agentId 代理ID
     * @param staffId 员工ID
     * @return 邮件发送上下文
     */
    private EmailSendContext buildContractReminderEmailContext(String recipientEmail, String recipientName, Long agentId, Long staffId) {
        // 构建邮件参数
        Map<String, String> emailParams = new HashMap<>();
        emailParams.put("personalName", recipientName); // 收件人姓名
        emailParams.put("name", recipientName); // 收件人姓名（与personalName相同）
        emailParams.put("id", String.valueOf(agentId)); // 代理ID
        
        // 构建二维码路径（将在邮件处理组件中转换为实际二维码）
        String qrcodePath = MiniProgramPageEnum.LOGIN.getPath();
        emailParams.put("qrcode", qrcodePath);
        
        // 添加staffId用于邮件国际化
        if (staffId != null) {
            emailParams.put("staffId", staffId.toString());
        }

        // 构建邮件上下文
        return EmailSendContext.builder()
                .projectKey(ProjectKeyEnum.SALE_CENTER)
                .tableName(TableEnum.SALE_AGENT)
                .tableId(agentId)
                .recipient(recipientEmail)
                .emailTemplate(EmailTemplateEnum.AGENT_CONTRACT_APPROVAL_PASSED)
                .parameters(emailParams)
                .build();
    }

    /**
     * 判断联系人是否为ADMIN类型
     *
     * @param contactPerson 联系人
     * @return 是否为ADMIN类型
     */
    private boolean isEligibleContactPersonForAdmin(SaleContactPerson contactPerson) {
        if (contactPerson == null) {
            return false;
        }
        
        String roleKey = contactPerson.getFkContactPersonTypeKey();
        if (StringUtils.isBlank(roleKey)) {
            return false;
        }

        // 解析逗号分隔的联系人类型字符串，检查是否包含ADMIN类型
        Set<String> roleSet = Arrays.stream(roleKey.split(","))
                .map(String::trim)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());

        return roleSet.contains(ContactPersonTypeEnum.ADMIN.getCode());
    }

    /**
     * 根据代理ID获取员工信息（跳过邮箱为空的员工）
     *
     * @param agentIds 代理ID集合
     * @return 员工信息映射，Key为代理ID，Value为员工信息
     */
    private Map<Long, StaffVo> getStaffInfoSkipEmptyEmailByAgentIds(Set<Long> agentIds) {
        return appAgentService.getStaffInfoSkipEmptyEmailByAgentIds(agentIds);
    }

    /**
     * 提醒邮件数据结构
     */
    private static class ReminderEmailData {
        private Long agentId;
        private StaffVo staffVo;
        private List<SaleContactPerson> adminContactPersons;

        public ReminderEmailData(Long agentId, StaffVo staffVo, List<SaleContactPerson> adminContactPersons) {
            this.agentId = agentId;
            this.staffVo = staffVo;
            this.adminContactPersons = adminContactPersons;
        }

        public Long getAgentId() {
            return agentId;
        }

        public StaffVo getStaffVo() {
            return staffVo;
        }

        public List<SaleContactPerson> getAdminContactPersons() {
            return adminContactPersons;
        }
    }

}
