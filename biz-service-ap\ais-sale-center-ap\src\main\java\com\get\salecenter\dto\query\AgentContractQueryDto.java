package com.get.salecenter.dto.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

@Data
public class AgentContractQueryDto {


 /**
  * 学生代理合同类型Id
  */
 @ApiModelProperty(value = "学生代理合同类型Id", required = true)
 @NotNull(message = "学生代理合同类型不能为空")
 private Long fkAgentContractTypeId;

 /**
  * 公司Id
  */
 @ApiModelProperty(value = "公司Id")
 private Long fkCompanyId;

 @ApiModelProperty("流程key")
 private String procdkey;

 /**
  * 学生代理Id
  */
 @ApiModelProperty(value = "学生代理Id", required = true)
 @NotNull(message = "学生代理id不能为空")
 private Long fkAgentId;

 /**
  * 是否激活：0否/1是
  */
 @ApiModelProperty(value = "是否激活：0否/1是")
 private Boolean isActive;
 @ApiModelProperty(value = "查询关键字")
 private String keyWord;

 @ApiModelProperty(value = "1查询全部，0查询个人，2我的审批")
 private String selectStatus;

 private String gmtCreateUser;

 @ApiModelProperty("代理合同查询开始时间")
 @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
 private Date createBeginTime;

 @ApiModelProperty("代理合同查询结束时间")
 @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
 private Date createEndTime;

 @ApiModelProperty("BD的id")
 private Long bdId;

 @ApiModelProperty("代理激活状态")
 private Boolean agentIsActive;
//============================================

 @ApiModelProperty("我的审批表单ids")
 private List<Long> formIds;

    /**
     * 合同状态
     */
    @ApiModelProperty("合同状态：0无合同/1有合同/2未签署/3待审核/4审核通过/-4审核驳回/5续约中/6生效中/7已过期")
    private Integer contractApprovalStatus;


}