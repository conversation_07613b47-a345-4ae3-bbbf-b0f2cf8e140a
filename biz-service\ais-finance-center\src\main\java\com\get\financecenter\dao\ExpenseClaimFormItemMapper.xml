<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.financecenter.dao.ExpenseClaimFormItemMapper">

  <select id="getFormItemByExpenseClaimFormId" resultType="com.get.financecenter.entity.ExpenseClaimFormItem">
    select * from m_expense_claim_form_item where fk_expense_claim_form_id=#{id}
  </select>
    <select id="getExpenseClaimFormTotalAmount" resultType="java.math.BigDecimal">
      select IFNULL(sum(amount),0) from m_expense_claim_form_item where fk_expense_claim_form_id=#{id}
    </select>

  <select id="getAllExpenseClaimFormItemByActivityData" resultType="com.get.financecenter.vo.ExpenseClaimFormAndItemVo">
    SELECT
    mecf.num AS num,
    mecfi.*
    FROM m_expense_claim_form mecf
    LEFT JOIN m_expense_claim_form_item mecfi ON mecf.id = mecfi.fk_expense_claim_form_id
    WHERE 1=1
    <if test="statusList != null and statusList.size() > 0">
      AND mecf.status IN
      <foreach collection="statusList" item="status" index="index" open="(" separator="," close=")">
        #{status}
      </foreach>
    </if>
    <if test="searchActivityDataDto.fkEventTableName  != null and searchActivityDataDto.fkEventTableName != ''">
      AND mecfi.fk_event_table_name = #{searchActivityDataDto.fkEventTableName}
    </if>
    <if test="searchActivityDataDto.fkEventTableId  != null and searchActivityDataDto.fkEventTableId != ''">
      AND mecfi.fk_event_table_id = #{searchActivityDataDto.fkEventTableId}
    </if>
  </select>

  <select id="getExpenseClaimFormByActivityData" resultType="com.get.financecenter.vo.ExpenseClaimFormAndItemVo">
    SELECT mecfi.*,
    mecf.fk_company_id,
    mecf.fk_department_id,
    mecf.fk_staff_id,
    mecf.fk_expense_claim_form_id_revoke,
    mecf.num,
    mecf.bill_count,
    mecf.fk_vouch_id,
    mecf.is_vouch_created,
    mecf.date_vouch_created,
    mecf.fk_staff_id_vouch_created,
    mecf.fk_staff_ids_notice,
    mecf.status
    FROM m_expense_claim_form_item mecfi
    LEFT JOIN m_expense_claim_form mecf  ON mecfi.fk_expense_claim_form_id =mecf.id
    WHERE 1=1
    <if test="searchActivityDataDto.fkEventTableName  != null and searchActivityDataDto.fkEventTableName != ''">
      AND mecfi.fk_event_table_name =  #{searchActivityDataDto.fkEventTableName}
    </if>
    <if test="searchActivityDataDto.fkEventTableId  != null and searchActivityDataDto.fkEventTableId != ''">
      AND mecfi.fk_event_table_id = #{searchActivityDataDto.fkEventTableId}
    </if>
    GROUP BY mecfi.id
    ORDER BY mecfi.gmt_create
  </select>

  <select id="getClaimedEventCostByActivityData" resultType="java.math.BigDecimal">
    SELECT IFNULL(SUM(mecfi.amount), 0) AS totalAmount
    FROM m_expense_claim_form mecf
    LEFT JOIN m_expense_claim_form_item mecfi ON mecf.id = mecfi.fk_expense_claim_form_id
    WHERE 1=1
    <if test="statusList != null and statusList.size() > 0">
      AND mecf.status IN
      <foreach collection="statusList" item="status" index="index" open="(" separator="," close=")">
        #{status}
      </foreach>
    </if>
    <if test="searchActivityDataDto.fkEventTableName  != null and searchActivityDataDto.fkEventTableName != ''">
      AND mecfi.fk_event_table_name = #{searchActivityDataDto.fkEventTableName}
    </if>
    <if test="searchActivityDataDto.fkEventTableId  != null and searchActivityDataDto.fkEventTableId != ''">
      AND mecfi.fk_event_table_id = #{searchActivityDataDto.fkEventTableId}
    </if>
  </select>

  <select id="getCurrencyTypeByActivityData" resultType="java.lang.String">
      SELECT mecfi.fk_currency_type_num
      FROM m_expense_claim_form mecf
               LEFT JOIN m_expense_claim_form_item mecfi ON mecf.id = mecfi.fk_expense_claim_form_id
      WHERE 1 = 1
      <if test="statusList != null and statusList.size() > 0">
          AND mecf.status IN
          <foreach collection="statusList" item="status" index="index" open="(" separator="," close=")">
              #{status}
          </foreach>
      </if>
      <if test="searchActivityDataDto.fkEventTableName != null and searchActivityDataDto.fkEventTableName != ''">
          AND mecfi.fk_event_table_name = #{searchActivityDataDto.fkEventTableName}
      </if>
      <if test="searchActivityDataDto.fkEventTableId != null and searchActivityDataDto.fkEventTableId != ''">
          AND mecfi.fk_event_table_id = #{searchActivityDataDto.fkEventTableId}
      </if>
  </select>
</mapper>