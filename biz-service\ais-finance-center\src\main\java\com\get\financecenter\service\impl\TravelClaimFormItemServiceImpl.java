package com.get.financecenter.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.TableEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.financecenter.dao.CurrencyTypeMapper;
import com.get.financecenter.dao.TravelClaimFeeTypeMapper;
import com.get.financecenter.dao.TravelClaimFormItemMapper;
import com.get.financecenter.dto.SearchActivityDataDto;
import com.get.financecenter.entity.TravelClaimFeeType;
import com.get.financecenter.entity.TravelClaimFormItem;
import com.get.financecenter.enums.ActivityTypeEnum;
import com.get.financecenter.service.ICurrencyTypeService;
import com.get.financecenter.service.TravelClaimFormItemService;
import com.get.financecenter.utils.ActivityTypeUtils;
import com.get.financecenter.vo.ActivityFinancialSummaryVo;
import com.get.financecenter.vo.TravelClaimFormAndItemVo;
import com.get.financecenter.vo.TravelClaimFormItemVo;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.salecenter.feign.ISaleCenterClient;
import com.get.workflowcenter.feign.IWorkflowCenterClient;
import com.get.workflowcenter.vo.ActRuTaskVo;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

@Service
public class TravelClaimFormItemServiceImpl extends BaseServiceImpl<TravelClaimFormItemMapper, TravelClaimFormItem> implements TravelClaimFormItemService {
    @Resource
    private TravelClaimFormItemMapper travelClaimFormItemMapper;
    @Resource
    private TravelClaimFeeTypeMapper travelClaimFeeTypeMapper;
    @Resource
    private CurrencyTypeMapper currencyTypeMapper;

    @Resource
    private ActivityTypeUtils activityTypeUtils;
    @Resource
    private IWorkflowCenterClient workflowCenterClient;
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Resource
    private ICurrencyTypeService currencyTypeService;
    @Resource
    private ISaleCenterClient saleCenterClient;


    @Override
    public List<TravelClaimFormItemVo> getDtoByTravelClaimFormId(Long id) {
        List<TravelClaimFormItem> expenseClaimFormItems = this.travelClaimFormItemMapper.selectList(Wrappers.<TravelClaimFormItem>query().lambda().eq(TravelClaimFormItem::getFkTravelClaimFormId, id));

        // 2021/9/16 费用类型名称改为获取Map 一次性sql查询
        List<TravelClaimFormItemVo> convertDatas = new ArrayList<>();
        setTravelClaimFormItemParaments(expenseClaimFormItems, convertDatas);
        return convertDatas;
    }

    @Override
    public List<TravelClaimFormItemVo> getDtoBySearchActivityDataDto(SearchActivityDataDto searchActivityDataDto) {
        List<TravelClaimFormItemVo> convertDatas = new ArrayList<>();
        List<TravelClaimFormItem> expenseClaimFormItems = this.travelClaimFormItemMapper.selectList(Wrappers.<TravelClaimFormItem>query().lambda()
                .eq(TravelClaimFormItem::getFkEventTableName, searchActivityDataDto.getFkEventTableName()).eq(TravelClaimFormItem::getFkEventTableId, searchActivityDataDto.getFkEventTableId()));
        //设置对应的参数
        setTravelClaimFormItemParaments(expenseClaimFormItems, convertDatas);
        return convertDatas;
    }

    @Override
    public List<TravelClaimFormAndItemVo> getAllTravelClaimFormItemByActivityData(SearchActivityDataDto searchActivityDataDto) {
        if (GeneralTool.isEmpty(searchActivityDataDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        if (GeneralTool.isEmpty(searchActivityDataDto.getFkEventTableName())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing") + "fkEventTableName");
        }
        if (GeneralTool.isEmpty(searchActivityDataDto.getFkEventTableId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing") + "fkEventTableId");
        }
        // 获取状态为：1审批结束 的费用 （费用报销和差旅费用报销）
        List<Integer> statusList = new ArrayList<>(Arrays.asList(1));
        List<TravelClaimFormAndItemVo> travelClaimFormAndItemVos = travelClaimFormItemMapper.getAllTravelClaimFormItemByActivityData(searchActivityDataDto, statusList);
        if (GeneralTool.isEmpty(travelClaimFormAndItemVos)) {
            return Collections.emptyList();
        }
        for (TravelClaimFormAndItemVo travelClaimFormAndItemVo : travelClaimFormAndItemVos) {
            //费用类型名称
            TravelClaimFeeType travelClaimFeeType = travelClaimFeeTypeMapper.selectById(travelClaimFormAndItemVo.getFkTravelClaimFeeTypeId());
            travelClaimFormAndItemVo.setExpenseClaimFeeTypeName(travelClaimFeeType.getTypeName());

            String currencyName = currencyTypeMapper.getCurrencyNameByNum(travelClaimFormAndItemVo.getFkCurrencyTypeNum());
            travelClaimFormAndItemVo.setFkCurrencyTypeName( currencyName );

            if (GeneralTool.isNotEmpty(travelClaimFormAndItemVo.getFkEventTableName()) && GeneralTool.isNotEmpty(travelClaimFormAndItemVo.getFkEventTableId())) {
                activityTypeUtils.processRelationTarget(
                        travelClaimFormAndItemVo.getFkEventTableName(),
                        travelClaimFormAndItemVo.getFkEventTableId(),
                        null,
                        travelClaimFormAndItemVo::setEventTableCompanyId,
                        travelClaimFormAndItemVo::setEventTableName
                );
            }
        }
        return travelClaimFormAndItemVos;
    }


    private void setTravelClaimFormItemParaments(List<TravelClaimFormItem> expenseClaimFormItems, List<TravelClaimFormItemVo> convertDatas) {
        for (TravelClaimFormItem travelClaimFormItem : expenseClaimFormItems) {
            TravelClaimFormItemVo travelClaimFormItemVo = BeanCopyUtils.objClone(travelClaimFormItem, TravelClaimFormItemVo::new);
            //费用类型名称
            TravelClaimFeeType travelClaimFeeType = travelClaimFeeTypeMapper.selectById(travelClaimFormItem.getFkTravelClaimFeeTypeId());
            travelClaimFormItemVo.setExpenseClaimFeeTypeName(travelClaimFeeType.getTypeName());

            String currencyName = currencyTypeMapper.getCurrencyNameByNum(travelClaimFormItemVo.getFkCurrencyTypeNum());
            travelClaimFormItemVo.setFkCurrencyTypeName(travelClaimFormItemVo.getFkCurrencyTypeNum() + "(" + currencyName + ")");

            if (GeneralTool.isNotEmpty(travelClaimFormItemVo.getFkEventTableName()) && GeneralTool.isNotEmpty(travelClaimFormItemVo.getFkEventTableId())) {
                activityTypeUtils.processRelationTarget(
                        travelClaimFormItemVo.getFkEventTableName(),
                        travelClaimFormItemVo.getFkEventTableId(),
                        null,
                        travelClaimFormItemVo::setEventTableCompanyId,
                        travelClaimFormItemVo::setEventTableName
                );
            }
            convertDatas.add(travelClaimFormItemVo);
        }
    }

    /**
     * 根据活动信息分页查询差旅报销单列表数据
     * @param searchActivityDataDto
     * @param page
     * @return
     */
    @Override
    public List<TravelClaimFormAndItemVo> getTravelClaimFormByActivityData(SearchActivityDataDto searchActivityDataDto, Page page) {
        verifyActivityParameters(searchActivityDataDto);
        List<TravelClaimFormAndItemVo> convertDatas = new ArrayList<>();
        IPage<TravelClaimFormAndItemVo> pages = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<TravelClaimFormAndItemVo> travelClaimFormAndItemVos = travelClaimFormItemMapper.getTravelClaimFormActivityData(searchActivityDataDto, pages);
        if (GeneralTool.isEmpty(travelClaimFormAndItemVos)) {
            return Collections.emptyList();
        }
        page.setAll((int) pages.getTotal());
        Result<Map<Long, Integer>> result = workflowCenterClient.getFromIdsByStaffId(SecureUtil.getStaffId(), TableEnum.FINANCE_TRAVEL_CLAIM_FORM.key);
        if (!result.isSuccess()) {
            throw new GetServiceException(result.getMessage());
        }
        //公司id集合
        Set<Long> companyIds = new HashSet<>();
        //部门id集合
        Set<Long> departmentIds = new HashSet<>();
        //报销人id集合
        Set<Long> staffIds = new HashSet<>();
        //报销单id集合
        List<Long> expenseClaimFormIds = new ArrayList<>();
        //获取各自集合的值
        for (TravelClaimFormAndItemVo travelClaimFormAndItemVo : travelClaimFormAndItemVos) {
            companyIds.add(travelClaimFormAndItemVo.getFkCompanyId());
            departmentIds.add(travelClaimFormAndItemVo.getFkDepartmentId());
            staffIds.add(travelClaimFormAndItemVo.getFkStaffId());
            expenseClaimFormIds.add(travelClaimFormAndItemVo.getId());
        }
        //feign调用 获取公司id-name的map
        companyIds.removeIf(Objects::isNull);
        Map<Long, String> companyNameMap = permissionCenterClient.getCompanyNamesByIds(companyIds).getData();
        //feign调用 获取部门id-name的map
        departmentIds.removeIf(Objects::isNull);
        Map<Long, String> departmentNameMap = permissionCenterClient.getDepartmentNamesByIds(departmentIds).getData();
        //feign调用 获取报销人id-name的map
        staffIds.removeIf(Objects::isNull);
        Map<Long, String> staffNameMap = permissionCenterClient.getStaffNameMap(staffIds).getData();
        //feign调用 获取流程方面dto 报销单id-actRuTaskDot的map
        expenseClaimFormIds.removeIf(Objects::isNull);
        Map<String, List<Long>> businessIdsWithProcdefKey = new HashMap<>();
        businessIdsWithProcdefKey.put(TableEnum.FINANCE_TRAVEL_CLAIM_FORM.key, expenseClaimFormIds);
        Result<Map<Long, ActRuTaskVo>> result1 = workflowCenterClient.getActRuTaskDtosByBusinessKey(businessIdsWithProcdefKey);
        if (!result1.isSuccess()) {
            throw new GetServiceException(result1.getMessage());
        }
        Map<Long, ActRuTaskVo> actRuTaskDtoMap = result1.getData();
        //币种编号nums
        Set<String> currencyTypeNums = travelClaimFormAndItemVos.stream().map(TravelClaimFormAndItemVo::getFkCurrencyTypeNum).collect(Collectors.toSet());
        //根据币种编号nums获取名称
        Map<String, String> currencyNamesByNums = new HashMap<>();
        if (GeneralTool.isNotEmpty(currencyTypeNums)) {
            currencyNamesByNums = currencyTypeService.getCurrencyNamesByNums(currencyTypeNums);
        }

        for (TravelClaimFormAndItemVo travelClaimFormAndItemVo : travelClaimFormAndItemVos) {
            travelClaimFormAndItemVo.setDepartmentName("");
            if (GeneralTool.isNotEmpty(companyNameMap.get(travelClaimFormAndItemVo.getFkCompanyId()))) {
                travelClaimFormAndItemVo.setCompanyName(companyNameMap.get(travelClaimFormAndItemVo.getFkCompanyId()));
            }
            if (GeneralTool.isNotEmpty(departmentNameMap.get(travelClaimFormAndItemVo.getFkDepartmentId()))) {
                travelClaimFormAndItemVo.setDepartmentName(departmentNameMap.get(travelClaimFormAndItemVo.getFkDepartmentId()));
            }
            if (GeneralTool.isNotEmpty(staffNameMap.get(travelClaimFormAndItemVo.getFkStaffId()))) {
                travelClaimFormAndItemVo.setStaffName(staffNameMap.get(travelClaimFormAndItemVo.getFkStaffId()));
            }
            if (GeneralTool.isNotEmpty(currencyNamesByNums.get(travelClaimFormAndItemVo.getFkCurrencyTypeNum()))) {
                travelClaimFormAndItemVo.setFkCurrencyTypeName(currencyNamesByNums.get(travelClaimFormAndItemVo.getFkCurrencyTypeNum()));
            }
            if (GeneralTool.isNotEmpty(result.getData().get(travelClaimFormAndItemVo.getId()))) {
                travelClaimFormAndItemVo.setTravelClaimFormStatus(result.getData().get(travelClaimFormAndItemVo.getId()));
            }
//            //流程对象
//            ActRuTaskVo actRuTaskVo = actRuTaskDtoMap.get(travelClaimFormAndItemVo.getId());
//            //正在进行的任务id
//            if (GeneralTool.isNotEmpty(actRuTaskVo.getId())) {
//                travelClaimFormAndItemVo.setTaskId(Long.valueOf(actRuTaskVo.getId()));
//            }
//            //流程实例id
//            if (GeneralTool.isNotEmpty(actRuTaskVo.getProcInstId())) {
//                travelClaimFormAndItemVo.setProcInstId(Long.valueOf(actRuTaskVo.getProcInstId()));
//            }
//            //任务版本
//            if (GeneralTool.isNotEmpty(actRuTaskVo.getTaskVersion())) {
//                travelClaimFormAndItemVo.setTaskVersion(actRuTaskVo.getTaskVersion());
//            }
            //获取报销金额总和


            if (GeneralTool.isNotEmpty(travelClaimFormAndItemVo.getFkEventTableName()) && GeneralTool.isNotEmpty(travelClaimFormAndItemVo.getFkEventTableId())) {
                activityTypeUtils.processRelationTarget(
                        travelClaimFormAndItemVo.getFkEventTableName(),
                        travelClaimFormAndItemVo.getFkEventTableId(),
                        null,
                        travelClaimFormAndItemVo::setEventTableCompanyId,
                        travelClaimFormAndItemVo::setEventTableName
                );
            }
            convertDatas.add(travelClaimFormAndItemVo);
        }
        return convertDatas;
    }

    /**
     * 根据活动信息查询费用
     * @param searchActivityDataDto
     * @return
     */
    @Override
    public ActivityFinancialSummaryVo getActivityFinancialSummary(SearchActivityDataDto searchActivityDataDto) {
        verifyActivityParameters(searchActivityDataDto);
        ActivityFinancialSummaryVo activityFinancialSummaryVo = new ActivityFinancialSummaryVo();

        if (searchActivityDataDto.getFkEventTableName().equals(ActivityTypeEnum.M_EVENT_INCENTIVE.activityTypeCode)) {
            //1.获取奖励推广活动费用明细

            Result<BigDecimal> eventIncentiveCostSubtotal = saleCenterClient.getEventIncentiveCostSubtotal(searchActivityDataDto.getFkEventTableId());
            if (eventIncentiveCostSubtotal.isSuccess()) {
                activityFinancialSummaryVo.setTotalEventCost(eventIncentiveCostSubtotal.getData());
                // 获取奖励推广活动费用小计
                activityFinancialSummaryVo.setEventCostSubtotal(eventIncentiveCostSubtotal.getData());
            }
        } else if (searchActivityDataDto.getFkEventTableName().equals(ActivityTypeEnum.M_EVENT.activityTypeCode)) {
            //2.获取活动费用明细
            Result<BigDecimal> eventCostSubtotal = saleCenterClient.getEventCostSubtotal(searchActivityDataDto.getFkEventTableId());
            if (eventCostSubtotal.isSuccess()) {
                activityFinancialSummaryVo.setTotalEventCost(eventCostSubtotal.getData());
                // 获取活动汇总费用小计
                activityFinancialSummaryVo.setEventCostSubtotal(eventCostSubtotal.getData());
            }
        }
        // 状态：0待发起/1审批结束/2审批中/3审批拒绝/4申请放弃/5作废/6撤销
        //已支出费用
//        获取状态为：1审批结束/2审批中 的支出费用
        List<Integer> claimedEventCoststatusList = new ArrayList<>(Arrays.asList(1, 2));
        String currencyTypeByActivityData = travelClaimFormItemMapper.getCurrencyTypeByActivityData(searchActivityDataDto, claimedEventCoststatusList);
        BigDecimal claimedEventCost = travelClaimFormItemMapper.getClaimedEventCostByActivityData(searchActivityDataDto,claimedEventCoststatusList).setScale(2);
        activityFinancialSummaryVo.setClaimedEventCost(claimedEventCost);
        activityFinancialSummaryVo.setFkCurrencyTypeNum(currencyTypeByActivityData);
        activityFinancialSummaryVo.setCurrencyTypeName(currencyTypeService.getCurrencyNameByNum(currencyTypeByActivityData));
//        报销费用小计
        activityFinancialSummaryVo.setClaimFormSubtotal(claimedEventCost);
        //已支出费用
//        获取状态为：1审批结束 的待报销总费用
        List<Integer> totalClaimAmountstatusList = new ArrayList<>(Arrays.asList(1, 2));
        BigDecimal totalClaimAmount = travelClaimFormItemMapper.getClaimedEventCostByActivityData(searchActivityDataDto,totalClaimAmountstatusList).setScale(2);
        activityFinancialSummaryVo.setTotalClaimAmount(totalClaimAmount);
//        availableEventCost
        return activityFinancialSummaryVo;

    }
    private static void verifyActivityParameters(SearchActivityDataDto searchActivityDataDto) {
        if (GeneralTool.isEmpty(searchActivityDataDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        if (GeneralTool.isEmpty(searchActivityDataDto.getFkEventTableName())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing") + "fkEventTableName");
        }
        if (GeneralTool.isEmpty(searchActivityDataDto.getFkEventTableId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing") + "fkEventTableId");
        }
    }
}
