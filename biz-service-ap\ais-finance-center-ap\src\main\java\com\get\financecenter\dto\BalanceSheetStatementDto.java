package com.get.financecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 资产负债表Dto
 */
@Data
public class BalanceSheetStatementDto {

    @ApiModelProperty(value = "公司Id")
    @NotNull(message = "公司Id不能为空")
    private Long companyId;

    @ApiModelProperty(value = "统计时间")
    @NotNull(message = "统计时间不能为空")
    @JsonFormat(pattern = "yyyy-MM", timezone = "GMT+8")
    private Date time;


}
