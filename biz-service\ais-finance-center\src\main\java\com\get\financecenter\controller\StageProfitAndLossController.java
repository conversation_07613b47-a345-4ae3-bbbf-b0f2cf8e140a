package com.get.financecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ResponseBo;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.redis.lock.RedisLock;
import com.get.financecenter.dto.ProfitAndLossStatementDto;
import com.get.financecenter.dto.RecomputeProfitLossStatementDto;
import com.get.financecenter.service.ProfitAndLossService;
import com.get.financecenter.vo.FinancialStatsAsyncVo;
import com.get.financecenter.vo.ProfitAndLossStatementVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Api(tags = "损益表管理")
@RestController
@RequestMapping("finance/profitAndLoss")
public class StageProfitAndLossController {

    @Resource
    private ProfitAndLossService profitAndLossService;

    @ApiOperation(value = "生成并查询损益表", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.ADD, description = "财务中心/财务报表/生成并查询损益表")
    @PostMapping("createProfitAndLossStatement")
    @RedisLock(value = "fzh:createProfitAndLossStatement", waitTime = 30L)
    public ResponseBo createProfitAndLossStatement(@RequestBody @Validated ProfitAndLossStatementDto probAndLossStatementDto) {
        ProfitAndLossStatementVo profitAndLossStatement = profitAndLossService.createProfitAndLossStatement(probAndLossStatementDto);
        return new ResponseBo<>(profitAndLossStatement);
    }

    @ApiOperation(value = "重新统计损益表", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.ADD, description = "财务中心/财务报表/重新统计损益表")
    @PostMapping("recomputeProfitLossStatement")
    @RedisLock(value = "fzh:createProfitAndLossStatement", waitTime = 60L)
    public ResponseBo recomputeProfitLossStatement(@RequestBody @Validated RecomputeProfitLossStatementDto recomputeProfitLossStatement) {
        profitAndLossService.recomputeProfitLossStatement(recomputeProfitLossStatement);
        return ResponseBo.ok();
    }

    @ApiOperation(value = "异步信息查询", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.ADD, description = "财务中心/财务报表/异步信息查询")
    @GetMapping("financialStatsAsyncQuery/{fkTableName}")
    public ResponseBo financialStatsAsyncQuery(@PathVariable("fkTableName") String fkTableName) {
        FinancialStatsAsyncVo financialStatsAsyncVo = profitAndLossService.financialStatsAsyncQuery(fkTableName);
        return new ResponseBo(financialStatsAsyncVo);
    }

}
