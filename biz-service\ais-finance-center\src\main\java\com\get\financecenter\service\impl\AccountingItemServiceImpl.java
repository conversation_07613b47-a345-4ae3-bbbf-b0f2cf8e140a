package com.get.financecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.utils.BeanUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.financecenter.dao.AccountingItemMapper;
import com.get.financecenter.dao.ExpenseClaimFeeTypeMapper;
import com.get.financecenter.dao.PaymentFeeTypeMapper;
import com.get.financecenter.dao.PaymentMethodTypeMapper;
import com.get.financecenter.dao.ProviderMapper;
import com.get.financecenter.dao.ReceiptFeeTypeMapper;
import com.get.financecenter.dao.ReceiptMethodTypeMapper;
import com.get.financecenter.dao.TravelClaimFeeTypeMapper;
import com.get.financecenter.dto.AccountingItemDto;
import com.get.financecenter.entity.AccountingItem;
import com.get.financecenter.entity.ExpenseClaimFeeType;
import com.get.financecenter.entity.PaymentFeeType;
import com.get.financecenter.entity.PaymentMethodType;
import com.get.financecenter.entity.Provider;
import com.get.financecenter.entity.ReceiptFeeType;
import com.get.financecenter.entity.ReceiptMethodType;
import com.get.financecenter.entity.TravelClaimFeeType;
import com.get.financecenter.enums.AccountingTypeEnum;
import com.get.financecenter.enums.AssetTypesEnum;
import com.get.financecenter.enums.RelationTargetKeyEnum;
import com.get.financecenter.service.AccountingItemService;
import com.get.financecenter.vo.AccountingItemDropdownMenuVo;
import com.get.financecenter.vo.AccountingItemSelectVo;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class AccountingItemServiceImpl extends BaseServiceImpl<AccountingItemMapper, AccountingItem> implements AccountingItemService {

    @Resource
    private AccountingItemMapper accountingItemMapper;
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Resource
    private ProviderMapper providerMapper;
    @Resource
    private UtilService utilService;

    @Resource
    private PaymentMethodTypeMapper paymentMethodTypeMapper;
    @Resource
    private PaymentFeeTypeMapper paymentFeeTypeMapper;
    @Resource
    private ExpenseClaimFeeTypeMapper expenseClaimFeeTypeMapper;
    @Resource
    private ReceiptMethodTypeMapper receiptMethodTypeMapper;
    @Resource
    private TravelClaimFeeTypeMapper travelClaimFeeTypeMapper;
    @Resource
    private ReceiptFeeTypeMapper receiptFeeTypeMapper;


    /**
     * 科目下拉框
     *
     * @return
     */
    @Override
    public AccountingItemSelectVo accountingItemSelectById(Long accountingItemId) {
        AccountingItem accountingItem = accountingItemMapper.selectById(accountingItemId);
        AccountingItemSelectVo accountingItemSelectVo = BeanUtil.copy(accountingItem, AccountingItemSelectVo.class);
        List<AccountingItemSelectVo> children = getChildrenAccountingItems(accountingItemId);
        accountingItemSelectVo.setChildrenAccountingItemSelect(children);
        return accountingItemSelectVo;
    }

    /**
     * 关联项下拉框
     *
     * @param relationTargetKey
     * @param companyId
     * @return
     */
    @Override
    public List<BaseSelectEntity> relationTargetSelect(String relationTargetKey, Long companyId) {
        List<BaseSelectEntity> baseSelectEntityList = new ArrayList<>();
        if (relationTargetKey.equals(RelationTargetKeyEnum.STAFF.relationTargetKey)) {
            baseSelectEntityList = permissionCenterClient.getStaffByCompanyId(companyId);
            for (BaseSelectEntity baseSelectEntity : baseSelectEntityList) {
                //名字格式：黄宇豪（Allen）
                baseSelectEntity.setFullName(GeneralTool.isNotEmpty(baseSelectEntity.getNameChn()) ?
                        baseSelectEntity.getNameChn() + "(" + baseSelectEntity.getName() + ")" : baseSelectEntity.getName());
            }
        } else if (relationTargetKey.equals(RelationTargetKeyEnum.PROVIDER.relationTargetKey)) {
            List<Provider> providers = providerMapper.selectList(Wrappers.<Provider>lambdaQuery()
                    .eq(Provider::getFkCompanyId, companyId)
                    .eq(Provider::getIsActive, true));
            for (Provider provider : providers) {
                BaseSelectEntity baseSelectEntity = new BaseSelectEntity();
                baseSelectEntity.setId(provider.getId());
                baseSelectEntity.setName(provider.getName());
                baseSelectEntity.setNameChn(provider.getName());
                baseSelectEntity.setFullName(provider.getName());
                baseSelectEntityList.add(baseSelectEntity);
            }
        }
        return baseSelectEntityList;
    }


    /**
     * 递归获取所有子科目
     */
    private List<AccountingItemSelectVo> getChildrenAccountingItems(Long parentId) {
        List<AccountingItem> children = accountingItemMapper.selectList(Wrappers.<AccountingItem>lambdaQuery()
                .eq(AccountingItem::getFkParentAccountingItemId, parentId)
                .eq(AccountingItem::getIsActive, true));

        if (GeneralTool.isEmpty(children)) {
            return Collections.emptyList();
        }

        return children.stream().map(item -> {
            AccountingItemSelectVo childVo = BeanUtil.copy(item, AccountingItemSelectVo.class);
            List<AccountingItemSelectVo> subChildren = getChildrenAccountingItems(item.getId());
            childVo.setChildrenAccountingItemSelect(subChildren);
            return childVo;
        }).collect(Collectors.toList());
    }


    /**
     * 科目类型下拉数据
     *
     * @return
     */
    @Override
    public List<BaseSelectEntity> accountingItemTypeSelect() {
        List<BaseSelectEntity> typeMap = AccountingTypeEnum.asSelectList();
        return typeMap;
    }

    /**
     * 余额方向下拉数据
     *
     * @return
     */
    @Override
    public List<BaseSelectEntity> balanceDirection() {
        List<BaseSelectEntity> typeMap = AssetTypesEnum.asSelectList();
        return typeMap;
    }

    /**
     * 查询科目列表
     *
     * @param accountingItemDto
     * @param page
     * @return
     */
    @Override
    public List<AccountingItemSelectVo> getAccountingItemAll(AccountingItemDto accountingItemDto, Page page) {
        if (GeneralTool.isEmpty(accountingItemDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        if (GeneralTool.isNotEmpty(accountingItemDto.getKeyWord())) {
            accountingItemDto.setKeyWord(accountingItemDto.getKeyWord().replace(" ", "").trim());
        }
        LambdaQueryWrapper<AccountingItem> wrapper = new LambdaQueryWrapper();
        IPage<AccountingItem> pages = this.page(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), wrapper);
        List<AccountingItemSelectVo> accountingItemSelectVos = accountingItemMapper.getAccountingItemAll(pages, accountingItemDto);
        page.setAll((int) pages.getTotal());
        if (GeneralTool.isNotEmpty(accountingItemSelectVos)) {
            for (AccountingItemSelectVo accountingItemSelectVo : accountingItemSelectVos) {
                //处理科目类型参数
                if (GeneralTool.isNotEmpty(accountingItemSelectVo.getType())) {
                    accountingItemSelectVo.setTypeName(AccountingTypeEnum.getNameById(accountingItemSelectVo.getType()));
                }

                //处理余额方向参数
                if (GeneralTool.isNotEmpty(accountingItemSelectVo.getDirection())) {
                    accountingItemSelectVo.setDirectionName(AssetTypesEnum.getNameById(accountingItemSelectVo.getDirection()));
                }

                //处理是否激活参数
                if (GeneralTool.isNotEmpty(accountingItemSelectVo.getIsActive())) {
                    accountingItemSelectVo.setIsActiveName(accountingItemSelectVo.getIsActive() == 1 ? "是" : "否");
                }
            }
        }
        return accountingItemSelectVos;
    }

    @Override
    public List<AccountingItemDropdownMenuVo> getAccountingItemByGrade(Integer grade) {
        if (GeneralTool.isEmpty(grade)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("grade_null"));
        }
        //查询grade的上级科目
        List<AccountingItemDropdownMenuVo> accountingItemByGrade = accountingItemMapper.getAccountingItemByGrade(grade - 1);
        return accountingItemByGrade;
    }

    @Override
    public List<AccountingItemDropdownMenuVo> getAccountingItemDropdownMenu() {
        List<AccountingItem> accountingItems = accountingItemMapper.getAccountingItemDropdownMenu();
        //构建树形数据
        Map<Long, AccountingItemDropdownMenuVo> voMap = new HashMap<>();

        Map<Integer, List<AccountingItemDropdownMenuVo>> levelMap = new HashMap<>();
        for (int i = 1; i <= 5; i++) {
            levelMap.put(i, new ArrayList<>());
        }
        //创建所有对象，并初始化属性
        for (AccountingItem item : accountingItems) {
            AccountingItemDropdownMenuVo vo = new AccountingItemDropdownMenuVo();
            vo.setId(item.getId());
            vo.setGrade(item.getGrade());

            // 拼接显示标签：科目编码 + 科目名称
            String label = item.getCode() + "-" + item.getCodeName();
            vo.setName(label);

            vo.setChildrenAccountingItem(new ArrayList<>()); // 初始化子节点列表
            voMap.put(item.getId(), vo);

            // 按层级分组
            if (item.getGrade() >= 1 && item.getGrade() <= 5) {
                levelMap.get(item.getGrade()).add(vo);
            }
        }

        for (int level = 5; level >= 1; level--) {
            for (AccountingItemDropdownMenuVo currentVo : levelMap.get(level)) {
                // 获取对应的实体对象
                AccountingItem item = accountingItems.stream()
                        .filter(i -> i.getId().equals(currentVo.getId()))
                        .findFirst()
                        .orElse(null);

                if (item != null && item.getFkParentAccountingItemId() != null) {
                    AccountingItemDropdownMenuVo parentVo = voMap.get(item.getFkParentAccountingItemId());
                    if (parentVo != null) {
                        parentVo.getChildrenAccountingItem().add(currentVo);
                    }
                }
            }
        }

        List<AccountingItemDropdownMenuVo> rootNodes = levelMap.get(1);

        for (int level = 1; level <= 5; level++) {
            levelMap.get(level).sort(Comparator.comparing(AccountingItemDropdownMenuVo::getGrade));
        }
        return rootNodes;
    }

    @Override
    public Integer save(AccountingItemDto accountingItemDto) {
        checkParams(accountingItemDto);
        AccountingItem accountingItem = BeanCopyUtils.objClone(accountingItemDto, AccountingItem::new);
        //校验名称或编码是否重复
        if (accountingItemMapper.checkNameOrCode(accountingItem.getCode(), accountingItem.getCodeName()) > 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("the_name_or_code_already_exists"));
        }
        utilService.setCreateInfo(accountingItem);
        return accountingItemMapper.insert(accountingItem);

    }

    @Override
    public Integer updateById(AccountingItemDto accountingItemDto) {
        if (GeneralTool.isEmpty(accountingItemDto.getId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        AccountingItem updateAccountingItem = BeanCopyUtils.objClone(accountingItemDto, AccountingItem::new);
        AccountingItem accountingItem = accountingItemMapper.selectById(updateAccountingItem.getId());
        if (GeneralTool.isEmpty(accountingItem)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        utilService.setUpdateInfo(updateAccountingItem);

        if (!updateAccountingItem.getCode().equals(accountingItem.getCode())){
            if (accountingItemMapper.checkNameOrCode(updateAccountingItem.getCode(), null) > 0) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("the_name_or_code_already_exists") + " (" + updateAccountingItem.getCode()+")");
            }
        }
        else if ( !updateAccountingItem.getCodeName().equals(accountingItem.getCodeName())) {
            if (accountingItemMapper.checkNameOrCode(null, updateAccountingItem.getCodeName()) > 0) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("the_name_or_code_already_exists") +" ("+ updateAccountingItem.getCodeName()+")");
            }
        }
        return accountingItemMapper.updateById(updateAccountingItem);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        //校验是否有类型绑定该科目,如果有，则不能删除
        validateAccountDeletion(id);

        AccountingItem updateAccountingItem = accountingItemMapper.selectById(id);
        if (GeneralTool.isEmpty(updateAccountingItem)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }
        accountingItemMapper.deleteById(id);
    }

    private void validateAccountDeletion(Long id) {
        // 使用静态映射表维护实体类与错误键的对应关系
        Map<Class<?>, String> errorKeyMap = new HashMap<>();
        errorKeyMap.put(PaymentMethodType.class, "accounting_item_bound_payment_method_type");
        errorKeyMap.put(PaymentFeeType.class, "accounting_item_bound_payment_fee_type");
        errorKeyMap.put(ReceiptFeeType.class, "accounting_item_bound_receipt_fee_type");
        errorKeyMap.put(ExpenseClaimFeeType.class, "accounting_item_bound_expense_claim_fee_type");
        errorKeyMap.put(ReceiptMethodType.class, "accounting_item_bound_receipt_method_type");
        errorKeyMap.put(TravelClaimFeeType.class, "accounting_item_bound_travel_claim_fee_type");  // 共享同一个错误键

        // 按顺序检查所有关联实体
        Object[] entities = {
                paymentMethodTypeMapper.selectByFkAccountingItemId(id),
                paymentFeeTypeMapper.selectByFkAccountingItemId(id),
                receiptFeeTypeMapper.selectByFkAccountingItemId(id),
                expenseClaimFeeTypeMapper.selectByFkAccountingItemId(id),
                receiptMethodTypeMapper.selectByFkAccountingItemId(id),
                travelClaimFeeTypeMapper.selectByFkAccountingItemId(id)
        };

        for (Object entity : entities) {
            if (entity != null) {
                String errorKey = errorKeyMap.get(entity.getClass());
                if (errorKey != null) {
                    throw new GetServiceException(LocaleMessageUtils.getMessage(errorKey));
                }
            }
        }
    }


    //参数校验的方法
    private void checkParams(AccountingItemDto accountingItemDto) {
        if (GeneralTool.isEmpty(accountingItemDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        if (GeneralTool.isEmpty(accountingItemDto.getCode())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("accounting_item_code_null"));
        }
        if (accountingItemDto.getCode().length() > 50) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("character_limit_exceeded")+"50" + " (" + accountingItemDto.getCode() + ")");
        }
        if (GeneralTool.isEmpty(accountingItemDto.getCodeName())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("accounting_item_code_name_null"));
        }
        if (accountingItemDto.getCodeName().length() > 50) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("character_limit_exceeded")+"50" + " (" + accountingItemDto.getCodeName() + ")");
        }
        if (GeneralTool.isEmpty(accountingItemDto.getType())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("accounting_item_type_null"));
        }
        if (GeneralTool.isEmpty(accountingItemDto.getGrade())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("accounting_item_grade_null"));
        }
        if (GeneralTool.isEmpty(accountingItemDto.getDirection())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("accounting_item_direction_null"));
        }
        if (GeneralTool.isEmpty(accountingItemDto.getIsActive())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("accounting_item_is_active_null"));
        }
        if (GeneralTool.isEmpty(accountingItemDto.getFkParentAccountingItemId())) {
            accountingItemDto.setFkParentAccountingItemId(0L);
        }
    }

    /**
     * 传入科目id，迭代获取所有的子科目的子科目的List
     */
    @Override
    public List<Long> getChildrenAccountingItems(List<Long> parentIds) {
        List<Long> allChildren = new ArrayList<>();
        List<AccountingItem> accountingItems = accountingItemMapper.selectList(Wrappers.<AccountingItem>lambdaQuery().in(AccountingItem::getFkParentAccountingItemId, parentIds).eq(AccountingItem::getIsActive, true));
        if (GeneralTool.isEmpty(accountingItems)) {
            return allChildren;
        } else {
            List<Long> ids = accountingItems.stream().map(AccountingItem::getId).collect(Collectors.toList());
            allChildren.addAll(ids);
            allChildren.addAll(getChildrenAccountingItems(ids));
        }
        return allChildren;
    }


}
