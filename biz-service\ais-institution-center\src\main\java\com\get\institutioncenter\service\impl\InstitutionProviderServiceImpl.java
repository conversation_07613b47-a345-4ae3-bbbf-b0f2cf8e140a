package com.get.institutioncenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.DataConverter;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.log.feign.ILogClient;
import com.get.core.log.model.LogInstitutionProvider;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.redis.cache.GetRedis;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.file.utils.FileUtils;
import com.get.institutioncenter.component.TranslationHelp;
import com.get.institutioncenter.dao.AreaCountryMapper;
import com.get.institutioncenter.dao.InstitutionChannelMapper;
import com.get.institutioncenter.dao.InstitutionGroupMapper;
import com.get.institutioncenter.dao.InstitutionMapper;
import com.get.institutioncenter.dao.InstitutionProviderAreaCountryMapper;
import com.get.institutioncenter.dao.InstitutionProviderInstitutionMapper;
import com.get.institutioncenter.dao.InstitutionProviderMapper;
import com.get.institutioncenter.dao.InstitutionProviderTypeMapper;
import com.get.institutioncenter.dto.ContactPersonDto;
import com.get.institutioncenter.dto.ContractDto;
import com.get.institutioncenter.dto.InstitutionChannelCompanyQueryDto;
import com.get.institutioncenter.dto.InstitutionChannelCompanySearchDto;
import com.get.institutioncenter.dto.InstitutionDto;
import com.get.institutioncenter.dto.InstitutionProviderAreaCountryDto;
import com.get.institutioncenter.dto.InstitutionProviderCompanyDto;
import com.get.institutioncenter.dto.InstitutionProviderDto;
import com.get.institutioncenter.dto.InstitutionProviderInstitutionChannelDto;
import com.get.institutioncenter.dto.InstitutionProviderInstitutionDto;
import com.get.institutioncenter.dto.NewsDto;
import com.get.institutioncenter.dto.ProviderInstitutionCompanyUpdateDto;
import com.get.institutioncenter.dto.ProviderInstitutionRelationDto;
import com.get.institutioncenter.dto.query.ContractQueryDto;
import com.get.institutioncenter.dto.query.InstitutionProviderQueryDto;
import com.get.institutioncenter.dto.query.NewsQueryDto;
import com.get.institutioncenter.entity.InstitutionChannel;
import com.get.institutioncenter.entity.InstitutionGroup;
import com.get.institutioncenter.entity.InstitutionProvider;
import com.get.institutioncenter.entity.InstitutionProviderAreaCountry;
import com.get.institutioncenter.entity.InstitutionProviderCompany;
import com.get.institutioncenter.entity.InstitutionProviderInstitution;
import com.get.institutioncenter.entity.InstitutionProviderType;
import com.get.institutioncenter.po.InstitutionProviderPo;
import com.get.institutioncenter.service.IAreaCityService;
import com.get.institutioncenter.service.IAreaCountryService;
import com.get.institutioncenter.service.IAreaStateService;
import com.get.institutioncenter.service.IContactPersonService;
import com.get.institutioncenter.service.IContractService;
import com.get.institutioncenter.service.IDeleteService;
import com.get.institutioncenter.service.IInstitutionProviderAreaCountryService;
import com.get.institutioncenter.service.IInstitutionProviderCompanyService;
import com.get.institutioncenter.service.IInstitutionProviderInstitutionChannelService;
import com.get.institutioncenter.service.IInstitutionProviderInstitutionService;
import com.get.institutioncenter.service.IInstitutionProviderService;
import com.get.institutioncenter.service.IInstitutionService;
import com.get.institutioncenter.service.IMediaAndAttachedService;
import com.get.institutioncenter.service.INewsService;
import com.get.institutioncenter.utils.MyStringUtils;
import com.get.institutioncenter.vo.ContactPersonVo;
import com.get.institutioncenter.vo.ContractVo;
import com.get.institutioncenter.vo.ExportInstitutionVo;
import com.get.institutioncenter.vo.InstitutionProviderAreaCountryVo;
import com.get.institutioncenter.vo.InstitutionProviderContractReminderVo;
import com.get.institutioncenter.vo.InstitutionProviderInstitutionChannelVo;
import com.get.institutioncenter.vo.InstitutionProviderInstitutionVo;
import com.get.institutioncenter.vo.InstitutionProviderVo;
import com.get.institutioncenter.vo.InstitutionProvidersAndAreaCountryVo;
import com.get.institutioncenter.vo.InstitutionVo;
import com.get.institutioncenter.vo.MediaAndAttachedVo;
import com.get.institutioncenter.vo.NewsVo;
import com.get.institutioncenter.vo.ProviderInstitutionRelationVo;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.permissioncenter.vo.CompanyVo;
import com.get.permissioncenter.vo.tree.CompanyTreeVo;
import com.get.pmpcenter.dto.institution.UnbindProviderInstitutionDto;
import com.get.pmpcenter.feign.IPmpCenterClient;
import com.get.remindercenter.dto.RemindTaskDto;
import com.get.remindercenter.feign.IReminderCenterClient;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.StringJoiner;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * @author: Sea
 * @create: 2020/7/14 12:06
 * @verison: 1.0
 * @description:
 */
@Service
public class InstitutionProviderServiceImpl extends BaseServiceImpl<InstitutionProviderMapper, InstitutionProvider> implements IInstitutionProviderService {
    @Resource
    private InstitutionProviderMapper providerMapper;
    @Resource
    @Lazy
    private IContactPersonService contactPersonService;
    @Resource
    private InstitutionGroupMapper institutionGroupMapper;
    @Resource
    private InstitutionProviderTypeMapper institutionProviderTypeMapper;
    @Resource
    private InstitutionProviderInstitutionMapper institutionProviderInstitutionMapper;
    @Resource
    private IInstitutionProviderCompanyService providerCompanyService;
    @Resource
    private IInstitutionProviderInstitutionService providerInstitutionService;
    @Resource
    private IContractService contractService;
    @Resource
    private INewsService newsService;
    @Resource
    @Lazy
    private IInstitutionService institutionService;
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Resource
    private ILogClient logClient;
    @Resource
    private IAreaCountryService areaCountryService;
    @Resource
    private IAreaStateService areaStateService;
    @Resource
    private IAreaCityService areaCityService;
    @Resource
    private IInstitutionProviderAreaCountryService institutionProviderAreaCountryService;
    @Resource
    private UtilService utilService;
    @Resource
    private InstitutionChannelMapper institutionChannelMapper;
    @Resource
    private IDeleteService deleteService;
    @Resource
    private InstitutionProviderAreaCountryMapper institutionProviderAreaCountryMapper;
    @Resource
    private IInstitutionProviderInstitutionChannelService institutionProviderInstitutionChannelService;
    @Resource
    private GetRedis redisClient;
    @Resource
    private TranslationHelp translationHelp;
    @Resource
    private IReminderCenterClient reminderCenterClient;
    @Resource
    private IInstitutionProviderInstitutionService institutionProviderInstitutionService;

    @Resource
    private IMediaAndAttachedService mediaAndAttachedService;
    @Resource
    private InstitutionProviderMapper institutionProviderMapper;

    @Resource
    private InstitutionMapper institutionMapper;
    @Resource
    private IPmpCenterClient pmpCenterClient;

    @Resource
    private AreaCountryMapper areaCountryMapper;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long addInstitutionProvider(InstitutionProviderDto providerVo) {
        InstitutionProvider institutionProvider = BeanCopyUtils.objClone(providerVo, InstitutionProvider::new);
        //设置合同状态为0
        institutionProvider.setContractStatus(0);
        utilService.updateUserInfoToEntity(institutionProvider);
        if (!validateAdd(providerVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("name_exists"));
        }
        if (!validateAdd(providerVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("name_exists"));
        }

        int flag = providerMapper.insertSelective(institutionProvider);
        //设置编号
        institutionProvider.setNum(MyStringUtils.getInstitutionProviderNum(institutionProvider.getId()));
        providerMapper.updateById(institutionProvider);
        //设置渠道-多选
        if (GeneralTool.isNotEmpty(providerVo.getInstitutionChannelIds())) {
            List<Long> fkInstitutionChannelIdList = providerVo.getInstitutionChannelIds().stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());
            if (GeneralTool.isNotEmpty(fkInstitutionChannelIdList)) {
                for (Long fkInstitutionChannelId : fkInstitutionChannelIdList) {
                    InstitutionProviderInstitutionChannelDto institutionProviderInstitutionChannelDto = new InstitutionProviderInstitutionChannelDto();
                    institutionProviderInstitutionChannelDto.setFkInstitutionProviderId(institutionProvider.getId());
                    institutionProviderInstitutionChannelDto.setFkInstitutionChannelId(fkInstitutionChannelId);
                    institutionProviderInstitutionChannelService.addInstitutionProviderInstitutionChannel(institutionProviderInstitutionChannelDto);
                }
            }
        }

        if (flag > 0) {
            //添加所属公司id
            Long companyId;
            //假如没有传入公司id则取当前登录人的公司id当新增的 校代提供的公司id
            if (GeneralTool.isNotEmpty(providerVo.getFkCompanyId())) {
                companyId = providerVo.getFkCompanyId();
            } else {
                companyId = SecureUtil.getFkCompanyId();
            }
            Set<Long> ids = new HashSet<>();
            //获取当前登录人的公司id和子公司id
            CompanyVo companyVo = permissionCenterClient.getCompanyVo().getData();
            for(CompanyVo companyVo1:companyVo.getChildCompanyDto()){
                ids.add(companyVo1.getId());
            }
            ids.add(companyId);
            //学校提供商和公司关联
            for (Long id:ids){
                InstitutionProviderCompanyDto relation = new InstitutionProviderCompanyDto();
                relation.setFkCompanyId(id);
                relation.setFkInstitutionProviderId(institutionProvider.getId());
                providerCompanyService.addRelation(relation);
            }
            //添加业务国家
            List<Long> areaCountryIds = providerVo.getAreaCountryIds();
            if (GeneralTool.isNotEmpty(areaCountryIds)) {
                for (Long areaCountryId : areaCountryIds) {
                    InstitutionProviderAreaCountryDto areaCountryVo = new InstitutionProviderAreaCountryDto();
                    areaCountryVo.setFkAreaCountryId(areaCountryId);
                    areaCountryVo.setFkInstitutionProviderId(institutionProvider.getId());
                    institutionProviderAreaCountryService.addProviderAreaCountry(areaCountryVo);
                }
            }
        }
        List<RemindTaskDto> remindTaskDtos = new ArrayList<>();
        if (GeneralTool.isNotEmpty(providerVo.getAppCommissionDeadline())) {
            RemindTaskDto remindTaskDto = new RemindTaskDto();
            remindTaskDto.setTaskTitle("申请佣金截止时间");
            //邮件方式发送
            remindTaskDto.setRemindMethod("0");
            //默认设置执行中
            remindTaskDto.setStatus(1);
            //默认背景颜色
            remindTaskDto.setTaskBgColor("#3788d8");
            remindTaskDto.setFkStaffId(SecureUtil.getStaffId());
            remindTaskDto.setStartTime(providerVo.getAppCommissionDeadline());
//            remindTaskDto.setStartTime(DateUtil.getAdvanceDateByDay(offerItemVo.getDepositDeadline(),7));
            remindTaskDto.setFkTableName(TableEnum.INSTITUTION_PROVIDER.key);
            remindTaskDto.setFkTableId(institutionProvider.getId());
            remindTaskDtos.add(remindTaskDto);
        }
        reminderCenterClient.batchAdd(remindTaskDtos);
        return institutionProvider.getId();
    }

    @Override
    public Integer updateContractStatus(Long id, Integer contractStatus) {
        //id为空返回
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        // 根据ID查询实体，如果不存在则抛出异常
        InstitutionProvider institutionProvider = institutionProviderMapper.selectById(id);
        if (institutionProvider == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }

        if (contractStatus == 1 && institutionProvider.getContractStatus() == 1) {
            return 1;
        }

        // 合同状态只能是0,1,2，否则直接返回0
        if (!Arrays.asList(0, 1, 2).contains(contractStatus)) {
            return 0;
        }

        // 定义更新条件和内容
        LambdaUpdateWrapper<InstitutionProvider> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(InstitutionProvider::getId, id);

        // 判断合同状态转换的合法性并设置新状态
//        if ((contractStatus == 1 && institutionProvider.getContractStatus() != 0)
//                || (contractStatus == 2 && institutionProvider.getContractStatus() != 1)) {
//            return 0; // 不允许的状态转换
//        }

        InstitutionProvider provider = new InstitutionProvider();
        provider.setContractStatus(contractStatus);

        // 执行更新操作
        int updateCount = institutionProviderMapper.update(provider, wrapper);

        return updateCount;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        deleteService.deleteValidateProviderByProviderId(id);
        providerMapper.deleteById(id);
        //删除提醒任务
        reminderCenterClient.batchUpdate(new ArrayList<>(), TableEnum.INSTITUTION_PROVIDER.key, id);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public InstitutionProviderVo updateInstitutionProvider(InstitutionProviderDto providerVo) {
        InstitutionProvider institutionProvider = BeanCopyUtils.objClone(providerVo, InstitutionProvider::new);
        utilService.updateUserInfoToEntity(institutionProvider);
        InstitutionProvider preProvider = providerMapper.selectById(providerVo.getId());
        if (GeneralTool.isEmpty(preProvider)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        //记录日志
        recordProviderLog(providerVo, preProvider);
        if (!validateUpdate(providerVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("name_exists"));
        }
        providerMapper.updateByIdWithNull(institutionProvider);

        //所属渠道修改
        List<Long> fkInstitutionChannelIdList = providerVo.getInstitutionChannelIds();
        if (GeneralTool.isNotEmpty(fkInstitutionChannelIdList)) {
            institutionProviderInstitutionChannelService.deleteByProviderId(providerVo.getId());
            for (Long fkInstitutionChannelId : fkInstitutionChannelIdList) {
                InstitutionProviderInstitutionChannelDto institutionProviderInstitutionChannelDto = new InstitutionProviderInstitutionChannelDto();
                institutionProviderInstitutionChannelDto.setFkInstitutionProviderId(providerVo.getId());
                institutionProviderInstitutionChannelDto.setFkInstitutionChannelId(fkInstitutionChannelId);
                institutionProviderInstitutionChannelService.addInstitutionProviderInstitutionChannel(institutionProviderInstitutionChannelDto);
            }
        }
        //业务国家修改
        List<Long> areaCountryIds = providerVo.getAreaCountryIds();
        if (GeneralTool.isNotEmpty(areaCountryIds)) {
            institutionProviderAreaCountryService.deleteByProviderId(providerVo.getId());
            for (Long areaCountryId : areaCountryIds) {
                InstitutionProviderAreaCountryDto areaCountryVo = new InstitutionProviderAreaCountryDto();
                areaCountryVo.setFkAreaCountryId(areaCountryId);
                areaCountryVo.setFkInstitutionProviderId(institutionProvider.getId());
                institutionProviderAreaCountryService.addProviderAreaCountry(areaCountryVo);
            }
        }
        //111111
        List<RemindTaskDto> remindTaskDtos = new ArrayList<>();
        if (GeneralTool.isNotEmpty(providerVo.getAppCommissionDeadline())) {
            RemindTaskDto remindTaskDto = new RemindTaskDto();
            remindTaskDto.setTaskTitle("申请佣金截止时间");
            //邮件方式发送
            remindTaskDto.setRemindMethod("0");
            //默认设置执行中
            remindTaskDto.setStatus(1);
            //默认背景颜色
            remindTaskDto.setTaskBgColor("#3788d8");
            remindTaskDto.setFkStaffId(SecureUtil.getStaffId());
            remindTaskDto.setStartTime(providerVo.getAppCommissionDeadline());
            remindTaskDto.setFkTableName(TableEnum.INSTITUTION_PROVIDER.key);
            remindTaskDto.setFkTableId(institutionProvider.getId());
            remindTaskDtos.add(remindTaskDto);
        }
        reminderCenterClient.batchUpdate(remindTaskDtos, TableEnum.INSTITUTION_PROVIDER.key, institutionProvider.getId());
        return findInstitutionProviderById(institutionProvider.getId());
    }

    @Override
    public List<InstitutionProviderVo> getProviderList(InstitutionProviderQueryDto providerVo, Page page) {

        int status = 0;
        LambdaQueryWrapper<InstitutionProvider> wrapper = new LambdaQueryWrapper();
        if(!SecureUtil.validateCompany(providerVo.getFkCompanyId())){
           return new ArrayList<>();
        }
        if (GeneralTool.isEmpty(providerVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }

        //根据公司id查询所在公司以及旗下公司对应的学校供应商
        List<Long> providerIdBYCompanyIds = null;
        if (GeneralTool.isEmpty(providerVo.getFkCompanyId())) {
            //providerIds：登录人所能看到的所有的学校提供商id
            List<Long> providerIds = getCompanyIds();
            providerIdBYCompanyIds = getCompanyIds();
            wrapper.in(InstitutionProvider::getId, providerIds);
        } else {
            //根据公司id查询所在公司以及旗下公司对应的学校供应商
            List<Long> providerIds = queryByCompanyId(providerVo);
            providerIdBYCompanyIds = queryByCompanyId(providerVo);
            wrapper.in(InstitutionProvider::getId, providerIds);
        }

        //根据当前登陆人的业务国家进行过滤，排除不在自己业务国家范围内的国家
        List<Long> areaCountryProviderIds = null;
        List<Long> providerIds = institutionProviderAreaCountryService.getProviderIds(providerVo.getAreaCountryIds());
        if (GeneralTool.isNotEmpty(providerIds)) {
            wrapper.in(InstitutionProvider::getId, providerIds);
            areaCountryProviderIds = providerIds;
        }
        if (GeneralTool.isNotEmpty(providerVo.getFkAreaCountryId())) {
            wrapper.eq(InstitutionProvider::getFkAreaCountryId, providerVo.getFkAreaCountryId());
        }
        if (GeneralTool.isNotEmpty(providerVo.getFkInstitutionProviderTypeId())) {
            wrapper.eq(InstitutionProvider::getFkInstitutionProviderTypeId, providerVo.getFkInstitutionProviderTypeId());
        }
//        if (GeneralTool.isNotEmpty(providerVo.getFkInstitutionChannelId())) {
//            wrapper.eq(InstitutionProvider::getFkInstitutionChannelId, providerVo.getFkInstitutionChannelId());
//        }
        //渠道过滤
        List<Long> institutionChannelIds=null;
        if (GeneralTool.isNotEmpty(providerVo.getFkInstitutionChannelId())) {
            List<Long> institutionProviderIds = institutionProviderInstitutionChannelService.getInstitutionProviderIdsByChannelId(providerVo.getFkInstitutionChannelId());
            if (GeneralTool.isNotEmpty(institutionProviderIds)) {
                wrapper.in(InstitutionProvider::getId, institutionProviderIds);
                institutionChannelIds = institutionProviderIds;
            }
        }

        if (GeneralTool.isNotEmpty(providerVo.getFkInstitutionGroupId())) {
            wrapper.eq(InstitutionProvider::getFkInstitutionGroupId, providerVo.getFkInstitutionGroupId());
        }
        if (GeneralTool.isNotEmpty(providerVo.getIsActive())) {
            wrapper.eq(InstitutionProvider::getIsActive, providerVo.getIsActive());
        }


        //过滤合同状态
//        if (GeneralTool.isNotEmpty(providerVo.getContractStatus())) {
//            status = providerVo.getContractStatus();
//            if (providerVo.getContractStatus() == 11 || providerVo.getContractStatus() == 12) {
//                providerVo.setContractStatus(1);
//            }
//            wrapper.eq(InstitutionProvider::getContractStatus, providerVo.getContractStatus());
//        }

        if (GeneralTool.isNotEmpty(providerVo.getPublicLevel())) {
            wrapper.like(InstitutionProvider::getPublicLevel, providerVo.getPublicLevel());
        }
        if (GeneralTool.isNotEmpty(providerVo.getKeyWord())) {
            wrapper.and(wrapper_ ->
                    wrapper_.like(InstitutionProvider::getName, providerVo.getKeyWord())
                            .or().like(InstitutionProvider::getNum, providerVo.getKeyWord())
                            .or().like(InstitutionProvider::getNameChn, providerVo.getKeyWord()));
        }
        wrapper.orderByDesc(InstitutionProvider::getId);

        List<InstitutionProviderVo> byContractStatus = null;
        IPage<InstitutionProviderVo> pages = null;
        pages = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        byContractStatus = institutionProviderMapper.findByContractStatus(pages, providerVo, providerIdBYCompanyIds, areaCountryProviderIds, institutionChannelIds);
//        if(status==11||status==12){
//            //获取分页数据
//            pages = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
//           byContractStatus = institutionProviderMapper.findByContractStatus(pages, providerVo, providerIdBYCompanyIds, areaCountryProviderIds, institutionChannelIds,status);
//
//        }

//        else {
//            //获取分页数据
//            pages = this.page(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), wrapper);
//        }



//        //有合同的数据
        List<InstitutionProviderVo> institutionProviderList = byContractStatus;
//        if (status==11||status==12) {
//            institutionProviderList = byContractStatus;
//        }else {
////            institutionProviderList = pages.getRecords();
//        }

//
//        // 生效期内的提供商集合
//        List<InstitutionProvider> validProviders = new ArrayList<>();
//        // 已过期的提供商集合
//        List<InstitutionProvider> expiredProviders = new ArrayList<>();
//        //有合同并且已过期的提供商id
//        List<Long> expiredContractSchoolProviderIds = institutionProviderMapper.getExpiredContractSchoolProviderIds();
//        //有合同并且生效中的提供商id
//        List<Long> activeContractProviderIds = institutionProviderMapper.getActiveContractSchoolProviderIds();

        // 遍历有合同的数据列表
//        for (InstitutionProviderVo provider : institutionProviderList) {
//            if (provider.getContractStatus() != null && provider.getContractStatus() == 1) {
//                if (activeContractProviderIds.contains(provider.getId())) {
//                    // 生效中合同
//                    provider.setContractStatus(11); //11表示"生效中"
//                } else if (expiredContractSchoolProviderIds.contains(provider.getId())) {
//                    // 已过期合同
//                    provider.setContractStatus(12); //12表示"已过期"
//                } else {
//                    // 无有效合同（未审批或没有符合条件的合同）
//                    provider.setContractStatus(0); // 设置为无合同状态
//                }
//
//            }
//
//        }
            page.setAll((int) pages.getTotal());

        //获取学校提供商id集合
        Set<Long> institutionProviderIdSet = null;
//        if (GeneralTool.isNotEmpty(providerVo.getContractStatus())) {
//            if (status == 11) {
//                institutionProviderIdSet = validProviders.stream().map(InstitutionProvider::getId).collect(Collectors.toSet());
//                institutionProviderList = validProviders;
//            } else if (status == 12) {
//                institutionProviderIdSet = expiredProviders.stream().map(InstitutionProvider::getId).collect(Collectors.toSet());
//                institutionProviderList = expiredProviders;
//            } else if (providerVo.getContractStatus() == 0 || providerVo.getContractStatus() == 2) {
//                institutionProviderIdSet = institutionProviderList.stream().map(InstitutionProvider::getId).collect(Collectors.toSet());
//            }
//        } else {
//            institutionProviderIdSet = institutionProviderList.stream().map(InstitutionProvider::getId).collect(Collectors.toSet());
//        }
        institutionProviderIdSet = institutionProviderList.stream().map(InstitutionProviderVo::getId).collect(Collectors.toSet());

        institutionProviderIdSet.removeIf(Objects::isNull);
        //查询学校提供商id和对应学校数量
        List<InstitutionProviderVo> providerIdAndInstitutionCount = institutionProviderInstitutionMapper.getInstitutionCountByInstitutionProviderIds(institutionProviderIdSet, SecureUtil.getCompanyIds());
        //将id和对应学校数量存到map中
        Map<Long, Integer> institutionCountMap = providerIdAndInstitutionCount.stream().collect(Collectors.toMap(InstitutionProviderVo::getId, InstitutionProviderVo::getInstitutionCount));
        List<InstitutionProviderVo> collect = institutionProviderList.stream().map(institutionProvider -> {
            InstitutionProviderVo institutionProviderVo = BeanCopyUtils.objClone(institutionProvider, InstitutionProviderVo::new);
            //根据map映射关系取出对应提供商id的学校数量并设置到dto中
            if (GeneralTool.isEmpty(institutionCountMap.get(institutionProvider.getId()))) {
                institutionProviderVo.setInstitutionCount(0);
            } else {
                institutionProviderVo.setInstitutionCount(institutionCountMap.get(institutionProvider.getId()));
            }
            if (GeneralTool.isNotEmpty(institutionProvider.getContractStatus())) {
                String value = ProjectExtraEnum.getValueByKey(Integer.valueOf(institutionProvider.getContractStatus()), ProjectExtraEnum.CONTRACT_STATUS);
                institutionProviderVo.setContractStatusName(value);
            }
            return institutionProviderVo;
        }).collect(Collectors.toList());
        setCompanyName(collect);
        //设置业务国家名称
        setAreaCountryNames(collect);
        return collect;
    }


    @Override
    public List<InstitutionProviderVo> getInstitutionProviderList(InstitutionProviderDto providerVo, Page page) {
        LambdaQueryWrapper<InstitutionProvider> wrapper = new LambdaQueryWrapper();
        IPage<InstitutionProvider> pages = this.page(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), wrapper);
        List<InstitutionProviderVo> providerList = providerMapper.getProviderList(pages, providerVo, SecureUtil.getCompanyIds());
        page.setAll((int) pages.getTotal());
        setCompanyName(providerList);
        //设置业务国家名称
        setAreaCountryNames(providerList);
        return providerList;
    }

    /**
     * 设置业务国家名称
     *
     * @param providerList
     */
    private void setAreaCountryNames(List<InstitutionProviderVo> providerList) {
        List<Long> providerIds = providerList.stream().map(InstitutionProviderVo::getId).filter(Objects::nonNull).collect(Collectors.toList());
        List<InstitutionProviderAreaCountryVo> institutionProviderAreaCountryList = institutionProviderAreaCountryMapper.getInstitutionProviderAreaCountryStr(providerIds);
        Map<Long, String> countryNamesMap = institutionProviderAreaCountryList.stream().collect(Collectors.toMap(InstitutionProviderAreaCountryVo::getFkInstitutionProviderId, InstitutionProviderAreaCountryVo::getAreaCountryNames));
        for (InstitutionProviderVo institutionProviderVo : providerList) {
            institutionProviderVo.setAreaCountryNames(countryNamesMap.get(institutionProviderVo.getId()));
        }
    }


    @Override
    public InstitutionProviderVo findInstitutionProviderById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        //InstitutionProvider institutionProvider = providerMapper.selectById(id);
        InstitutionProviderQueryDto providerQueryDto = new InstitutionProviderQueryDto();
        providerQueryDto.setId(id);
        InstitutionProviderVo providerDto = providerMapper.findByContractStatusById(id);
//        InstitutionProviderVo providerDto = BeanCopyUtils.objClone(institutionProvider, InstitutionProviderVo::new);
        setFullName(providerDto);
        setContractStatusName(providerDto);
        //查询国家名称
        if (GeneralTool.isNotEmpty(providerDto.getFkAreaCountryId())) {
            providerDto.setFkAreaCountryName(areaCountryService.getCountryNameById(providerDto.getFkAreaCountryId()));
        }
        if (GeneralTool.isNotEmpty(providerDto.getFkAreaStateId())) {
            providerDto.setFkAreaStateName(areaStateService.getStateFullNameById(providerDto.getFkAreaStateId()));
        }
        if (GeneralTool.isNotEmpty(providerDto.getFkAreaCountryId())) {
            providerDto.setFkAreaCityName(areaCityService.getCityFullNameById(providerDto.getFkAreaCityId()));
        }
        if (GeneralTool.isNotEmpty(providerDto.getFkInstitutionGroupId())) {
            providerDto.setFkInstitutionGroupName(institutionGroupMapper.getNameById(providerDto.getFkInstitutionGroupId()));
        }


//        if (GeneralTool.isNotEmpty(providerDto.getFkInstitutionChannelId())) {
//            providerDto.setFkInstitutionChannelName(institutionChannelMapper.getNameById(providerDto.getFkInstitutionChannelId()));
//        }
        StringJoiner stringJoiner = new StringJoiner(" ");
        if (GeneralTool.isNotEmpty(providerDto.getPublicLevel())) {
            List<String> result = Arrays.asList(providerDto.getPublicLevel().split(","));
            for (String key : result) {
                stringJoiner.add(LocaleMessageUtils.getMessage(ProjectExtraEnum.getEnum(Integer.valueOf(key), ProjectExtraEnum.PUBLIC_OBJECTS).name()));
            }
            providerDto.setPublicLevelName(stringJoiner.toString());
        }
        //设置所属渠道
        providerDto.setInstitutionChannelNames(institutionProviderInstitutionChannelService.getInstitutionChannelNamesById(id));
        providerDto.setInstitutionChannelIds(institutionProviderInstitutionChannelService.getInstitutionChannelIdsById(id));
        providerDto.setAreaCountryNames(institutionProviderAreaCountryService.getAreaCountryNameByProviderId(id));
        providerDto.setAreaCountryIds(institutionProviderAreaCountryService.getAreaCountryIdByProviderId(id));
        providerDto.setTypeName(institutionProviderTypeMapper.getNameById(providerDto.getFkInstitutionProviderTypeId()));
//        String language = SecureUtil.getLocale();
        String language = SecureUtil.getLocale();
        InstitutionProviderPo providerPo = BeanCopyUtils.objClone(providerDto, InstitutionProviderPo::new);
        if (GeneralTool.isNotEmpty(providerPo) && GeneralTool.isNotEmpty(ProjectKeyEnum.getInitialValue(language))) {
            translationHelp.translation(Collections.singletonList(providerPo), ProjectKeyEnum.getInitialValue(language));
        }
        if (GeneralTool.isNotEmpty(providerPo)) {
            providerDto = BeanCopyUtils.objClone(providerPo, InstitutionProviderVo::new);
        }
        return providerDto;
    }

    private void setContractStatusName(InstitutionProviderVo provider) {
//        //有合同并且生效中的提供商id
//        List<Long> expiredContractSchoolProviderIds = institutionProviderMapper.getActiveContractSchoolProviderIds();
//        // 获取有已过期合同的提供商ID列表
//        List<Long> expiredContractProviderIds = institutionProviderMapper.getExpiredContractSchoolProviderIds();
//        if (provider.getContractStatus() == 1) {
//            if (expiredContractSchoolProviderIds.contains(provider.getId())) {
//                // 生效中合同
//                provider.setContractStatus(11); // 11表示"生效中"
//            } else if (expiredContractProviderIds.contains(provider.getId())) {
//                // 已过期合同
//                provider.setContractStatus(12); // 12表示"已过期"
//            } else {
//                // 无有效合同（未审批或没有符合条件的合同）
//                provider.setContractStatus(0); // 设置为无合同状态
//            }
//        }
        String value = ProjectExtraEnum.getValueByKey(Integer.valueOf(provider.getContractStatus()), ProjectExtraEnum.CONTRACT_STATUS);
        provider.setContractStatusName(value);
    }


    @Override
    public ResponseBo<String> getInfo(Long providerId, Long channelId) {
        if (GeneralTool.isNull(providerId) || GeneralTool.isNull(channelId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        BaseSelectEntity info = institutionChannelMapper.getInfo(providerId, channelId);
        if (info == null) {
            return new ResponseBo<>(null);
        }
        return new ResponseBo<>(info.getName() + info.getNameChn());
    }

    @Override
    public Long addContactPerson(ContactPersonDto contactPersonDto) {
        if (GeneralTool.isEmpty(contactPersonDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        contactPersonDto.setFkTableName(TableEnum.INSTITUTION_PROVIDER.key);
        return contactPersonService.addProviderContactPerson(contactPersonDto);
    }


    @Override
    public List<ContactPersonVo> getInstitutionContactPersonDtos(ContactPersonDto contactPersonDto, Page page) {
        if (GeneralTool.isEmpty(contactPersonDto.getFkTableId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        contactPersonDto.setFkTableName(TableEnum.INSTITUTION_PROVIDER.key);
        return contactPersonService.getContactPersonDtos(contactPersonDto, page);
    }

    @Override
    public Long addNews(NewsDto newsDto) {
        newsDto.setFkTableName(TableEnum.INSTITUTION_PROVIDER.key);
        return newsService.addNews(newsDto);
    }

    @Override
    public List<NewsVo> getNewsData(NewsQueryDto newsVo, Page page) {
        if (GeneralTool.isEmpty(newsVo.getFkTableId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        //查询学校下面的新闻
        newsVo.setFkTableId(newsVo.getFkTableId());
        newsVo.setFkTableName(TableEnum.INSTITUTION_PROVIDER.key);
        return newsService.datas(newsVo, page);
    }

    @Override
    public List<Long> getInstitutionProviderIdsByName(String institutionProviderName) {
        List<Long> institutionProviderIds = providerMapper.getInstitutionProviderIds("%" + institutionProviderName + "%");
        if (GeneralTool.isEmpty(institutionProviderIds)) {
            institutionProviderIds.add(0L);
        }
        return institutionProviderIds;
    }

    @Override
    public List<InstitutionProviderVo> getInstitutionProvidersByName(String keyword) {
        List<InstitutionProviderVo> institutionProviders = providerMapper.getInstitutionProvidersByName(keyword);
        if (GeneralTool.isEmpty(institutionProviders)) {
            return Collections.emptyList();
        }
        return institutionProviders;
    }

    @Override
    public void editProviderInstitutionRelation(List<InstitutionProviderInstitutionDto> providerInstitutionVos) {
        providerInstitutionService.editProviderInstitutionRelation(providerInstitutionVos);
    }

    @Override
    public void editProviderCompanyRelation(List<InstitutionProviderCompanyDto> providerCompanyVos) {
        providerCompanyService.editProviderCompanyRelation(providerCompanyVos);
    }

    @Override
    public List<CompanyTreeVo> getProviderCompanyRelation(Long providerId) {

        return providerCompanyService.getProviderCompanyRelation(providerId);
    }

    @Override
    public List<ProviderInstitutionRelationVo> getProviderInstitutionRelation(ProviderInstitutionRelationDto
                                                                                      relationVo, Page page) {
        return providerInstitutionService.getProviderInstitutionRelation(relationVo, page);
    }

    @Override
    public String getInstitutionProviderNameById(Long id) {
        return providerMapper.getInstitutionProviderNameById(id);
    }

    @Override
    public Map<Long, String> getInstitutionProviderNamesByIds(Set<Long> ids) {
        if (GeneralTool.isEmpty(ids)) {
            return null;
        }
        LambdaQueryWrapper<InstitutionProvider> wrapper = new LambdaQueryWrapper();
        wrapper.in(InstitutionProvider::getId, ids);
        List<InstitutionProvider> institutionProviders = providerMapper.selectList(wrapper);
        Map<Long, String> map = new HashMap<>();
        if (GeneralTool.isEmpty(institutionProviders)) {
            return map;
        }
        for (InstitutionProvider institutionProvider : institutionProviders) {
            String institutionProviderName = GeneralTool.isEmpty(institutionProvider.getName()) ? "" : institutionProvider.getName();
            StringBuilder builder = new StringBuilder(institutionProviderName);
            if (GeneralTool.isNotEmpty(institutionProvider.getNameChn())) {
                builder.append("（");
                builder.append(institutionProvider.getNameChn());
                builder.append("）");
            }
            map.put(institutionProvider.getId(), builder.toString());
        }
        return map;
    }

    @Override
    public String getInstitutionProviderTypeById(Long id) {
        return providerMapper.getInstitutionProviderTypeById(id);
    }

    @Override
    public Set<Long> getInstitutionProviderByInstitution(Long institutionId) {
        return providerMapper.getInstitutionProviderByInstitution(institutionId);
    }

    @Override
    public List<InstitutionProviderVo> getInstitutionProviderList(Long companyId, String name) {
        List<Long> companyIds = new ArrayList<>();
        if (GeneralTool.isEmpty(companyId)) {
            companyIds = SecureUtil.getCompanyIds();
        } else {
            if (!SecureUtil.validateCompany(companyId)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
            }
            companyIds.add(companyId);
        }
        return providerMapper.getInstitutionProviderList(companyIds, name);
    }


    @Override
    public List<InstitutionProviderVo> getInstitutionProviders(Long companyId, String name) {
        List<Long> companyIds = new ArrayList<>();
        companyIds.add(companyId);
        return providerMapper.getInstitutionProviderList(companyIds, name);
    }


    /**
     * Author Cream
     * Description : //获取所有提供商下拉
     * Date 2023/6/16 12:33
     * Params:
     * Return
     */
    @Override
    public List<BaseSelectEntity> getAllInstitutionSelection() {
        List<InstitutionProvider> institutionProviders = providerMapper.selectList(Wrappers.lambdaQuery());
        List<BaseSelectEntity> result = new ArrayList<>(institutionProviders.size());
        for (InstitutionProvider provider : institutionProviders) {
            BaseSelectEntity baseSelectEntity = new BaseSelectEntity();
            baseSelectEntity.setNameChn(provider.getNameChn());
            baseSelectEntity.setId(provider.getId());
            baseSelectEntity.setName(provider.getName());
            baseSelectEntity.setFullName(provider.getName() + "（" + provider.getNameChn() + "）");
            result.add(baseSelectEntity);
        }
        return result;
    }

    @Override
    public List<BaseSelectEntity> getInstitutionProviderSelect(Long companyId) {
        if (!SecureUtil.validateCompany(companyId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
        }
        return providerMapper.getInstitutionProviderSelect(companyId);
    }

    @Override
    public List<BaseSelectEntity> getInstitutionProviderListByCountry(Long countryId) {
        return providerMapper.getInstitutionProviderListByCountry(countryId, SecureUtil.getFkCompanyId());
    }


    @Override
    public List<ContractVo> getContract(ContractQueryDto contractVo, Page page) {
        if (GeneralTool.isEmpty(contractVo.getFkInstitutionProviderId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("providerId_null"));
        }
        List<ContractVo> contractDtoList = contractService.getAllContract(contractVo, page, SecureUtil.getCompanyIds());
//        String language = SecureUtil.getLocale();
        String language = SecureUtil.getLocale();
        if (GeneralTool.isNotEmpty(contractDtoList) && GeneralTool.isNotEmpty(ProjectKeyEnum.getInitialValue(language))) {
            translationHelp.translation(contractDtoList, ProjectKeyEnum.getInitialValue(language));
        }
        Set<Long> ids = contractDtoList.stream().map(ContractVo::getId).collect(Collectors.toSet());
        //附件文件
        if (GeneralTool.isNotEmpty(ids)) {
            Map<Long, List<MediaAndAttachedVo>> mediaAndAttachedDtoByTableIds = mediaAndAttachedService.getMediaAndAttachedDtoByTableIds(ids, TableEnum.INSTITUTION_CONTRACT.key);
            for (ContractVo contractDto : contractDtoList) {
                if (GeneralTool.isNotEmpty(mediaAndAttachedDtoByTableIds)) {
                    contractDto.setMediaAndAttachedDto(mediaAndAttachedDtoByTableIds.get(contractDto.getId()));
                }
            }
        }
        return contractDtoList;
    }

    @Override
    public Long addContract(ContractDto contractDto) {
        return contractService.addProviderContract(contractDto);
    }

    @Override
    public List<InstitutionVo> getInstitution(InstitutionDto institutionDto, Page page) {
        if (GeneralTool.isEmpty(institutionDto.getFkProviderId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("providerId_null"));
        }
        if(page == null){
            return institutionService.getInstitutionDto(institutionDto, null);
        }
        return institutionService.getInstitutionDto(institutionDto, page);
    }

    @Override
    public List<InstitutionProviderVo> getProviderList(List<Long> countryIds) {
        LambdaQueryWrapper<InstitutionProvider> wrapper = new LambdaQueryWrapper();
        if (GeneralTool.isEmpty(countryIds)) {
            return null;
        }
        wrapper.in(InstitutionProvider::getFkAreaCountryId, countryIds);
        List<InstitutionProvider> providerList = providerMapper.selectList(wrapper);
        return providerList.stream().map(institutionProvider ->
                BeanCopyUtils.objClone(institutionProvider, InstitutionProviderVo::new)).collect(Collectors.toList());
    }

    @Override
    public String getNameByProviderId(Long providerId) {
        if (GeneralTool.isEmpty(providerId)) {
            return null;
        }
        InstitutionProvider institutionProvider = providerMapper.selectById(providerId);
        if (GeneralTool.isEmpty(institutionProvider)) {
            return null;
        }
        return institutionProvider.getName();
    }

    @Override
    public Map<Long, String> getNameByProviderIds(Set<Long> providerIds) {
        Map<Long, String> map = new HashMap<>();
        if (GeneralTool.isEmpty(providerIds)) {
            return map;
        }
        LambdaQueryWrapper<InstitutionProvider> wrapper = new LambdaQueryWrapper();
        wrapper.in(InstitutionProvider::getId, providerIds);
        List<InstitutionProvider> institutionProviders = providerMapper.selectList(wrapper);
        if (GeneralTool.isEmpty(institutionProviders)) {
            return map;
        }
        for (InstitutionProvider institutionProvider : institutionProviders) {
            map.put(institutionProvider.getId(), institutionProvider.getName());
        }
        return map;
    }

    /**
     * 根据学校获取绑定的学校提供商下拉框数据
     *
     * @Date 16:06 2021/8/18
     * <AUTHOR>
     */
    @Override
    public List<BaseSelectEntity> getInstitutionProviderListByInstitution(Long institutionId, Long studentCompanyId) {
        List<Long> companyIds = SecureUtil.getCompanyIds();
        return providerMapper.getInstitutionProviderListByInstitution(institutionId, studentCompanyId, companyIds);
    }

    /**
     * 根据提供商id获取业务国家下拉框数据
     *
     * @Date 10:31 2021/9/9
     * <AUTHOR>
     */
    @Override
    public List<BaseSelectEntity> getCountrySelectByInstitutionProvider(Long providerId) {
        return providerMapper.getCountrySelectByInstitutionProvider(providerId);
    }

    /**
     * feign调用 获取所有的渠道Map
     *
     * @Date 16:01 2021/11/23
     * <AUTHOR>
     */
    @Override
    public Map<Long, String> getInstitutionProviderChannel() {
        LambdaQueryWrapper<InstitutionChannel> wrapper = new LambdaQueryWrapper();
        List<InstitutionChannel> institutionChannels = institutionChannelMapper.selectList(wrapper);
        Map<Long, String> map = new HashMap<>();
        for (InstitutionChannel institutionChannel : institutionChannels) {
            map.put(institutionChannel.getId(), institutionChannel.getName());
        }
        return map;
    }

    /**
     * feign调用 获取渠道名By id
     *
     * @param
     * @return
     */
    @Override
    public String getInstitutionProviderChannelById(Long id) {
        InstitutionChannel institutionChannel = institutionChannelMapper.selectById(id);
        return institutionChannel.getName();
    }

    @Override
    public Map<Long, String> getInstitutionProviderChannelByIds(Set<Long> ids) {
        List<InstitutionChannel> institutionChannels = institutionChannelMapper.selectBatchIds(ids);
        Map<Long, String> map = new HashMap<>(institutionChannels.size());
        for (InstitutionChannel institutionChannel : institutionChannels) {
            map.put(institutionChannel.getId(), institutionChannel.getName());
        }
        return map;
    }

    /**
     * 获取下拉名称
     *
     * @param ids
     * @return
     */
    @Override
    public Map<Long, String> getInstitutionProviderSelectNamesByIds(Set<Long> ids) {
        Map<Long, String> map = new HashMap<>();
        List<InstitutionProvider> institutionProviders = providerMapper.selectBatchIds(ids);
        if (GeneralTool.isNotEmpty(institutionProviders)) {
            for (InstitutionProvider institutionProvider : institutionProviders) {
                String name = "";
                if (GeneralTool.isNotEmpty(institutionProvider.getIsActive())) {
                    if (institutionProvider.getIsActive()) {
                        name = "【" + institutionProvider.getNum() + "】";
                    } else {
                        name = "【无效】";
                    }
                } else {
                    name = "【无效】";
                }
                name = name + institutionProvider.getNameChn() + "（" + institutionProvider.getName() + "）";
                map.put(institutionProvider.getId(), name);
            }
        }
        return map;
    }

    /**
     * 根据公司id获取提供商ids
     *
     * @param companyId
     * @return
     */
    @Override
    public List<Long> getInstitutionProviderIdsByCompanyId(Long companyId) {
        return providerMapper.getInstitutionProviderIdsByCompanyId(companyId);
    }

    @Override
    public List<Long> getInstitutionProviderIdsByCompanyIds(List<Long> fkCompanyIds) {
        return providerMapper.getInstitutionProviderIdsByCompanyIds(fkCompanyIds);
    }


    @Override
    public List<Long> getInstitutionProviderIdsByCompanyIdAndName(List<Long> companyIds, String institutionProviderName) {
        return providerMapper.getInstitutionProviderIdsByCompanyIdAndName(companyIds, institutionProviderName);
    }

    @Override
    public List<Long> getInstitutionProviderIdsByChannelName(String channelName) {
        return providerMapper.getInstitutionProviderIdsByChannelName(channelName);
    }

    @Override
    public List<Long> getInstitutionProviderIdsByChannelGroup(Long groupId, Long channelId) {
        return providerMapper.getInstitutionProviderIdsByChannelGroup(groupId, channelId);
    }


    @Override
    public List<InstitutionProviderVo> getInstitutionProviders(InstitutionProviderDto institutionProviderDto, Page page) {
        LambdaQueryWrapper<InstitutionProviderInstitution> wrapper = new LambdaQueryWrapper();
        wrapper.eq(InstitutionProviderInstitution::getFkInstitutionId, institutionProviderDto.getFkInstitutionId());
        List<InstitutionProviderInstitution> ipis = institutionProviderInstitutionMapper.selectList(wrapper);
        //获取分页数据
        LambdaQueryWrapper<InstitutionProvider> wrapper1 = new LambdaQueryWrapper();
        IPage<InstitutionProvider> pages = this.page(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), wrapper1);
        if(GeneralTool.isEmpty(institutionProviderDto.getFkCompanyIds()) && GeneralTool.isEmpty(institutionProviderDto.getFkCompanyId())){
            institutionProviderDto.setFkCompanyIds(getCompanyIds());
        } else if (GeneralTool.isNotEmpty(institutionProviderDto.getFkCompanyIds())) {
            if(!SecureUtil.validateCompanys(institutionProviderDto.getFkCompanyIds())){
                return new ArrayList<>();
            }
        }else if(GeneralTool.isNotEmpty(institutionProviderDto.getFkCompanyId())){
            if(!SecureUtil.validateCompany(institutionProviderDto.getFkCompanyId())){
                return new ArrayList<>();
            }
        }
        List<InstitutionProviderVo> institutionProviderVos = providerMapper.getInstitutionProviders(pages, institutionProviderDto);
        page.setAll((int) pages.getTotal());
        for (InstitutionProviderVo institutionProviderVo : institutionProviderVos) {
            if (GeneralTool.isNotEmpty(institutionProviderVo.getFkInstitutionProviderTypeId())) {
                institutionProviderVo.setTypeName(institutionProviderTypeMapper.getNameById(institutionProviderVo.getFkInstitutionProviderTypeId()));
            }
            if (GeneralTool.isNotEmpty(institutionProviderVo.getFkInstitutionGroupId())) {
                institutionProviderVo.setFkInstitutionGroupName(institutionGroupMapper.getNameById(institutionProviderVo.getFkInstitutionGroupId()));
            }
            StringJoiner stringJoiner = new StringJoiner(" ");
            if (GeneralTool.isNotEmpty(institutionProviderVo.getPublicLevel())) {
                List<String> result = Arrays.asList(institutionProviderVo.getPublicLevel().split(","));
                for (String key : result) {
                    stringJoiner.add(LocaleMessageUtils.getMessage(ProjectExtraEnum.getEnum(Integer.valueOf(key), ProjectExtraEnum.PUBLIC_OBJECTS).name()));
                }
                institutionProviderVo.setPublicLevelName(stringJoiner.toString());
            }
            for (InstitutionProviderInstitution i : ipis) {
                if (i.getFkInstitutionProviderId().equals(institutionProviderVo.getId())) {
                    institutionProviderVo.setIsSelect(true);
                }
            }
        }
        return institutionProviderVos;
    }

    /**
     * @return void
     * @Description: 记录修改日志
     * @Param [providerVo, preProvider]
     * <AUTHOR>
     */
    private void recordProviderLog(InstitutionProviderDto providerVo, InstitutionProvider preProvider) {
        //修改前的值 所属集团
        Long beforeGroupId = preProvider.getFkInstitutionGroupId();
        beforeGroupId = GeneralTool.isNotEmpty(beforeGroupId) ? beforeGroupId : 0L;
        //所属渠道
//        Long beforeChannelId = preProvider.getFkInstitutionChannelId();
//        beforeChannelId = GeneralTool.isNotEmpty(beforeChannelId) ? beforeChannelId : 0L;

        //修改后的值
        Long afterGroupId = providerVo.getFkInstitutionGroupId();
        afterGroupId = GeneralTool.isNotEmpty(afterGroupId) ? afterGroupId : 0L;
        //所属渠道
//        Long afterChannelId = providerVo.getFkInstitutionChannelId();
//        afterChannelId = GeneralTool.isNotEmpty(afterChannelId) ? afterChannelId : 0L;

        //日志vo
        LogInstitutionProvider logInstitutionProvider = new LogInstitutionProvider();
        logInstitutionProvider.setFkInstitutionProviderId(providerVo.getId());
        if (!beforeGroupId.equals(afterGroupId)) {
            //类型
            logInstitutionProvider.setOptType(ProjectKeyEnum.UPDATE_INSTITUTION_GROUP.key);
            //获取名称
            String beforeGroupName = institutionGroupMapper.getNameById(beforeGroupId);
            beforeGroupName = GeneralTool.isNotEmpty(beforeGroupName) ? beforeGroupName : "null";
            //获取名称
            String afterGroupName = institutionGroupMapper.getNameById(afterGroupId);
            afterGroupName = GeneralTool.isNotEmpty(afterGroupName) ? afterGroupName : "null";

            logInstitutionProvider.setOptInfo(beforeGroupName + "->" + afterGroupName);
            logClient.saveLogInstitutionProvider(logInstitutionProvider);
        }
        //渠道id字段删除，修改为多选，这里对应修改
//        if (!beforeChannelId.equals(afterChannelId)) {
//            //类型
//            logProviderVo.setOptType(ProjectKeyEnum.UPDATE_INSTITUTION_CHANNEL.key);
//            //获取名称
//            String beforeChannelName = institutionChannelMapper.getNameById(beforeChannelId);
//            beforeChannelName = GeneralTool.isNotEmpty(beforeChannelName) ? beforeChannelName : "null";
//            //获取名称
//            String afterChannelName = institutionChannelMapper.getNameById(afterChannelId);
//            afterChannelName = GeneralTool.isNotEmpty(afterChannelName) ? afterChannelName : "null";
//
//            logProviderVo.setOptInfo(beforeChannelName + "->" + afterChannelName);
//            feignLogService.add(logProviderVo);
//        }
    }

    private List<Long> getCompanyIds() {
        //companyIds = 获取当前登陆人所在公司以及所有的下属公司的id
        List<Long> companyIds = SecureUtil.getCompanyIds();
        //根据可以看到的公司id  把其公司所有关联的学校提供商查询出来
        List<Long> providerIds = providerCompanyService.getRelationByCompanyId(companyIds);
        if (GeneralTool.isEmpty(providerIds)) {
            providerIds = new ArrayList<>();
            providerIds.add(0L);
        }
        return providerIds;
    }

    /**
     * 给学校提供商dto 公司名和国家名称字段赋值
     *
     * @param collect
     * @
     */
    private void setCompanyName(List<InstitutionProviderVo> collect) {
        if (GeneralTool.isNotEmpty(collect)) {
            Map<Long, String> companyMap = getCompanyMap();
            Set<Long> institutionProviderIds = collect.stream().map(InstitutionProviderVo::getId).collect(Collectors.toSet());
            //根据提供商ids查询公司
            Map<Long, LinkedList<Long>> relationByProviderIds = new HashMap<>();
            if (GeneralTool.isNotEmpty(institutionProviderIds)) {
                relationByProviderIds = providerCompanyService.getRelationByProviderIds(institutionProviderIds);
            }

            //获取集合的所有国家ids
            Set<Long> countryIdSet = collect.stream().map(InstitutionProviderVo::getFkAreaCountryId).collect(Collectors.toSet());
            //获取集合的所有州省ids
            Set<Long> stateIdSet = collect.stream().map(InstitutionProviderVo::getFkAreaStateId).collect(Collectors.toSet());
            //获取集合的所有城市ids
            Set<Long> cityIdSet = collect.stream().map(InstitutionProviderVo::getFkAreaCityId).collect(Collectors.toSet());

            //根据国家ids获取国家名称
            Map<Long, String> countryNamesByIds = new HashMap<>();
            if (GeneralTool.isNotEmpty(countryIdSet)) {
                countryNamesByIds = areaCountryService.getCountryNamesByIds(countryIdSet);
            }
            //根据州省ids获取州省名称
            Map<Long, String> stateNamesByIds = new HashMap<>();
            if (GeneralTool.isNotEmpty(stateIdSet)) {
                stateNamesByIds = areaStateService.getStateNamesByIds(stateIdSet);
            }
            //根据城市ids获取城市名称
            Map<Long, String> cityNamesByIds = new HashMap<>();
            if (GeneralTool.isNotEmpty(cityIdSet)) {
                cityNamesByIds = areaCityService.getCityNamesByIds(cityIdSet);
            }

            //所属集团ids
            Set<Long> institutionGroupIds = collect.stream().map(InstitutionProviderVo::getFkInstitutionGroupId).collect(Collectors.toSet());
//            //来源渠道ids
//            Set<Long> institutionChannelIds = collect.stream().map(InstitutionProviderVo::getFkInstitutionChannelId).collect(Collectors.toSet());
            //提供商类型ids
            Set<Long> institutionProviderTypeIds = collect.stream().map(InstitutionProviderVo::getFkInstitutionProviderTypeId).collect(Collectors.toSet());

            //根据所属集团Ids获取名称
            Map<Long, String> institutionGroupByInstitutionGroupIds = getInstitutionGroupByInstitutionGroupIds(institutionGroupIds);
            //根据渠道来源Ids获取名称
//            Map<Long, String> institutionGroupByInstitutionChannelId = getInstitutionGroupByInstitutionChannelId(institutionChannelIds);
            //根据提供商类型Ids获取名称
            Map<Long, String> institutionGroupByInstitutionProviderTypeIds = getInstitutionGroupByInstitutionProviderTypeIds(institutionProviderTypeIds);

            for (InstitutionProviderVo institutionProviderVo : collect) {
                //institutionProviderVo.setAreaCountryNames(institutionProviderAreaCountryService.getAreaCountryNameByProviderId(institutionProviderVo.getId()));
                StringBuilder builder = new StringBuilder();
                LinkedList<Long> companyIds = relationByProviderIds.get(institutionProviderVo.getId());
                if (GeneralTool.isNotEmpty(companyIds)) {
                    companyIds.retainAll(SecureUtil.getCompanyIds());
                    for (Long companyId : companyIds) {
                        String name = companyMap.get(companyId);
                        builder.append(name).append("，");
                    }
                    institutionProviderVo.setCompanyName(sub(builder));
                }
                setFullName(institutionProviderVo);
                setProperty(institutionProviderVo, countryNamesByIds, stateNamesByIds, cityNamesByIds, institutionGroupByInstitutionGroupIds,
//                        institutionGroupByInstitutionChannelId,
                        institutionGroupByInstitutionProviderTypeIds);
                StringJoiner stringJoiner = new StringJoiner(" ");
                if (GeneralTool.isNotEmpty(institutionProviderVo.getPublicLevel())) {
                    List<String> result = Arrays.asList(institutionProviderVo.getPublicLevel().split(","));
                    for (String key : result) {
                        stringJoiner.add(LocaleMessageUtils.getMessage(ProjectExtraEnum.getEnum(Integer.valueOf(key), ProjectExtraEnum.PUBLIC_OBJECTS).name()));
                    }
                    institutionProviderVo.setPublicLevelName(stringJoiner.toString());
                }
            }
        }
    }

    private void setProperty(InstitutionProviderVo providerDto, Map<Long, String> countryNamesByIds,
                             Map<Long, String> stateNamesByIds, Map<Long, String> cityNamesByIds,
                             Map<Long, String> institutionGroupByInstitutionGroupIds,
//                             Map<Long, String> institutionGroupByInstitutionChannelId,
                             Map<Long, String> institutionGroupByInstitutionProviderTypeIds) {
        //查询国家名称
        if (GeneralTool.isNotEmpty(providerDto.getFkAreaCountryId())) {
            providerDto.setFkAreaCountryName(countryNamesByIds.get(providerDto.getFkAreaCountryId()));
        }
        //查询州省名称
        if (GeneralTool.isNotEmpty(providerDto.getFkAreaStateId())) {
            providerDto.setFkAreaStateName(stateNamesByIds.get(providerDto.getFkAreaStateId()));
        }
        //查询城市名称
        if (GeneralTool.isNotEmpty(providerDto.getFkAreaCountryId())) {
            providerDto.setFkAreaCityName(cityNamesByIds.get(providerDto.getFkAreaCityId()));
        }
        //查询所属集团名称
        if (GeneralTool.isNotEmpty(providerDto.getFkInstitutionGroupId())) {
            providerDto.setFkInstitutionGroupName(institutionGroupByInstitutionGroupIds.get(providerDto.getFkInstitutionGroupId()));
        }
        // 渠道改为多选后搜索对应修改
//        //查询所属渠道名称
//        if (GeneralTool.isNotEmpty(providerDto.getFkInstitutionChannelId())) {
//            providerDto.setFkInstitutionChannelName(institutionGroupByInstitutionChannelId.get(providerDto.getFkInstitutionChannelId()));
//        }
        //查询提供商类型名称
        providerDto.setTypeName(institutionGroupByInstitutionProviderTypeIds.get(providerDto.getFkInstitutionProviderTypeId()));
    }

    /**
     * 根据所属集团Ids获取名称
     *
     * @param institutionGroupIds
     * @return
     */
    private Map<Long, String> getInstitutionGroupByInstitutionGroupIds(Set<Long> institutionGroupIds) {
        Map<Long, String> map = new HashMap<>();
        if (GeneralTool.isEmpty(institutionGroupIds)) {
            return map;
        }
        LambdaQueryWrapper<InstitutionGroup> wrapper = new LambdaQueryWrapper();
        wrapper.in(InstitutionGroup::getId, institutionGroupIds);
        List<InstitutionGroup> institutionGroups = institutionGroupMapper.selectList(wrapper);
        if (GeneralTool.isEmpty(institutionGroups)) {
            return map;
        }
        for (InstitutionGroup institutionGroup : institutionGroups) {
            String name = GeneralTool.isEmpty(institutionGroup.getName()) ? "" : institutionGroup.getName();
            StringBuilder sb = new StringBuilder(name);
            //如果中文名称不为空，则名称继续拼接中文名称
            if (GeneralTool.isNotEmpty(institutionGroup.getNameChn())) {
                sb.append("（");
                sb.append(institutionGroup.getNameChn());
                sb.append("）");
            }
            map.put(institutionGroup.getId(), sb.toString());
        }
        return map;
    }

    /**
     * 根据渠道来源Ids获取名称
     *
     * @param institutionChannelId
     * @return
     */
    private Map<Long, String> getInstitutionGroupByInstitutionChannelId(Set<Long> institutionChannelId) {
        Map<Long, String> map = new HashMap<>();
        if (GeneralTool.isEmpty(institutionChannelId)) {
            return map;
        }
        LambdaQueryWrapper<InstitutionChannel> wrapper = new LambdaQueryWrapper();
        wrapper.in(InstitutionChannel::getId, institutionChannelId);
        List<InstitutionChannel> institutionChannels = institutionChannelMapper.selectList(wrapper);
        if (GeneralTool.isEmpty(institutionChannels)) {
            return map;
        }
        for (InstitutionChannel institutionChannel : institutionChannels) {
            String name = GeneralTool.isEmpty(institutionChannel.getName()) ? "" : institutionChannel.getName();
            StringBuilder sb = new StringBuilder(name);
            //如果中文名称不为空，则名称继续拼接中文名称
            if (GeneralTool.isNotEmpty(institutionChannel.getNameChn())) {
                sb.append("（");
                sb.append(institutionChannel.getNameChn());
                sb.append("）");
            }
            map.put(institutionChannel.getId(), sb.toString());
        }
        return map;
    }

    /**
     * 根据提供商类型Ids获取名称
     *
     * @param institutionProviderTypeIds
     * @return
     */
    private Map<Long, String> getInstitutionGroupByInstitutionProviderTypeIds(Set<Long> institutionProviderTypeIds) {
        Map<Long, String> map = new HashMap<>();
        if (GeneralTool.isEmpty(institutionProviderTypeIds)) {
            return map;
        }
        LambdaQueryWrapper<InstitutionProviderType> wrapper = new LambdaQueryWrapper();
        wrapper.in(InstitutionProviderType::getId, institutionProviderTypeIds);
        List<InstitutionProviderType> institutionProviderTypes = institutionProviderTypeMapper.selectList(wrapper);
        if (GeneralTool.isEmpty(institutionProviderTypes)) {
            return map;
        }
        for (InstitutionProviderType institutionProviderType : institutionProviderTypes) {
            map.put(institutionProviderType.getId(), institutionProviderType.getTypeName());
        }
        return map;
    }

    private void setFullName(InstitutionProviderVo dto) {
        String fullName = dto.getName();
        if (GeneralTool.isNotEmpty(dto.getNameChn())) {
            fullName = fullName + "（" + dto.getNameChn() + "）";
        }
        dto.setFullName(fullName);
    }


    /**
     * 根据公司id查询所在公司以及旗下公司对应的学校供应商
     *
     * @param providerVo
     * @return
     * @
     */
    private List<Long> queryByCompanyId(InstitutionProviderQueryDto providerVo) {
        //校验 防止篡改公司id数据，改到了上级公司 --> 校验传入的公司id是否等于当前登陆人的公司id
        if (!SecureUtil.validateCompany(providerVo.getFkCompanyId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
        }
        List<Long> queryCompany = new ArrayList<>();
        queryCompany.add(providerVo.getFkCompanyId());

        List<Long> providerIds = providerCompanyService.getRelationByCompanyId(queryCompany);
        if (GeneralTool.isEmpty(providerIds)) {
            providerIds = new ArrayList<>();
            providerIds.add(0L);
        }
        return providerIds;
    }

    private Map<Long, String> getCompanyMap() {
        Result<List<CompanyTreeVo>> result = permissionCenterClient.getAllCompanyDto();
        if (!result.isSuccess()) {
            throw new GetServiceException(result.getMessage());
        }
        List<CompanyTreeVo> companyTreeVos = result.getData();
        //初始为5的map
        Map<Long, String> companyMap = new HashMap<>(5);
        if (GeneralTool.isNotEmpty(companyTreeVos)) {
            companyMap = companyTreeVos.stream().collect(Collectors.toMap(CompanyTreeVo::getId, CompanyTreeVo::getShortName));
        }
        return companyMap;
    }

    private String sub(StringBuilder sb) {
        if (GeneralTool.isEmpty(sb)) {
            return null;
        }
        String substring = null;
        int i = sb.lastIndexOf("，");
        if (i != -1) {
            substring = sb.substring(0, i);
        }
        return substring;
    }

    private boolean validateAdd(InstitutionProviderDto providerVo) {
        LambdaQueryWrapper<InstitutionProvider> wrapper = new LambdaQueryWrapper();
        wrapper.eq(InstitutionProvider::getName, providerVo.getName());
        List<InstitutionProvider> list = this.providerMapper.selectList(wrapper);
        return GeneralTool.isEmpty(list);
    }

    private boolean validateUpdate(InstitutionProviderDto providerVo) {
        LambdaQueryWrapper<InstitutionProvider> wrapper = new LambdaQueryWrapper();
        wrapper.eq(InstitutionProvider::getName, providerVo.getName());
        List<InstitutionProvider> list = this.providerMapper.selectList(wrapper);
        return list.size() <= 0 || list.get(0).getId().equals(providerVo.getId());
    }

    @Override
    public List<InstitutionProviderInstitutionChannelVo> getInstitutionProviderByName(InstitutionChannelCompanyQueryDto queryVo) {
        //登录用户所拥有权限id
        List<Long> ids = SecureUtil.getCompanyIds();
        if (GeneralTool.isEmpty(ids)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
        }
        queryVo.setFkCompanyIds(ids);
        List<InstitutionProviderInstitutionChannelVo> institutionProviderList = this.providerMapper.getInstitutionProviderByName(queryVo);
        return institutionProviderList;
    }

    @Override
    public void updateBindingActive(Long institutionId, Long institutionProviderId, Integer isBindingActive) {
        //.last("LIMIT 1")
        InstitutionProviderInstitution institutionProviderInstitution = institutionProviderInstitutionService.getOne(Wrappers.<InstitutionProviderInstitution>lambdaQuery()
                .eq(InstitutionProviderInstitution::getFkInstitutionId, institutionId)
                .eq(InstitutionProviderInstitution::getFkInstitutionProviderId, institutionProviderId).last("LIMIT 1"));

        if (isBindingActive.equals(1)) {
            institutionProviderInstitution.setIsActive(true);
            institutionProviderInstitution.setActiveDate(new Date());
        } else {
            institutionProviderInstitution.setIsActive(false);
            institutionProviderInstitution.setUnactiveDate(new Date());
        }
        utilService.setUpdateInfo(institutionProviderInstitution);
        boolean b = institutionProviderInstitutionService.updateById(institutionProviderInstitution);
        if (!b) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }

        if (!isBindingActive.equals(1)) {
            //调用PMP接口
            UnbindProviderInstitutionDto dto = new UnbindProviderInstitutionDto();
            dto.setProviderId(institutionProviderId);
            dto.setInstitutionIds(Arrays.asList(institutionId));
            pmpCenterClient.unbindProviderInstitution(dto);
        }

    }

    @Override
    public List<InstitutionProviderVo> getEventRegistrationProviderByIds(List<Long> providerIds) {
        if (GeneralTool.isEmpty(providerIds)) {
            return new ArrayList<>();
        }
        List<InstitutionProvider> institutionProviders = providerMapper.selectBatchIds(providerIds);
        List<InstitutionProviderVo> institutionProviderVos = BeanCopyUtils.copyListProperties(institutionProviders, InstitutionProviderVo::new);
        doSetEventRegistrationProvidersName(institutionProviderVos, providerIds);
        return institutionProviderVos;
    }

    @Override
    public List<InstitutionProviderVo> getEventRegistrationProviders(InstitutionProviderDto institutionProviderDto, Page page) {
        List<Long> ids = institutionProviderDto.getProviderIds();
        //获取分页数据
        LambdaQueryWrapper<InstitutionProvider> wrapper1 = new LambdaQueryWrapper();
        if (GeneralTool.isNotEmpty(ids)) {
            wrapper1.notIn(InstitutionProvider::getId, ids);
        }
        IPage<InstitutionProvider> pages = this.page(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), wrapper1);
        List<InstitutionProviderVo> institutionProviderVos = providerMapper.getInstitutionProviders(pages, institutionProviderDto);
        page.setAll((int) pages.getTotal());
        for (InstitutionProviderVo institutionProviderVo : institutionProviderVos) {
            if (GeneralTool.isNotEmpty(institutionProviderVo.getFkInstitutionProviderTypeId())) {
                institutionProviderVo.setTypeName(institutionProviderTypeMapper.getNameById(institutionProviderVo.getFkInstitutionProviderTypeId()));
            }
            if (GeneralTool.isNotEmpty(institutionProviderVo.getFkInstitutionGroupId())) {
                institutionProviderVo.setFkInstitutionGroupName(institutionGroupMapper.getNameById(institutionProviderVo.getFkInstitutionGroupId()));
            }
            StringJoiner stringJoiner = new StringJoiner(" ");
            if (GeneralTool.isNotEmpty(institutionProviderVo.getPublicLevel())) {
                List<String> result = Arrays.asList(institutionProviderVo.getPublicLevel().split(","));
                for (String key : result) {
                    stringJoiner.add(LocaleMessageUtils.getMessage(ProjectExtraEnum.getEnum(Integer.valueOf(key), ProjectExtraEnum.PUBLIC_OBJECTS).name()));
                }
                institutionProviderVo.setPublicLevelName(stringJoiner.toString());
            }

//            if(GeneralTool.isNotEmpty(ids)){
//                for (Long id : ids) {
//                    if (id.equals(institutionProviderVo.getId())){
//                        institutionProviderVo.setIsSelect(true);
//                    }
//                }
//            }
        }
        List<Long> resultIds = institutionProviderVos.stream().map(InstitutionProviderVo::getId).collect(Collectors.toList());

        doSetEventRegistrationProvidersName(institutionProviderVos, resultIds);

        return institutionProviderVos;
    }

    @Override
    public List<InstitutionProviderInstitutionChannelVo> getInstitutionProviderByProviderName(InstitutionChannelCompanySearchDto queryVo) {
        if (!SecureUtil.validateCompany(queryVo.getFkCompanyIds().get(0))) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
        }
        List<InstitutionProviderInstitutionChannelVo> institutionProviderList = this.providerMapper.getInstitutionProviderByProviderName(queryVo);
        return institutionProviderList;
    }

    /**
     * Author Cream
     * Description : //获取公司下的提供商列表
     * Date 2023/2/17 17:44
     * Params:
     * Return
     */
    @Override
    public List<BaseSelectEntity> getInstitutionSelectByCompanyId(Long companyId) {
        if (Objects.isNull(companyId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        return providerMapper.getInstitutionSelectByCompanyId(companyId);
    }

    @Override
    public List<BaseSelectEntity> getInstitutionProviderByTargetName(String targetName) {
        return providerMapper.getInstitutionProviderByTargetName(DataConverter.stringManipulation(targetName));
    }

    /**
     * Author Cream
     * Description : //获取提供商和渠道拼接的名称
     * Date 2023/6/8 11:35
     * Params:
     * Return
     */
    @Override
    public Map<Long, String> getInstitutionChannelProviderNamesByIds(Set<Long> providerIds) {
        if (providerIds.isEmpty()) {
            return Collections.emptyMap();
        }
        List<BaseSelectEntity> selectEntities = providerMapper.getInstitutionChannelProviderNamesByIds(providerIds);
        return selectEntities.stream().collect(Collectors.toMap(BaseSelectEntity::getId, BaseSelectEntity::getFullName));
    }

    @Override
    public InstitutionProviderVo getInstitutionProviderById(Long fkInstitutionProviderId) {
        InstitutionProvider institutionProvider = providerMapper.selectById(fkInstitutionProviderId);
        if (GeneralTool.isEmpty(institutionProvider)) {
            return null;
        } else {
            return BeanCopyUtils.objClone(institutionProvider, InstitutionProviderVo::new);
        }
    }

    /**
     * 提供商-学校安全配置
     *
     * @Date 12:25 2023/12/4
     * <AUTHOR>
     */
    @Override
    public void updateProviderInstitutionCompanyRelation(ProviderInstitutionCompanyUpdateDto providerInstitutionCompanyUpdateDto) {
        String companyIds = providerInstitutionCompanyUpdateDto.getCompanyIds().stream().map(String::valueOf).collect(Collectors.joining(","));
        //登录用户所拥有权限id
        List<Long> ids = SecureUtil.getCompanyIds();
        //所选学校提供商
        List<InstitutionProviderInstitution> list = institutionProviderInstitutionService.lambdaQuery().in(InstitutionProviderInstitution::getId, providerInstitutionCompanyUpdateDto.getFkInstitutionProviderIds()).list();
        //逐个根据权限处理公司名称
        for (InstitutionProviderInstitution i : list) {

            String fkCompanyIds = i.getFkCompanyIds();
            List<Long> CompanyList = Arrays.stream(fkCompanyIds.split(","))
                    .map(Long::parseLong)
                    .collect(Collectors.toList());
            List<Long> exclusiveToList2 = CompanyList.stream()
                    .filter(element -> !ids.contains(element))
                    .collect(Collectors.toList());
            if (!exclusiveToList2.isEmpty()) {
                String companyIds1 = exclusiveToList2.stream().map(String::valueOf).collect(Collectors.joining(","));
                String companyIds2 = companyIds1 + "," + companyIds;
                InstitutionProviderInstitution institutionProviderInstitution = new InstitutionProviderInstitution();
                institutionProviderInstitution.setFkCompanyIds(companyIds2);
                utilService.setUpdateInfo(institutionProviderInstitution);
                institutionProviderInstitutionMapper.update(institutionProviderInstitution, Wrappers.<InstitutionProviderInstitution>lambdaQuery()
                        .in(InstitutionProviderInstitution::getId, providerInstitutionCompanyUpdateDto.getFkInstitutionProviderIds()));
            } else {
                InstitutionProviderInstitution institutionProviderInstitution = new InstitutionProviderInstitution();
                institutionProviderInstitution.setFkCompanyIds(companyIds);
                institutionProviderInstitutionMapper.update(institutionProviderInstitution, Wrappers.<InstitutionProviderInstitution>lambdaQuery()
                        .in(InstitutionProviderInstitution::getId, providerInstitutionCompanyUpdateDto.getFkInstitutionProviderIds()));
            }
        }
    }

    /**
     * 根据提供商ids 获取提供商对象Map
     *
     * @Date 18:02 2023/12/18
     * <AUTHOR>
     */
    @Override
    public Map<Long, InstitutionProviderVo> getInstitutionProviderMapByIds(Set<Long> institutionProviderIds) {
        Map<Long, InstitutionProviderVo> map = new HashMap<>();
        if (GeneralTool.isEmpty(institutionProviderIds)) {
            return map;
        }
        List<InstitutionProvider> institutionProviders = this.providerMapper.selectList(Wrappers.<InstitutionProvider>query().lambda().in(InstitutionProvider::getId, institutionProviderIds));
        if (GeneralTool.isEmpty(institutionProviders)) {
            return map;
        }
        return institutionProviders.stream().collect(Collectors.toMap(InstitutionProvider::getId, institutionProvider -> BeanCopyUtils.objClone(institutionProvider, InstitutionProviderVo::new)));

    }

    @Override
    public List<InstitutionProviderInstitutionVo> getInstitutionByProvider(Set<Long> fkCompanyIds, Set<Long> fkInstitutionProviderIds) {
        List<InstitutionProviderInstitution> institutionByProvider = providerMapper.getInstitutionByProvider(fkCompanyIds, fkInstitutionProviderIds);
        if (GeneralTool.isEmpty(institutionByProvider)) {
            return Collections.emptyList();
        }
        return BeanCopyUtils.copyListProperties(institutionByProvider, InstitutionProviderInstitutionVo::new);
    }

    private void doSetEventRegistrationProvidersName(List<InstitutionProviderVo> institutionProviderVos, List<Long> providerIds) {
        if (GeneralTool.isEmpty(institutionProviderVos)) {
            return;
        }
        List<InstitutionProviderCompany> institutionProviderCompanies = providerCompanyService.list(Wrappers.<InstitutionProviderCompany>lambdaQuery()
                .in(InstitutionProviderCompany::getFkInstitutionProviderId, providerIds));


        Set<Long> companyIds = institutionProviderCompanies.stream().map(InstitutionProviderCompany::getFkCompanyId).filter(Objects::nonNull).collect(Collectors.toSet());
        Map<Long, String> companyNameMap = permissionCenterClient.getCompanyNamesByIds(companyIds).getData();

        Map<Long, List<InstitutionProviderCompany>> providerCompanyMap = null;
        if (GeneralTool.isNotEmpty(institutionProviderCompanies)) {
            providerCompanyMap = institutionProviderCompanies.stream().collect(Collectors.groupingBy(InstitutionProviderCompany::getFkInstitutionProviderId));
        }

        List<InstitutionProviderType> institutionProviderTypes = institutionProviderTypeMapper.selectList(null);
        Map<Long, String> typeNameMap = institutionProviderTypes.stream().collect(Collectors.toMap(InstitutionProviderType::getId, InstitutionProviderType::getTypeName));


        List<InstitutionProviderAreaCountry> institutionProviderAreaCountries = institutionProviderAreaCountryService.list(Wrappers.<InstitutionProviderAreaCountry>lambdaQuery()
                .in(InstitutionProviderAreaCountry::getFkInstitutionProviderId, providerIds));


        Set<Long> countryIds = institutionProviderAreaCountries.stream().map(InstitutionProviderAreaCountry::getFkAreaCountryId).filter(Objects::nonNull).collect(Collectors.toSet());
        Map<Long, String> countryFullNamesByIds = areaCountryService.getCountryFullNamesByIds(countryIds);
        Map<Long, List<InstitutionProviderAreaCountry>> providerCountryMap = null;
        if (GeneralTool.isNotEmpty(institutionProviderAreaCountries)) {
            providerCountryMap = institutionProviderAreaCountries.stream().collect(Collectors.groupingBy(InstitutionProviderAreaCountry::getFkInstitutionProviderId));
        }


        for (InstitutionProviderVo institutionProviderVo : institutionProviderVos) {
            /*公司名*/
            if (GeneralTool.isNotEmpty(providerCompanyMap) && GeneralTool.isNotEmpty(providerCompanyMap.get(institutionProviderVo.getId()))) {
                List<InstitutionProviderCompany> institutionProviderCompanieList = providerCompanyMap.get(institutionProviderVo.getId());
                institutionProviderCompanieList = institutionProviderCompanieList.stream().sorted(Comparator.comparing(InstitutionProviderCompany::getFkCompanyId)).collect(Collectors.toList());
                StringJoiner companyNames = new StringJoiner("，");
                for (InstitutionProviderCompany institutionProviderCompany : institutionProviderCompanieList) {
                    companyNames.add(companyNameMap.get(institutionProviderCompany.getFkCompanyId()));
                }
                institutionProviderVo.setCompanyName(companyNames.toString());
            }
            /*公司名*/

            /*类型名称*/
            if (GeneralTool.isNotEmpty(typeNameMap) && GeneralTool.isNotEmpty(typeNameMap.get(institutionProviderVo.getFkInstitutionProviderTypeId()))) {
                institutionProviderVo.setTypeName(typeNameMap.get(institutionProviderVo.getFkInstitutionProviderTypeId()));
            }
            /*类型名称*/

            /*业务国家名*/
            if (GeneralTool.isNotEmpty(providerCountryMap) && GeneralTool.isNotEmpty(providerCountryMap.get(institutionProviderVo.getId()))) {
                List<InstitutionProviderAreaCountry> institutionProviderAreaCountryList = providerCountryMap.get(institutionProviderVo.getId());
                institutionProviderAreaCountryList = institutionProviderAreaCountryList.stream().sorted(Comparator.comparing(InstitutionProviderAreaCountry::getFkAreaCountryId)).collect(Collectors.toList());
                StringJoiner countryNames = new StringJoiner("，");
                for (InstitutionProviderAreaCountry institutionProviderAreaCountry : institutionProviderAreaCountryList) {
                    countryNames.add(countryFullNamesByIds.get(institutionProviderAreaCountry.getFkAreaCountryId()));
                }
                institutionProviderVo.setAreaCountryNames(countryNames.toString());
            }
            /*业务国家名*/
        }
    }


    @Override
    public List<InstitutionVo> getMpsInstitutionProviderList(Long fkProviderId) {
        InstitutionDto institutionDto = new InstitutionDto();
        institutionDto.setFkProviderId(fkProviderId);
        return institutionMapper.getInstitutions(institutionDto, SecureUtil.getCompanyIds());
    }

    //TODO 禅道2109  8/11注释 lucky
//    @Override
//    public ResponseBo batchUpdateProvidersToRenewing(Set<Long> institutionProviderIds) {
//        if (GeneralTool.isEmpty(institutionProviderIds)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
//        }
//        List<InstitutionProvider> institutionProviders = institutionProviderMapper.selectList(Wrappers.<InstitutionProvider>lambdaQuery()
//                .in(InstitutionProvider::getId, institutionProviderIds)
//                .eq(InstitutionProvider::getContractStatus, 1));
//        if (GeneralTool.isEmpty(institutionProviders)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
////            return SaveResponseBo.error(INVALID_PARAM.getCode(),INVALID_PARAM.getMessage());
//        }
//        institutionProviders.forEach((institutionProvider) -> {
//            institutionProvider.setContractStatus(2);
//            utilService.setUpdateInfo(institutionProvider);
//        });
//        //批量更新 每次更新DEFAULT_BATCH_SIZE = 1000
//        boolean b = updateBatchById(institutionProviders, DEFAULT_BATCH_SIZE);
//        if (!b) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
//        }
//        return SaveResponseBo.ok();
//    }

    @Override
    public List<InstitutionProvidersAndAreaCountryVo> getInstitutionProvidersAndAreaCountryById(Long id) {
        return institutionProviderMapper.getInstitutionProvidersAndAreaCountryById(id);
    }

    @Override
    public void exportInstitution(InstitutionDto institutionDto,Page page, HttpServletResponse response) {
        List<InstitutionVo> institution  = getInstitution(institutionDto, null);
        if (GeneralTool.isEmpty(institution)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        List<ExportInstitutionVo> exportInstitutionVos =institution.stream().map(institutionVo -> {
            ExportInstitutionVo exportInstitutionVo = BeanCopyUtils.objClone(institutionVo, ExportInstitutionVo::new);
            if (GeneralTool.isNotEmpty(institutionVo.getIsBindingActive())){
                exportInstitutionVo.setIsBindingActive(institutionVo.getIsBindingActive() ? "绑定" : "解绑");
            }
            return exportInstitutionVo;
        }).collect(Collectors.toList());
        FileUtils.exportExcelNotWrapText(response, exportInstitutionVos, "institutionInfo", ExportInstitutionVo.class);

    }

    @Override
    public InstitutionProviderContractReminderVo getContractExpiredByProviderId(Long contractId) {
        return institutionProviderMapper.getContractExpiredByProviderId(contractId);
    }

    @Override
    public Boolean checkAreaCountryId(InstitutionProviderDto providerVo) {
        if (GeneralTool.isEmpty(providerVo.getAreaCountryIds())){
            throw new GetServiceException(LocaleMessageUtils.getMessage("institution_provider_do_not_hava_area_country"));
        }
        InstitutionDto institutionDto = new InstitutionDto();
        institutionDto.setFkProviderId(providerVo.getId());
        institutionDto.setIsBindingActive(true);
        List<InstitutionVo> institutions = institutionMapper.getInstitutions(null, institutionDto, SecureUtil.getCompanyIds());
        Set<Long> areaCountryIds = new HashSet<>();
        if (GeneralTool.isNotEmpty(institutions)){
            areaCountryIds = institutions.stream().map(InstitutionVo::getFkAreaCountryId).collect(Collectors.toSet());
        }else {
            return true;
        }
        //校验不在列表中的id
        Set<Long> declaredAreaSet = new HashSet<>(providerVo.getAreaCountryIds());
        Set<Long> invalidIds = areaCountryIds.stream().filter(id -> !declaredAreaSet.contains(id)).collect(Collectors.toSet());

        if (GeneralTool.isNotEmpty(invalidIds)){
            ArrayList<String> areaCountryNames = new ArrayList<>();
            for (Long invalidId : invalidIds) {
                areaCountryNames.add(areaCountryMapper.getCountryNameById(invalidId));
            }
            throw new GetServiceException(LocaleMessageUtils.getMessage("the_area_country_of_the_school_provider_cannot_be_modified") + " "+areaCountryNames);

        }
        return true;

    }

    @Override
    public List<String> getContractApplyCountryByContractId(Long id) {
        List<String> countryNames = institutionProviderMapper.getContractApplyCountryByContractId(id);
        return countryNames;
    }

}
