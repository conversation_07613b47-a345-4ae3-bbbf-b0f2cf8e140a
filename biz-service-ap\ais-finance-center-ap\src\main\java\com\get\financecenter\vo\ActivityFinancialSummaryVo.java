package com.get.financecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;

@Data
public class ActivityFinancialSummaryVo {

    @ApiModelProperty(value = "活动总费用")
    private BigDecimal totalEventCost;

    @ApiModelProperty(value = "已支出费用")
    private BigDecimal claimedEventCost;

    @ApiModelProperty(value = "剩余可用费用")
    private BigDecimal availableEventCost;

    @ApiModelProperty(value = "活动费用小计")
    private BigDecimal eventCostSubtotal;




    @ApiModelProperty(value = "待报销单总费用")
    private BigDecimal totalClaimAmount;

    @ApiModelProperty(value = "当前报销费用")
    private BigDecimal availableClaimAmount;

    @ApiModelProperty(value = "已超出费用")
    private BigDecimal exceedingTheCostAmount;

    // 小计
    @ApiModelProperty(value = "报销明细小计")
    private BigDecimal claimFormSubtotal;

    @ApiModelProperty(value = "币种编号")
    private String fkCurrencyTypeNum;
    /**
     * 币种名称
     */
    @ApiModelProperty(value = "币种名称")
    private String currencyTypeName;
}