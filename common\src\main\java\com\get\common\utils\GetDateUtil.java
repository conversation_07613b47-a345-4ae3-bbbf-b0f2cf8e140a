package com.get.common.utils;

import org.springframework.context.i18n.LocaleContextHolder;

import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.MissingResourceException;
import java.util.ResourceBundle;

/**
 * <p><b>Java日期类型相关操作类</b></p>
 * <p>该类负责对日期格式化转换、日期比较、日期加减、润年判断、获取相关的日期信息等。</p>
 *
 * <AUTHOR>
 * @version 0.0.1
 */
public class GetDateUtil {

    public static final String BUNDLE_KEY = "ApplicationResources";
    public static final long ONE_MINUTE_MILLISECOND = 60 * 1000L;
    public static final long ONE_HOUR_MILLISECOND = 60 * ONE_MINUTE_MILLISECOND;
    /**
     * 一天所对应的毫秒数
     */
    public static final long ONE_DAY_MILLISECOND = 24 * ONE_HOUR_MILLISECOND;
    /**
     * 一周所对应的毫秒数
     */
    public static final long ONE_WEEK_MILLISECOND = 7 * ONE_DAY_MILLISECOND;
    public static final long ONE_MONTH_MILLISECOND = 30 * ONE_DAY_MILLISECOND;
    public static final long ONE_YEAR_MILLISECOND = 365 * ONE_DAY_MILLISECOND;
    private static String defaultDatePattern = null;

    /**
     * 得到系统当前时间的Timestamp对象
     *
     * @return 系统当前时间的Timestamp对象
     */
    public static Timestamp getCurrTimestamp() {
        return new Timestamp(System.currentTimeMillis());
    }

    /**
     * 获取当前unix时间的秒数。
     *
     * @return
     */
    public static long getCurrentUnixTimeSecond() {
        return getCurrTimestamp().getTime() / 1000;
    }

    /**
     * 从配置文件中返回配置项"date.format",默认的日期格式符 (yyyy-MM-dd)，
     *
     * @return a string representing the date pattern on the UI
     */
    public static synchronized String getDatePattern() {
        Locale locale = LocaleContextHolder.getLocale();
        try {
            defaultDatePattern = ResourceBundle.getBundle(BUNDLE_KEY, locale).getString("date.format");
        } catch (MissingResourceException mse) {
            defaultDatePattern = "yyyy-MM-dd";
        }
        return defaultDatePattern;
    }

    /**
     * 获取日期的年份
     *
     * @param date
     * @return 日期的年份
     */
    public static int getYear(Date date) {
        return getCalendar(date).get(Calendar.YEAR);
    }

    /**
     * 获取月最大日期
     * @param date
     * @return
     */
    public static int getActualMaximum(Date date){
        return getCalendar(date).getActualMaximum(Calendar.DATE);
    }

    /**
     * 获取日期的月份（0-11）
     *
     * @param date
     * @return 日期的月份（0-11）
     */
    public static int getMonth(Date date) {
        return getCalendar(date).get(Calendar.MONTH);
    }

    /**
     * 获取当前日期是第几周
     *
     * @param date
     * @return
     */
    public static int getWeekOfMonth(Date date) {
        return getCalendar(date).get(Calendar.WEEK_OF_MONTH);
    }

    /**
     * 获取日期的一个月中的某天
     *
     * @param date
     * @return 日期的一个月中的某天(1 - 31)
     */
    public static int getDay(Date date) {
        return getCalendar(date).get(Calendar.DATE);
    }

    /**
     * 获取日期的一个星期中的某天
     *
     * @param date
     * @return 日期的星期中日期(1 : sunday - 7 : SATURDAY)
     */
    public static int getWeek(Date date) {
        return getCalendar(date).get(Calendar.DAY_OF_WEEK);
    }

    /**
     * 获取日期的一天中的某个小时
     *
     * @param date
     * @return 日期的一个天中的某个小时(0 - 23)
     */
    public static int getHour(Date date) {
        return getCalendar(date).get(Calendar.HOUR_OF_DAY);
    }

    /**
     * 获取日期的一个小时中的某个分钟
     *
     * @param date
     * @return 日期的一个小时中的某个分钟(0 - 60)
     */
    public static int getMinutes(Date date) {
        return getCalendar(date).get(Calendar.MINUTE);
    }

    /**
     * 获取日期的当月1号所对应的星期中的某天
     *
     * @param date
     * @return 日期的星期中日期(1 : sunday - 7 : SATURDAY)
     */
    public static int getWeekOfFirstDayOfMonth(Date date) {
        return getWeek(getFirstDayOfMonth(date));
    }

    /**
     * 获取日期的当月最后1天所对应的星期中的某天
     *
     * @param date
     * @return 日期的星期中日期(1 : sunday - 7 : SATURDAY)
     */
    public static int getWeekOfLastDayOfMonth(Date date) {
        return getWeek(getLastDayOfMonth(date));
    }

    /**
     * 将日期字符串按指定的格式转为Date类型
     *
     * @param strDate 待解析的日期字符串
     * @param format  日期格式
     * @return 字符串对应的日期对象
     * @see {@link #parseDate(String, String, boolean)}
     */
    public static final Date parseDate(String strDate, String format) {
        return parseDate(strDate, format, true);
    }

    /**
     * 将日期字符串按指定的格式转为Date类型
     *
     * @param strDate   待解析的日期字符串
     * @param format    日期格式
     * @param isLenient false时表示为严格的日期格式，如2013年1月33日将会抛出错误；true时表示为宽松的日期格式，
     *                  如2013年1月33日实际会转换成2013年1月1日之后的32天，即2013年2月2日
     * @return 字符串对应的日期对象
     * @see {@link DateFormat#setLenient(boolean)}
     */
    public static final Date parseDate(String strDate, String format, boolean isLenient) {
        SimpleDateFormat df = new SimpleDateFormat(format);
        df.setLenient(isLenient);
        try {
            return df.parse(strDate);
        } catch (ParseException pe) {
            return null;
        }
    }

    /**
     * 当前日期自然周计算
     *
     * @param date
     * @param week 小于0表示往前退的周数，大于零表示往后退的周数
     * @return
     */
    public static final Date afterWeek(Date date, int week) {
        Calendar calendar = getCalendar(date);
        calendar.setWeekDate(calendar.get(Calendar.YEAR), calendar.get(Calendar.WEEK_OF_YEAR) + week, calendar.get(Calendar.DAY_OF_WEEK));
        return calendar.getTime();
    }

    /**
     * 当前日期自然月计算
     *
     * @param date
     * @param month 小于0表示往前退的月数，大于零表示往后退的月数
     * @return
     */
    public static final Date afterMonth(Date date, int month) {
        Calendar calendar = getCalendar(date);
        calendar.set(calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH) + month, calendar.get(Calendar.DAY_OF_MONTH));
        return calendar.getTime();
    }

    /**
     * 将日期字符串按系统配置中指定默认格式(getDatePattern()返回的格式)转为Date类型
     *
     * @param strDate 待解析的日期字符串
     * @return 字符串对应的日期对象
     */
    public static Date parseDate(String strDate) {
        return parseDate(strDate, getDatePattern());
    }


    /**
     * 检查所给的年份是否是闰年
     *
     * @param year 年
     * @return 检查结果: true - 是闰年; false - 是平年
     */
    public static boolean isLeapYear(int year) {
        if (year / 4 * 4 != year) {
            //不能被4整除
            return false;
        } else if (year / 100 * 100 != year) {
            //能被4整除，不能被100整除
            return true;
        } else if (year / 400 * 400 != year) {
            //能被100整除，不能被400整除
            return false;
        } else {
            //能被400整除
            return true;
        }
    }

    /**
     * 按照默认格式化样式格式化当前系统时间
     *
     * @return 日期字符串
     */
    public static String getCurrentTime() {
        return formatDate(new Date());
    }

    /**
     * 按照默认格式化样式格式化当前系统时间
     *
     * @param format String 日期格式化标准
     * @return String 日期字符串。
     */
    public static String getCurrentTime(String format) {
        return formatDate(new Date(), format);
    }

    /**
     * 按照指定格式化样式格式化指定的日期
     *
     * @param date   待格式化的日期
     * @param format 日期格式
     * @return 日期字符串
     */
    public static String formatDate(Date date, String format) {
        if (date == null) {
            date = new Date();
        }
        if (format == null) {
            format = getDatePattern();
        }
        SimpleDateFormat formatter = new SimpleDateFormat(format);
        return formatter.format(date);
    }

    /**
     * 按照默认格式化样式格式化指定的日期
     *
     * @param date 待格式化的日期
     * @return 日期字符串
     */
    public static String formatDate(Date date) {
        long offset = System.currentTimeMillis() - date.getTime();
        String pos = "前";
        if (offset < 0) {
            pos = "后";
            offset = -offset;
        }
        if (offset >= ONE_YEAR_MILLISECOND) {
            return formatDate(date, getDatePattern());
        }

        StringBuilder sb = new StringBuilder();
        if (offset >= 2 * ONE_MONTH_MILLISECOND) {
            return sb.append((offset + ONE_MONTH_MILLISECOND / 2) / ONE_MONTH_MILLISECOND).append("个月").append(pos).toString();
        }
        if (offset > ONE_WEEK_MILLISECOND) {
            return sb.append((offset + ONE_WEEK_MILLISECOND / 2) / ONE_WEEK_MILLISECOND).append("周").append(pos).toString();
        }
        if (offset > ONE_DAY_MILLISECOND) {
            return sb.append((offset + ONE_DAY_MILLISECOND / 2) / ONE_DAY_MILLISECOND).append("天").append(pos).toString();
        }
        if (offset > ONE_HOUR_MILLISECOND) {
            return sb.append((offset + ONE_HOUR_MILLISECOND / 2) / ONE_HOUR_MILLISECOND).append("小时").append(pos).toString();
        }
        if (offset > ONE_MINUTE_MILLISECOND) {
            return sb.append((offset + ONE_MINUTE_MILLISECOND / 2) / ONE_MINUTE_MILLISECOND).append("分钟").append(pos).toString();
        }
        return sb.append(offset / 1000).append("秒").append(pos).toString();
    }

    /**
     * 将date的时间部分清零
     *
     * @param day
     * @return 返回Day将时间部分清零后对应日期
     */
    public static Date getCleanDay(Date day) {
        return getCleanDay(getCalendar(day));
    }

    /**
     * 获取day对应的Calendar对象
     *
     * @param day
     * @return 返回date对应的Calendar对象
     */
    public static Calendar getCalendar(Date day) {
        Calendar c = Calendar.getInstance();
        c.clear();
        if (day != null) {
            c.setTime(day);
        }
        return c;
    }

    /**
     * 根据year，month，day构造日期对象
     *
     * @param year  年份（4位长格式）
     * @param month 月份（1-12)
     * @param day   天（1-31）
     * @return 日期对象
     */
    public static Date makeDate(int year, int month, int day) {
        Calendar c = Calendar.getInstance();
        getCleanDay(c);
        c.set(Calendar.YEAR, year);
        c.set(Calendar.MONTH, month - 1);
        c.set(Calendar.DAY_OF_MONTH, day);
        return c.getTime();
    }

    /**
     * 根据year，month，day构造日期对象
     *
     * @param year  年份（4位长格式）
     * @param month 月份（1-12)
     * @param day   天（1-31）
     * @return 日期对象
     */
    public static Date makeDay(Integer year, Integer month, Integer day, Integer hour, Integer minute, Integer second) {
        Calendar c = Calendar.getInstance();
        getCleanDay(c);
        c.set(Calendar.YEAR, year);
        c.set(Calendar.MONTH, month - 1);
        c.set(Calendar.DAY_OF_MONTH, day);
        c.set(Calendar.HOUR_OF_DAY, hour);
        c.set(Calendar.MINUTE, minute);
        c.set(Calendar.SECOND, second);
        return c.getTime();
    }


    /**
     * 日期date所在星期的第一天00:00:00对应日期对象
     *
     * @param date
     * @return 日期所在星期的第一天00:00:00对应日期对象
     */
    public static Date getFirstDayOfWeek(Date date) {
        return getFirstCleanDay(Calendar.DAY_OF_WEEK, date);
    }

    /**
     * 当前日期所在星期的第一天00:00:00对应日期对象
     *
     * @return 当前日期所在星期的第一天00:00:00对应日期对象
     */
    public static Date getFirstDayOfWeek() {
        return getFirstDayOfWeek(null);
    }

    /**
     * 日期date所在月份的第一天00:00:00对应日期对象
     *
     * @param date
     * @return 日期所在月份的第一天00:00:00对应日期对象
     */
    public static Date getFirstDayOfMonth(Date date) {
        return getFirstCleanDay(Calendar.DAY_OF_MONTH, date);
    }

    /**
     * 日期date所在月份的第一天的指定时间点对应的日期对象
     *
     * @param date
     * @param hour
     * @param minute
     * @param second
     * @return
     */
    public static Date getFirstDayOfMonth(Date date, int hour, int minute, int second) {
        Calendar c = Calendar.getInstance();
        if (date != null) {
            c.setTime(date);
        }
        c.set(Calendar.DAY_OF_MONTH, 1);
        return getSetTimeDay(c, hour, minute, second);
    }

    /**
     * 当前日期所在月份的第一天00:00:00对应日期对象
     *
     * @return 当前日期所在月份的第一天00:00:00对应日期对象
     */
    public static Date getFirstDayOfMonth() {
        return getFirstDayOfMonth(null);
    }

    /**
     * 当前日期所在月份的最后一天00:00:00对应日期对象
     *
     * @return 当前日期所在月份的最后一天00:00:00对应日期对象
     */
    public static Date getLastDayOfMonth() {
        return getLastDayOfMonth(null);
    }

    /**
     * 日期date所在月份的最后一天00:00:00对应日期对象
     *
     * @param date
     * @return 日期date所在月份的最后一天00:00:00对应日期对象
     */
    public static Date getLastDayOfMonth(Date date) {
        Calendar c = getCalendar(getFirstDayOfMonth(date));
        c.add(Calendar.MONTH, 1);
        c.add(Calendar.DATE, -1);
        return getCleanDay(c);
    }

    /**
     * 日期date所在月份的最后一天指定时间点对应的日期对象
     *
     * @param date
     * @param hour
     * @param minute
     * @param second
     * @return
     */
    public static Date getLastDayOfMonth(Date date, int hour, int minute, int second) {
        Calendar c = getCalendar(getFirstDayOfMonth(date));
        c.add(Calendar.MONTH, 1);
        c.add(Calendar.DATE, -1);
        return getSetTimeDay(c, hour, minute, second);
    }

    /**
     * 日期date所在季度的第一天00:00:00对应日期对象
     *
     * @param date
     * @return 日期date所在季度的第一天00:00:00对应日期对象
     */
    public static Date getFirstDayOfSeason(Date date) {
        Date d = getFirstDayOfMonth(date);
        int delta = GetDateUtil.getMonth(d) % 3;
        if (delta > 0) {
            d = GetDateUtil.getDateAfterMonths(d, -delta);
        }
        return d;
    }

    /**
     * 当前日期所在季度的第一天00:00:00对应日期对象
     *
     * @return 当前日期所在季度的第一天00:00:00对应日期对象
     */
    public static Date getFirstDayOfSeason() {
        return getFirstDayOfMonth(null);
    }

    /**
     * 日期date所在年份的第一天00:00:00对应日期对象
     *
     * @param date
     * @return 日期date所在年份的第一天00:00:00对应日期对象
     */
    public static Date getFirstDayOfYear(Date date) {
        return makeDate(getYear(date), 1, 1);
    }

    /**
     * 当前日期年度的最后一天23:59:59对应日期对象
     *
     * @param date
     * @return 当前日期年度的最后一天23:59:59对应日期对象
     */
    public static Date getLastDayOfYear(Date date) {
        return parseDate(getYear(date) + "-12-31 23:59:59", "yyyy-MM-dd HH:mm:ss");
    }

    /**
     * 当前日期年度的第一天00:00:00对应日期对象
     *
     * @return 当前日期年度第一天00:00:00对应日期对象
     */
    public static Date getFirstDayOfYear() {
        return getFirstDayOfYear(new Date());
    }

    /**
     * 当前日期年度的最后一天23:59:59对应日期对象
     *
     * @return 当前日期年度的最后一天23:59:59对应日期对象
     */
    public static Date getLastDayOfYear() {
        return parseDate(getYear(new Date()) + "-12-31 23:59:59", "yyyy-MM-dd HH:mm:ss");
    }

    /**
     * 计算N周后的日期
     *
     * @param start 开始日期
     * @param weeks 可以为负，表示前N周
     * @return 新的日期
     */
    public static Date getDateAfterWeeks(Date start, int weeks) {
        return getDateAfterMs(start, weeks * ONE_WEEK_MILLISECOND);
    }

    /**
     * 计算N月后的日期
     *
     * @param start  开始日期
     * @param months 可以为负，表示前N月
     * @return 新的日期
     */
    public static Date getDateAfterMonths(Date start, int months) {
        return add(Calendar.MONTH, months, start);
    }

    /**
     * 计算N年后的日期
     *
     * @param start 开始日期
     * @param years 可以为负，表示前N年
     * @return 新的日期
     */
    public static Date getDateAfterYears(Date start, int years) {
        return add(Calendar.YEAR, years, start);
    }

    /**
     * 计算N天后的日期（0点）
     *
     * @param start
     * @param days
     * @return
     */
    public static Date getDateAfterDaysNoTime(Date start, int days) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(getDateAfterDays(start, days));
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTime();
    }

    /**
     * 计算N天后的日期
     *
     * @param start 开始日期
     * @param days  可以为负，表示前N天
     * @return 新的日期
     */
    public static Date getDateAfterDays(Date start, int days) {
        return getDateAfterMs(start, days * ONE_DAY_MILLISECOND);
    }
    /**
     * 计算N毫秒后的日期
     *
     * @param start 开始日期
     * @param ms    可以为负，表示前N毫秒
     * @return 新的日期
     */
    public static Date getDateAfterMs(Date start, long ms) {
        return new Date(start.getTime() + ms);
    }

    /**
     * 计算2个日期之间的间隔的周期数
     *
     * @param start    开始日期
     * @param end      结束日期
     * @param msPeriod 单位周期的毫秒数
     * @return 周期数
     */
    public static long getPeriodNum(Date start, Date end, long msPeriod) {
        return getIntervalMs(start, end) / msPeriod;
    }

    /**
     * 计算2个日期之间的毫秒数
     *
     * @param start 开始日期
     * @param end   结束日期
     * @return 毫秒数
     */
    public static long getIntervalMs(Date start, Date end) {
        return end.getTime() - start.getTime();
    }

    /**
     * 计算2个日期之间的天数
     *
     * @param start 开始日期
     * @param end   结束日期
     * @return 天数
     */
    public static int getIntervalDays(Date start, Date end) {
        return (int) getPeriodNum(start, end, ONE_DAY_MILLISECOND);
    }

    /**
     * 计算2个日期之间的周数
     *
     * @param start 开始日期
     * @param end   结束日期
     * @return 周数
     */
    public static int getIntervalWeeks(Date start, Date end) {
        return (int) getPeriodNum(start, end, ONE_WEEK_MILLISECOND);
    }

    /**
     * 比较日期前后关系
     *
     * @param base 基准日期
     * @param date 待比较的日期
     * @return 如果date在base之前或相等返回true，否则返回false
     */
    public static boolean before(Date base, Date date) {
        return date.before(base) || date.equals(base);
    }

    /**
     * 比较日期前后关系
     *
     * @param base 基准日期
     * @param date 待比较的日期
     * @return 如果date在base之前返回true，否则返回false
     */
    public static boolean justBefore(Date base, Date date) {
        return date.before(base);
    }

    /**
     * 比较日期前后关系
     *
     * @param base 基准日期
     * @param date 待比较的日期
     * @return 如果date在base之后或相等返回true，否则返回false
     */
    public static boolean after(Date base, Date date) {
        return date.after(base) || date.equals(base);
    }

    /**
     * 返回对应毫秒数大的日期
     *
     * @param date1
     * @param date2
     * @return 返回对应毫秒数大的日期
     */
    public static Date max(Date date1, Date date2) {
        if (date1.getTime() > date2.getTime()) {
            return date1;
        } else {
            return date2;
        }
    }

    /**
     * 返回对应毫秒数小的日期
     *
     * @param date1
     * @param date2
     * @return 返回对应毫秒数小的日期
     */
    public static Date min(Date date1, Date date2) {
        if (date1.getTime() < date2.getTime())
            return date1;
        else {
            return date2;
        }
    }

    /**
     * 判断date是否在指定的时期范围（start~end）内
     *
     * @param start 时期开始日期
     * @param end   时期结束日期
     * @param date  待比较的日期
     * @return 如果date在指定的时期范围内，返回true，否则返回false
     */
    public static boolean inPeriod(Date start, Date end, Date date) {
        return (end.after(date) || end.equals(date)) && (start.before(date) || start.equals(date));
    }

    /**
     * 获取当前日期是星期几
     *
     * @param dt
     * @return 当前日期是星期几
     */
    public static String getWeekOfDate(Date dt) {
        String[] weekDays = {"星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"};
        Calendar cal = Calendar.getInstance();
        cal.setTime(dt);
        int w = cal.get(Calendar.DAY_OF_WEEK) - 1;
        if (w < 0) {
            w = 0;
        }
        return weekDays[w];
    }

    /**
     * 获得当天指定时间的时间戳
     *
     * @param hour
     * @param minute
     * @param second
     * @return
     */
    public static long getTodayUnixTime(int hour, int minute, int second) {
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.HOUR_OF_DAY, hour);
        cal.set(Calendar.SECOND, second);
        cal.set(Calendar.MINUTE, minute);
        cal.set(Calendar.MILLISECOND, 0);
        return (long) (cal.getTimeInMillis() / 1000);
    }

    /**
     * 获取某一天的前一天日期
     *
     * @param date
     * @return
     */
    public static Date getYesterdayDate(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.set(Calendar.DATE, c.get(Calendar.DATE) - 1);
        return c.getTime();
    }

    /**
     * 获取某一天的后一天日期
     *
     * @param date
     * @return
     */
    public static Date getTomorrowDate(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.set(Calendar.DATE, c.get(Calendar.DATE) + 1);
        return c.getTime();
    }

    /**
     * 获取今日为一年中的第几周
     *
     * @param date
     * @return
     */
    public static int getWeekOfYear(Date date) {
        return getCalendar(date).get(Calendar.WEEK_OF_YEAR);
    }

    /**
     * 获取指定某年某月的总天数
     *
     * @param year
     * @param month
     * @return
     */
    public static int getDaysByYearMonth(int year, int month) {
        Calendar a = Calendar.getInstance();
        a.set(Calendar.YEAR, year);
        a.set(Calendar.MONTH, month - 1);
        a.set(Calendar.DATE, 1);
        a.roll(Calendar.DATE, -1);
        int maxDate = a.get(Calendar.DATE);
        return maxDate;
    }

    /**
     * 获取本周的星期几为本月几号
     *
     * @param week   1:星期一       2：星期二  ....
     * @param format 日期格式       如：y年M月d日
     * @return
     * @throws ParseException
     */
    public static Date getDayOfThisWeek(int week, String format) throws ParseException {
        Calendar c = Calendar.getInstance();
        SimpleDateFormat sf = new SimpleDateFormat(format);
        int day_of_week = c.get(Calendar.DAY_OF_WEEK) - week;
        if (day_of_week == 0) {
            day_of_week = 7;
        }
        c.add(Calendar.DATE, -day_of_week + 1);

        return sf.parse(sf.format(c.getTime()));
    }

    /**
     * 获取N周以后的周M,如：
     * 1. weekNum=1, weekDayNum=5 表示获取1周以后的周5
     * 2. weekNum<0时，表示前n周
     * 3. weekDayNum 必须大于 0
     *
     * @param date
     * @param weekNum
     * @param weekDayNum
     * @return
     */
    public static Date getWeekDayAfterWeekNum(Date date, int weekNum, int weekDayNum) {
        Calendar c = getCalendar(date);
        int weekOfYear = c.get(Calendar.WEEK_OF_YEAR);
        c.set(Calendar.WEEK_OF_YEAR, weekOfYear + weekNum);
        if (weekDayNum > 0) {
            c.set(Calendar.DAY_OF_WEEK, weekDayNum);
        }
        return c.getTime();
    }

    /**
     * 获取N月以后的M号,如：
     * 1. monthNum=1, dayOfMonth=5 表示获取1个月以后的5日
     * 2. monthNum<0时，表示前n月
     * 3. dayOfMonth 必须大于 0
     *
     * @param date
     * @param monthNum
     * @param dayOfMonth
     * @return
     */
    public static Date getMonthDayAfterMonthNum(Date date, int monthNum, int dayOfMonth) {
        Calendar c = getCalendar(date);
        int month = c.get(Calendar.MONTH);
        c.set(Calendar.MONTH, month + monthNum);
        if (dayOfMonth > 0) {
            c.set(Calendar.DAY_OF_MONTH, dayOfMonth);
        }
        return c.getTime();
    }

    /**
     * 根据当前日期获取当前所在的季度
     *
     * @param date
     * @return
     */
    public static int getQuarter(Date date) {
        Calendar c = getCalendar(date);
        int month = c.get(Calendar.MONTH);
        return (month / 3) + 1;
    }

    /**
     * 获取当前季度之后的N个季度值
     * 算法：
     * 1. 获取当前季度中间月分
     * 2. 根据中间月往前或往后以3个月为步长递推为需要求的季度
     *
     * @param quarterNum quarterNum > 0表示往后递推，quarterNum < 0 表示往前递推
     * @return 年份+季度 如：20161(表示2016年第一季度)
     */
    public static int getQuarterAfterThisQuarter(Date date, int quarterNum) {
        Calendar c = getCalendar(date);
        int quartMidMonth = getMidMonthInQuarter(getQuarter(date)) - 1;

        //每个季度为3个月，以3个月为单位往前或往后递增
        c.set(Calendar.MONTH, quartMidMonth + (3 * quarterNum));
        Date targetQuartDate = c.getTime();
        return Integer.parseInt(c.get(Calendar.YEAR) + "" + getQuarter(targetQuartDate));
    }

    /**
     * 指定时间获取年月
     * @param date
     * @return年份+月份 如：202201
     */
    public static int getYearMonth(Date date){
        Calendar c = getCalendar(date);
        return Integer.parseInt(c.get(Calendar.YEAR) + "" + ((c.get(Calendar.MONTH) + 1) > 10 ? c.get(Calendar.MONTH):(0+""+c.get(Calendar.MONTH))));
    }

    /**
     * 根据季度获取当个季度中间月分，如，一季度中间月为2月
     *
     * @param quarterNum
     * @return
     */
    public static int getMidMonthInQuarter(int quarterNum) {
        return 3 * quarterNum - 1;
    }

    public static Date getFirstDayOfThisQuart(Date date) {
        Calendar c = getCalendar(date);
        int quart = getQuarter(date);
        int firstMonthIndex = 3 * quart - 3;

        c.set(Calendar.MONTH, firstMonthIndex);
        c.set(Calendar.DAY_OF_MONTH, 1);
        return c.getTime();
    }

    /**
     * 获取某季度第一天
     *
     * @param quarterNum yyyyQ
     * @return
     */
    public static Date getFirstDayOfQuarter(int quarterNum) {
        int year = quarterNum / 10;
        int quarterIndex = quarterNum % 10;
        Calendar c = Calendar.getInstance();
        c.set(Calendar.YEAR, year);
        c.set(Calendar.MONTH, 3 * quarterIndex - 3);
        c.set(Calendar.DAY_OF_MONTH, 1);
        return c.getTime();
    }

    /**
     * 获取某季度最后一天
     *
     * @param quarterNum yyyyQ
     * @return
     */
    public static Date getLatestDayOfQuarter(int quarterNum) {
        int year = quarterNum / 10;
        int quarterIndex = quarterNum % 10;
        Calendar c = Calendar.getInstance();
        c.set(Calendar.YEAR, year);
        c.set(Calendar.MONTH, 3 * quarterIndex);
        c.set(Calendar.DAY_OF_MONTH, 0);
        return c.getTime();
    }

    /**
     * 获取某年某一周的第一天
     *
     * @param year       某一年
     * @param weekOfYear 一年中第几天
     * @param weekOfDay  周几
     * @return
     */
    public static Date getDateOfWeek(int year, int weekOfYear, int weekOfDay) {
        Calendar c = Calendar.getInstance();
        c.set(Calendar.YEAR, year);
        c.set(Calendar.WEEK_OF_YEAR, weekOfYear);
        if (weekOfDay > 0) {
            c.set(Calendar.DAY_OF_WEEK, Calendar.SUNDAY);
        }
        return c.getTime();
    }

    /**
     * 获取上一个小时
     *
     * @param date
     * @return
     */
    public static Date getLastHour(Date date) {
        Calendar c = getCalendar(date);
        int hour = c.get(Calendar.HOUR_OF_DAY);
        c.set(Calendar.HOUR_OF_DAY, hour - 1);
        return c.getTime();
    }

    /**
     * 修改日期时间
     *
     * @param date
     * @param hour
     * @return
     */
    public static Date setTime(Date date, int hour, int minute, int second) {
        Calendar c = getCalendar(date);
        if (hour >= 0) {
            c.set(Calendar.HOUR_OF_DAY, hour);
        }
        if (minute >= 0) {
            c.set(Calendar.MINUTE, minute);
        }
        if (second >= 0) {
            c.set(Calendar.SECOND, second);
        }
        return c.getTime();
    }

    /**
     * 获取指定日期的周几(时间为00:00:00)
     *
     * @param date      日期
     * @param weekOfDay 周几(1.SUNDAY,2.MONDAY,...)
     * @param format    格式默认(yyyy-MM-dd)
     * @return
     */
    public static String getWeekOfDay(Date date, int weekOfDay, String format) {
        Calendar c = getCalendar(date);
        switch (weekOfDay) {
            case Calendar.SUNDAY:
                c.set(Calendar.DAY_OF_WEEK, Calendar.SUNDAY);
                break;
            case Calendar.MONDAY:
                c.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
                break;
            case Calendar.TUESDAY:
                c.set(Calendar.DAY_OF_WEEK, Calendar.TUESDAY);
                break;
            case Calendar.WEDNESDAY:
                c.set(Calendar.DAY_OF_WEEK, Calendar.WEDNESDAY);
                break;
            case Calendar.THURSDAY:
                c.set(Calendar.DAY_OF_WEEK, Calendar.THURSDAY);
                break;
            case Calendar.FRIDAY:
                c.set(Calendar.DAY_OF_WEEK, Calendar.FRIDAY);
                break;
            case Calendar.SATURDAY:
                c.set(Calendar.DAY_OF_WEEK, Calendar.SATURDAY);
                break;
            default:
                break;
        }
        c.set(Calendar.HOUR_OF_DAY, 0);
        c.set(Calendar.MINUTE, 0);
        c.set(Calendar.SECOND, 0);
        format = format == null ? getDatePattern() : format;
        SimpleDateFormat formatter = new SimpleDateFormat(format);
        return formatter.format(c.getTime());
    }

    private static Date getCleanDay(Calendar c) {
        c.set(Calendar.HOUR_OF_DAY, 0);
        c.clear(Calendar.MINUTE);
        c.clear(Calendar.SECOND);
        c.clear(Calendar.MILLISECOND);
        return c.getTime();
    }

    private static Date getSetTimeDay(Calendar c, int hour, int minute, int second) {
        c.set(Calendar.HOUR_OF_DAY, hour);
        c.set(Calendar.MINUTE, minute);
        c.set(Calendar.SECOND, second);
        c.set(Calendar.MILLISECOND, 0);
        return c.getTime();
    }

    private static Date getFirstCleanDay(int datePart, Date date) {
        Calendar c = Calendar.getInstance();
        if (date != null) {
            c.setTime(date);
        }
        c.set(datePart, 1);
        return getCleanDay(c);
    }

    private static Date add(int datePart, int detal, Date date) {
        Calendar c = Calendar.getInstance();
        if (date != null) {
            c.setTime(date);
        }
        c.add(datePart, detal);
        return c.getTime();
    }

    /**
     * 获取指定日期提前天数的日期
     *
     * @param start 开始时间
     * @param days  提前天数 负数为推迟的时间
     *              return 新的日期
     */
    public static Date getAdvanceDateByDay(Date start, long days) {
        return getDateAfterMs(start, days * ONE_DAY_MILLISECOND * (-1L));
    }

//-----------------------------------------------------------------

    /**
     * 获取指定日期提前小时的日期
     *
     * @param start 开始日期
     * @param hour  提前小时数 负数为推迟的时间
     * @return 新的日期
     */
    public static Date getAdvanceDateByHour(Date start, long hour) {
        return getDateAfterMs(start, hour * ONE_HOUR_MILLISECOND * (-1L));
    }

    /**
     * 获取指定日期提前小时的日期(支持小数)
     *
     * @param start 开始日期
     * @param hour  提前小时数 负数为推迟的时间
     * @return 新的日期
     */
    public static Date getAdvanceDateByStrHour(Date start, String hour) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(start);
        calendar.add(Calendar.MINUTE, (int) (60 * Double.parseDouble(hour)) * (-1));
        return calendar.getTime();
    }

    /**
     * 获取时间间隔内每隔指定天数的所有日期
     *
     * @param start   开始日期
     * @param end     结束日期
     * @param day     相隔天数
     * @param nowDate 当前日期
     * @return 时间间隔内符合条件的日期
     */
    public static List<Date> getDatesBetweenStartAndEndByDay(Date start, Date end, long day, Date nowDate) {
        if (!justBefore(end, start)) {
            return null;
        }
        List<Date> dateList = new ArrayList<>();
        Date tmpDate;
        long periodNum = getPeriodNum(start, end, day * ONE_DAY_MILLISECOND);
        for (long i = 0; i <= periodNum; i++) {
            tmpDate = getAdvanceDateByDay(start, day * i * (-1));
            if (inPeriod(nowDate, end, tmpDate)) {
                dateList.add(tmpDate);
                continue;
            }
            if (i > 0) {
                //除了第一个，不符合条件直接退出循环
                break;
            }
        }
        return dateList;
    }

    /**
     * 获取时间间隔内所有指定星期几的所有日期
     *
     * @param start   开始日期
     * @param end     结束日期
     * @param weekDay 星期几（例：周一：1 周二：2）
     * @param nowDate 当前日期
     * @return 时间间隔内符合条件的日期
     * @ 日期错误
     */
    public static List<Date> getDatesBetweenStartAndEndByWeek(Date start, Date end, int weekDay, Date nowDate) {
        if (!justBefore(end, start)) {
            return null;
        }
        List<Date> dateList = new ArrayList<>();
        int weekNum = 0;
        //因为查询周一的数据weekDay需要为2，所以进来默认就给当前天+1，周一的话只需要传1就行
        ++weekDay;
        boolean flag = true;
        while (flag) {
            Date tmpDate = getWeekDayAfterWeekNum(start, weekNum, weekDay);
            flag = inPeriod(nowDate, end, tmpDate);
            if (flag) {
                dateList.add(tmpDate);
            }
            weekNum++;
            if (weekNum == 1) {
                //除了第一个，不符合条件直接退出循环
                flag = true;
            }
        }
        return dateList;
    }

    /**
     * 获取时间间隔内所有指定几号的所有日期
     *
     * @param start    开始日期
     * @param end      结束日期
     * @param monthDay 几号
     * @param nowDate  当前日期
     * @return 时间间隔内符合条件的日期
     * @ 日期错误
     */
    public static List<Date> getDatesBetweenStartAndEndByMonth(Date start, Date end, int monthDay, Date nowDate) {
        if (!justBefore(end, start)) {
            return null;
        }
        List<Date> dateList = new ArrayList<>();
        int monthNum = 0;
        boolean flag = true;
        while (flag) {
            Date tmpDate = getMonthDayAfterMonthNum(start, monthNum, monthDay);
            flag = inPeriod(nowDate, end, tmpDate);
            if (flag) {
                dateList.add(tmpDate);
            }
            monthNum++;
            if (monthNum == 1) {
                //除了第一个，不符合条件直接退出循环
                flag = true;
            }
        }
        return dateList;
    }

    /**
     * 获取指定时间的23:59:59时间
     *
     * @param dayTime
     * @return
     */
    public static Date getMaxTimeByDay(Date dayTime) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(dayTime);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 59);
        Date endTime = calendar.getTime();
        return endTime;
    }

    /**
     * @return java.lang.String
     * @Description :计算耗时
     * @Param [startTime, endTime, format]
     * <AUTHOR>
     */
    public static String dateDiff(String startTime, String endTime,
                                  String format) {
        SimpleDateFormat sd = new SimpleDateFormat(format);
        long nd = 1000 * 60 * 60 * 24;// 一天的毫秒数
        long nh = 1000 * 60 * 60;// 一小时的毫秒数
        long nm = 1000 * 60;// 一分钟的毫秒数
        long ns = 1000;// 一秒钟的毫秒数
        long diff;
        long day = 0;
        long hour = 0;
        long min = 0;
        long sec = 0;
        try {
            diff = sd.parse(endTime).getTime() - sd.parse(startTime).getTime();
            day = diff / nd;// 计算差多少天
            hour = diff % nd / nh + day * 24;// 计算差多少小时
            min = diff % nd % nh / nm + day * 24 * 60;// 计算差多少分钟
            sec = diff % nd % nh % nm / ns;// 计算差多少秒
            return "时间相差：" + day + "天" + (hour - day * 24) + "小时"
                    + (min - day * 24 * 60) + "分钟" + sec + "秒。";
        } catch (ParseException e) {

            e.printStackTrace();
        }
        return null;
    }

    /**
     * 获取指定时间的00:00:00时间
     *
     * @param dayTime
     * @return
     */
    public static Date getMinTimeByDay(Date dayTime) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(dayTime);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        Date startTime = calendar.getTime();
        return startTime;
    }

    /**
     * 根据年月获取周末的日期数组
     *
     * @param year
     * @param month
     * @return
     */
    public static List<String> getWeekendInMonth(int year, int month) {
        List list = new ArrayList();
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.YEAR, year);// 不设置的话默认为当年
        calendar.set(Calendar.MONTH, month - 1);// 设置月份
        calendar.set(Calendar.DAY_OF_MONTH, 1);// 设置为当月第一天
        int daySize = calendar.getActualMaximum(Calendar.DATE);// 当月最大天数
        for (int i = 0; i < daySize - 1; i++) {
            int week = calendar.get(Calendar.DAY_OF_WEEK);
            if (week == Calendar.SATURDAY || week == Calendar.SUNDAY) {// 1代表周日，7代表周六 判断这是一个星期的第几天从而判断是否是周末
                StringBuffer stringBuffer = new StringBuffer();
                stringBuffer.append(year).append("-");
                if (month < 10) {
                    stringBuffer.append(0);
                }
                stringBuffer.append(month).append("-");
                int day = calendar.get(Calendar.DAY_OF_MONTH);
                if (day < 10) {
                    stringBuffer.append(0);
                }
                stringBuffer.append(day);
                list.add(stringBuffer.toString());// 得到当天是一个月的第几天
            }
            calendar.add(Calendar.DATE, 1);//在第一天的基础上加1
        }
        return list;
    }

    /**
     * 根据范围时间获取周末的日期数组
     * @param startTime
     * @param endTime
     * @return
     */
    public static List<Date> getWeekendByRangeTime(Date startTime,Date endTime){
        List list = new ArrayList();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startTime);
        while (calendar.getTime().compareTo(endTime) <= 0 ){
            int week = calendar.get(Calendar.DAY_OF_WEEK);
            if (week == Calendar.SATURDAY || week == Calendar.SUNDAY) {// 1代表周日，7代表周六 判断这是一个星期的第几天从而判断是否是周末
                list.add(calendar.getTime());
            }
            calendar.add(Calendar.DATE, 1);//在第一天的基础上加1
        }
        return list;
    }

    /**
     * 比较两个时间点相差多少年
     *
     * @param startDate
     * @param endDate
     * @return
     */
    public static int compareYearDiff(Date startDate, Date endDate) {
        Calendar start = Calendar.getInstance();
        Calendar end = Calendar.getInstance();
        start.setTime(startDate);
        end.setTime(endDate);
        if (end.get(Calendar.YEAR) > start.get(Calendar.YEAR)) {
            int year = end.get(Calendar.YEAR) - start.get(Calendar.YEAR);
            if (end.get(Calendar.MONTH) + 1 >= start.get(Calendar.MONTH) + 1) {
                if (end.get(Calendar.DATE) >= start.get(Calendar.DATE)) {
                    return year;
                } else {
                    return year - 1;
                }
            } else {
                return year - 1;
            }

        } else {
            return 0;
        }
    }

    /**
     * 比较两个时间点相差多少天
     * @param date1
     * @param date2
     * @return
     */
    public static int compareDayDiff(Date date1,Date date2)
    {
        Calendar cal1 = Calendar.getInstance();
        cal1.setTime(date1);

        Calendar cal2 = Calendar.getInstance();
        cal2.setTime(date2);
        int day1= cal1.get(Calendar.DAY_OF_YEAR);
        int day2 = cal2.get(Calendar.DAY_OF_YEAR);

        int year1 = cal1.get(Calendar.YEAR);
        int year2 = cal2.get(Calendar.YEAR);
        if(year1 != year2) //不同年
        {
            int timeDistance = 0 ;
            for(int i = year1 ; i < year2 ; i ++)
            {
                if(i%4==0 && i%100!=0 || i%400==0) //闰年
                {
                    timeDistance += 366;
                }
                else //不是闰年
                {
                    timeDistance += 365;
                }
            }

            return timeDistance + (day2-day1) ;
        }
        else //同一年
        {
            return day2-day1;
        }
    }

    /**
     * 比较两个时间点相差多少小时
     * @DateTime 2023/1/3 16:00
     */
    public static int compareHoursDiff(Date date1,Date date2){
        long diff = date2.getTime() - date1.getTime();
        long hours = diff / 60 / 60 / 1000;
        return Integer.parseInt(String.valueOf(hours));
    }
    /**
     * 比较两个时间点相差多少分钟
     * @DateTime 2023/1/3 16:00
     */
    public static int compareMinuteDiff(Date date1,Date date2){
        long diff = date2.getTime() - date1.getTime();
        long min = diff / 60 / 1000;
        return Integer.parseInt(String.valueOf(min));
    }

    /**
     * 比较两个时间点相差多少秒
     * @DateTime 2023/1/3 16:00
     */
    public static int compareSecondDiff(Date date1,Date date2){
        long diff = date2.getTime() - date1.getTime();
        long sec = diff / 1000;
        return Integer.parseInt(String.valueOf(sec));
    }



    /**
     * 指定年份与月份获取开始时间
     *
     * @param year
     * @param month
     * @return
     */
    public static Date getBeginTime(int year, int month) {
        YearMonth yearMonth = YearMonth.of(year, month);
        LocalDate localDate = yearMonth.atDay(1);
        LocalDateTime startOfDay = localDate.atStartOfDay();
        ZonedDateTime zonedDateTime = startOfDay.atZone(ZoneId.of("Asia/Shanghai"));

        return Date.from(zonedDateTime.toInstant());
    }

    /**
     * 指定年份与月份获取结束时间
     *
     * @param year
     * @param month
     * @return
     */
    public static Date getEndTime(int year, int month) {
        YearMonth yearMonth = YearMonth.of(year, month);
        LocalDate endOfMonth = yearMonth.atEndOfMonth();
        LocalDateTime localDateTime = endOfMonth.atTime(23, 59, 59, 999);
        ZonedDateTime zonedDateTime = localDateTime.atZone(ZoneId.of("Asia/Shanghai"));
        return Date.from(zonedDateTime.toInstant());
    }

    /**
     * 获取指定年月的最后一天
     *
     * @param year
     * @param month
     * @return
     */
    public static Date getLastDayOfMonth(int year, int month) {
        Calendar cal = Calendar.getInstance();
        //设置年份
        cal.set(Calendar.YEAR, year);
        //设置月份
        cal.set(Calendar.MONTH, month - 1);
        //获取某月最大天数
        int lastDay = cal.getActualMaximum(Calendar.DATE);
        //设置日历中月份的最大天数
        cal.set(Calendar.DAY_OF_MONTH, lastDay);
        return cal.getTime();
    }

    /**
     * 根据年、月、日构造返回日期
     *
     * @param year
     * @param month
     * @param day
     * @return
     */
    public static Date getDate(int year, int month, int day) {
        Calendar cal = Calendar.getInstance();
        cal.clear();
        cal.set(Calendar.YEAR, year);
        cal.set(Calendar.MONTH, month - 1);
        cal.set(Calendar.DAY_OF_MONTH, day);
        return cal.getTime();
    }

    /**
     * 比较两个时间是否为同一天
     * @param date1
     * @param date2
     * @return
     */
    public static boolean isSameDay(Date date1, Date date2) {
        return (getYear(date1) == getYear(date2) &&
                getMonth(date1) == getMonth(date2) &&
                getDay(date1) == getDay(date2));
    }

    /**
     * 获取上N年或下N年的开始时间。当year是正数，下N年；当year是负数数，上N年；
     */
    public static Date getYearStart(int year){
        // 创建当前日期对象
        Calendar calendar = Calendar.getInstance();

        // 设置为上一年的第一天（1月1号）
        calendar.add(Calendar.YEAR, year);
        calendar.set(Calendar.MONTH, Calendar.JANUARY);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        return calendar.getTime();
    }

    /**
     * 获取上N年或下N年的结束时间。当year是正数，下N年；当year是负数数，上N年；
     */
    public static Date getYearEnd(int year){
        // 创建当前日期对象
        Calendar calendar = Calendar.getInstance();

        // 将日期设置为下一年的最后一天（12月31号）
        calendar.add(Calendar.YEAR, year);
        calendar.set(Calendar.MONTH, Calendar.UNDECIMBER);
        calendar.set(Calendar.DAY_OF_MONTH, 0);
        return calendar.getTime();
    }

    /**
     * 计算两个Date之间的所有YearMonth
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return YearMonth集合
     */
    public static List<YearMonth> getYearMonthRange(Date startDate, Date endDate) {
        List<YearMonth> yearMonths = new ArrayList<>();

        // 将Date转换为YearMonth（使用系统默认时区）
        YearMonth startYearMonth = convertToYearMonth(startDate);
        YearMonth endYearMonth = convertToYearMonth(endDate);

        // 循环添加所有年月
        YearMonth current = startYearMonth;
        while (!current.isAfter(endYearMonth)) {
            yearMonths.add(current);
            current = current.plusMonths(1);
        }

        return yearMonths;
    }

    /**
     * 将Date转换为YearMonth
     * @param date 要转换的Date对象
     * @return 对应的YearMonth
     */
    public static YearMonth convertToYearMonth(Date date) {
        // Date -> Instant -> LocalDate -> YearMonth
        return YearMonth.from(
                date.toInstant()
                        .atZone(ZoneId.systemDefault())
                        .toLocalDate()
        );
    }

    public static class Milliscond {
        private final static String format = "dd HH:mm:ss";

        // 传入起始、结束时间，默认格式
        public static String format2Str(Date start, Date end) {
            return replace(start, end, format);
        }

        // 传入毫秒数，默认格式
        public static String format2Str(long ms) {
            return replace(ms, format);
        }

        // 传入起始、结束时间，指定格式
        public static String format2Str(Date start, Date end, String format) {
            return replace(start, end, format);
        }

        // 传入毫秒数，指定格式
        public static String format2Str(long ms, String format) {
            return replace(ms, format);
        }

        // 传入起始、结束时间，获取Map
        public static Map<String, String> format2Map(Date start, Date end) {
            return format2Map(end.getTime() - start.getTime());
        }

        // 传入毫秒数，获取Map
        public static Map<String, String> format2Map(long ms) {
            long ss = 1000;
            long mi = ss * 60;
            long hh = mi * 60;
            long dd = hh * 24;

            long day = ms / dd;
            long hour = (ms - day * dd) / hh;
            long minute = (ms - day * dd - hour * hh) / mi;
            long second = (ms - day * dd - hour * hh - minute * mi) / ss;
            long milliSecond = ms - day * dd - hour * hh - minute * mi - second * ss;

            String strDay = day < 10 ? "0" + day : "" + day;
            String strHour = hour < 10 ? "0" + hour : "" + hour;
            String strMinute = minute < 10 ? "0" + minute : "" + minute;
            String strSecond = second < 10 ? "0" + second : "" + second;
            String strMilliSecond = milliSecond < 10 ? "0" + milliSecond : "" + milliSecond;

            Map<String, String> resultMap = new HashMap<String, String>();
            resultMap.put("day", strDay);
            resultMap.put("hour", strHour);
            resultMap.put("minute", strMinute);
            resultMap.put("second", strSecond);
            resultMap.put("milliSecond", strMilliSecond);
            return resultMap;
        }

        // 传入起始、结束时间，替换目标字符串
        private static String replace(Date start, Date end, String format) {
            String result = null;
            if (format.contains("dd") && format.contains("HH")
                    && format.contains("mm") && format.contains("ss")) {
                Map<String, String> map = format2Map(start, end);
                format = format.replaceAll("dd", map.get("day"));
                format = format.replaceAll("HH", map.get("hour"));
                format = format.replaceAll("mm", map.get("minute"));
                format = format.replaceAll("ss", map.get("second") + "." + map.get("milliSecond"));
                result = format;
            }
            if (format.contains("HH")
                    && format.contains("mm") && format.contains("ss")) {
                Map<String, String> map = format2Map(start, end);
                format = format.replaceAll("HH", map.get("hour"));
                format = format.replaceAll("mm", map.get("minute"));
                format = format.replaceAll("ss", map.get("second"));
                result = format;
            }
            return result;
        }

        // 传入毫秒数，替换目标字符串
        private static String replace(long ms, String format) {
            String result = null;
            if (format.contains("dd") && format.contains("HH")
                    && format.contains("mm") && format.contains("ss")) {
                Map<String, String> map = format2Map(ms);
                format = format.replaceAll("dd", map.get("day"));
                format = format.replaceAll("HH", map.get("hour"));
                format = format.replaceAll("mm", map.get("minute"));
                format = format.replaceAll("ss", map.get("second") + "." + map.get("milliSecond"));
                result = format;
            }
            return result;
        }
    }
}