package com.get.institutioncenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.FileTypeEnum;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.financecenter.feign.IFinanceCenterClient;
import com.get.institutioncenter.dao.AreaCountryMapper;
import com.get.institutioncenter.dao.AreaRegionMapper;
import com.get.institutioncenter.dto.AreaCountryDto;
import com.get.institutioncenter.dto.MediaAndAttachedDto;
import com.get.institutioncenter.dto.NewsDto;
import com.get.institutioncenter.dto.query.AreaCountryQueryDto;
import com.get.institutioncenter.dto.query.NewsQueryDto;
import com.get.institutioncenter.entity.AreaCountry;
import com.get.institutioncenter.service.IAreaCountryService;
import com.get.institutioncenter.service.IDeleteService;
import com.get.institutioncenter.service.IMediaAndAttachedService;
import com.get.institutioncenter.service.INewsService;
import com.get.institutioncenter.service.ITranslationMappingService;
import com.get.institutioncenter.vo.AreaCountryHtiVo;
import com.get.institutioncenter.vo.AreaCountryVo;
import com.get.institutioncenter.vo.AreaRegionVo;
import com.get.institutioncenter.vo.MediaAndAttachedVo;
import com.get.institutioncenter.vo.NewsVo;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.StringJoiner;
import java.util.stream.Collectors;

/**
 * @author: Sea
 * @create: 2020/7/17 11:25
 * @verison: 1.0
 * @description: 区域管理-国家配置实现类
 */
@Service
public class AreaCountryServiceImpl extends BaseServiceImpl<AreaCountryMapper, AreaCountry> implements IAreaCountryService {

    /**
     * 中国区号常量
     */
    private static final String CHINA_AREA_CODE = "86";
    @Resource
    private AreaCountryMapper areaCountryMapper;
    @Resource
    private IMediaAndAttachedService attachedService;
    @Resource
    private INewsService newsService;
    @Resource
    private UtilService utilService;
    @Resource
    private IFinanceCenterClient financeCenterClient;
    @Resource
    private IDeleteService deleteService;
    @Resource
    private ITranslationMappingService translationMappingService;
    @Resource
    private AreaRegionMapper areaRegionMapper;

    @Override
    public AreaCountryVo findAreaCountryById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        AreaCountry areaCountry = areaCountryMapper.selectById(id);
        if (GeneralTool.isEmpty(areaCountry)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        AreaCountryVo areaCountryVo = BeanCopyUtils.objClone(areaCountry, AreaCountryVo::new);

        List<AreaRegionVo> geographicalRegions = getGeographicalRegions();
        Map<Long, AreaRegionVo> geographicalRegionMap = geographicalRegions.stream().collect(Collectors.toMap(AreaRegionVo::getId, v -> v));
        if (GeneralTool.isNotEmpty(areaCountryVo.getFkAreaRegionIds())) {
            String[] fkAreaRegionIds = areaCountryVo.getFkAreaRegionIds().split(",");
            StringBuilder fkAreaRegionIdsStr = new StringBuilder();
            for (String fkAreaRegionId : fkAreaRegionIds) {
                fkAreaRegionIdsStr.append(geographicalRegionMap.get(Long.parseLong(fkAreaRegionId)).getFullName()).append(",");
            }
            areaCountryVo.setFkAreaRegionNames(fkAreaRegionIdsStr.substring(0, fkAreaRegionIdsStr.length() - 1));
        }


        areaCountryVo.setBusinessAreaName(ProjectKeyEnum.getValue(areaCountry.getBusinessAreaKey()));

        MediaAndAttachedDto attachedVo = new MediaAndAttachedDto();
        attachedVo.setFkTableName(TableEnum.INSTITUTION_COUNTRY.key);
        attachedVo.setFkTableId(id);
        List<MediaAndAttachedVo> mediaAndAttachedVos = attachedService.getMediaAndAttachedDto(attachedVo);
        //媒体附件
        if (GeneralTool.isNotEmpty(mediaAndAttachedVos)) {
            //过滤获取国旗DTO
            List<MediaAndAttachedVo> nationalFlags =
                    mediaAndAttachedVos
                            .stream()
                            .filter(mediaAndAttachedDto -> ("institution_country_flag_pic").equals(mediaAndAttachedDto.getTypeKey()))
                            .collect(Collectors.toList());
            //过滤获取国徽DTO
            List<MediaAndAttachedVo> nationalEmblems = mediaAndAttachedVos
                    .stream()
                    .filter(mediaAndAttachedDto -> ("institution_country_emblem_pic").equals(mediaAndAttachedDto.getTypeKey()))
                    .collect(Collectors.toList());

            areaCountryVo.setNationalEmblems(nationalEmblems);
            areaCountryVo.setNationalFlags(nationalFlags);
        }
        //设置新闻
        setNewsDtos(id, areaCountryVo);
        String fullName = areaCountryVo.getName();
        if (GeneralTool.isNotEmpty(areaCountryVo.getNameChn())) {
            fullName = fullName + "（" + areaCountryVo.getNameChn() + "）";
        }
        areaCountryVo.setFullName(fullName);
        //查询币种名称
        if (GeneralTool.isNotEmpty(areaCountryVo.getFkCurrencyTypeNum())) {
            Result<String> result = financeCenterClient.getCurrencyNameByNum(areaCountryVo.getFkCurrencyTypeNum());
            if (result.isSuccess()) {
                areaCountryVo.setFkCurrencyTypeName(result.getData());
            }
        }
        //语言
        areaCountryVo.setFkTableName(TableEnum.INSTITUTION_COUNTRY.key);
        //公开对象
        StringJoiner stringJoiner = new StringJoiner(" ");
        if (GeneralTool.isNotEmpty(areaCountryVo.getPublicLevel())) {
            List<String> result = Arrays.asList(areaCountryVo.getPublicLevel().split(","));
            for (String name : result) {
                stringJoiner.add(ProjectExtraEnum.getValueByKey(Integer.valueOf(name), ProjectExtraEnum.PUBLIC_OBJECTS));
            }
            areaCountryVo.setPublicLevelName(stringJoiner.toString());
        }
        return areaCountryVo;
    }

    private void setNewsDtos(Long id, AreaCountryVo areaCountryVo) {
        //查询学校下面的新闻
        NewsDto news = new NewsDto();
        news.setFkTableId(id);
        news.setFkTableName(TableEnum.INSTITUTION_COUNTRY.key);
        areaCountryVo.setNewsDtos(newsService.getNewsDtoByTarget(news));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long addAreaCountry(AreaCountryDto areaCountryDto) {
        if (GeneralTool.isEmpty(areaCountryDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        //获取最大排序值
        areaCountryDto.setViewOrder(areaCountryMapper.getMaxViewOrder());
        AreaCountry areaCountry = BeanCopyUtils.objClone(areaCountryDto, AreaCountry::new);
        String validateResult = validateAdd(areaCountryDto);
        if (GeneralTool.isEmpty(validateResult)) {
            utilService.updateUserInfoToEntity(areaCountry);
            areaCountryMapper.insert(areaCountry);
            //在国家保存前,媒体附件已经添加,返回媒体附件id,根据id去修改,国家保存后返回的id作为媒体附件中fk_table_id(这里存在国旗国徽两个文件id所以for)
            if (GeneralTool.isNotEmpty(areaCountryDto.getFkId())) {
                for (Long fkId : areaCountryDto.getFkId()) {
                    attachedService.updateTableId(fkId, areaCountry.getId());
                }
            }
        } else {
            throw new GetServiceException(validateResult);
        }
        return areaCountry.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        if (findAreaCountryById(id) == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        //删除国家数据校验
        deleteService.deleteValidateAreaCountry(id);
        areaCountryMapper.deleteById(id);

        //删除翻译内容
        translationMappingService.deleteTranslations(TableEnum.INSTITUTION_COUNTRY.key, id);
    }

    @Override
    public AreaCountryVo updateAreaCountry(AreaCountryDto areaCountryDto) {
        if (areaCountryDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        AreaCountry result = areaCountryMapper.selectById(areaCountryDto.getId());
        if (result == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        String validateResult = validateUpdate(areaCountryDto);
        if (GeneralTool.isEmpty(validateResult)) {
            AreaCountry areaCountry = BeanCopyUtils.objClone(areaCountryDto, AreaCountry::new);
            utilService.updateUserInfoToEntity(areaCountry);
            areaCountryMapper.updateById(areaCountry);
        } else {
            throw new GetServiceException(validateResult);
        }
        return findAreaCountryById(areaCountryDto.getId());
    }

    @Override
    public List<AreaCountryVo> getAreaCountrys(AreaCountryQueryDto areaCountryVo, Page page) {

//        LambdaQueryWrapper<AreaCountry> wrapper = new LambdaQueryWrapper();
        QueryWrapper<AreaCountry> wrapper = new QueryWrapper();
        if (GeneralTool.isNotEmpty(areaCountryVo)) {
            if (GeneralTool.isNotEmpty(areaCountryVo.getKeyWord())) {
                wrapper.lambda().and(wrapper_ ->
                        wrapper_.like(AreaCountry::getName, areaCountryVo.getKeyWord()).or().like(AreaCountry::getNameChn, areaCountryVo.getKeyWord()));
            }
            //过滤公开对象
            if (GeneralTool.isNotEmpty(areaCountryVo.getPublicLevel())) {
                wrapper.lambda().apply(areaCountryVo.getPublicLevel() != null, "FIND_IN_SET ('" + areaCountryVo.getPublicLevel() + "',public_level)>0");
            }
        }
        wrapper.orderByDesc("IFNULL(view_order,0)");
        wrapper.orderByAsc("CONVERT(name USING gbk)");
        //获取分页数据
        IPage<AreaCountry> pages = this.page(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), wrapper);
        List<AreaCountry> areaCountrys = pages.getRecords();
        page.setAll((int) pages.getTotal());
        List<AreaCountryVo> convertDatas = new ArrayList<>();
        for (AreaCountry areaCountry : areaCountrys) {
            AreaCountryVo areaCountryDto = BeanCopyUtils.objClone(areaCountry, AreaCountryVo::new);
            String fullName = areaCountryDto.getName();
            if (GeneralTool.isNotEmpty(areaCountryDto.getNameChn())) {
                fullName = fullName + "（" + areaCountryDto.getNameChn() + "）";
            }
            areaCountryDto.setFullName(fullName);
            //设置公开对象名称
            setPublicLevelName(areaCountryDto);
            //设置大区名称
            areaCountryDto.setBusinessAreaName(ProjectKeyEnum.getValue(areaCountry.getBusinessAreaKey()));

            convertDatas.add(areaCountryDto);
        }
        return convertDatas;
    }

    @Override
    public List<AreaCountryVo> getAreaCountrys(String keyWord) {

        LambdaQueryWrapper<AreaCountry> wrapper = new LambdaQueryWrapper();
        if (GeneralTool.isNotEmpty(keyWord)) {
            wrapper.like(AreaCountry::getName, keyWord).or();
            wrapper.like(AreaCountry::getNameChn, keyWord).or();
            wrapper.like(AreaCountry::getNum, keyWord);
        }
        wrapper.orderByDesc(AreaCountry::getViewOrder);
        List<AreaCountry> areaCountries = areaCountryMapper.selectList(wrapper);
        List<AreaCountryVo> areaCountryVos =
                areaCountries.stream().map(areaCountry -> BeanCopyUtils.objClone(areaCountry, AreaCountryVo::new)).collect(Collectors.toList());
        return areaCountryVos;
    }

    @Override
    public String getCountryNameByKey(String countryKey) {
        return areaCountryMapper.getCountryNameByKey(countryKey);
    }


    @Override
    public String getCountryNameEnByKey(String countryKey) {
        return areaCountryMapper.getCountryNameEnByKey(countryKey);
    }


    @Override
    public List<BaseSelectEntity> getAreaCountryList() {
        List<Long> countryIds = SecureUtil.getCountryIds();
        if (GeneralTool.isEmpty(countryIds)) {
            countryIds = new ArrayList<>();
            countryIds.add(0L);
        }
        return areaCountryMapper.getCountryList(countryIds);
    }

    /**
     * HTI国家下拉框
     *
     * @Date 11:18 2023/12/14
     * <AUTHOR>
     */
    @Override
    public List<AreaCountryHtiVo> getAreaCountryListHti() {
        return areaCountryMapper.getAreaCountryListHti();
    }

    @Override
    public List<BaseSelectEntity> getAreaCountryListByKeyword(String keyword) {
        List<Long> countryIds = SecureUtil.getCountryIds();
        return areaCountryMapper.getAreaCountryListByKeyWord(keyword,countryIds);
    }

    @Override
    public List<AreaCountryVo> getAllAreaCountryList() {
        return areaCountryMapper.getAllCountryList();
    }

    @Override
    public List<MediaAndAttachedVo> addInstitutionFacultyMedias(List<MediaAndAttachedDto> mediaAttachedVos) {
        List<MediaAndAttachedVo> mediaAndAttachedVos = new ArrayList<>();
        if (GeneralTool.isEmpty(mediaAttachedVos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_vo_null"));
        }
        for (MediaAndAttachedDto mediaAndAttachedDto : mediaAttachedVos) {
            //设置插入的表
            mediaAndAttachedDto.setFkTableName(TableEnum.INSTITUTION_COUNTRY.key);
            mediaAndAttachedVos.add(attachedService.addMediaAndAttached(mediaAndAttachedDto));
        }
        return mediaAndAttachedVos;
    }

    @Override
    public List<Map<String, Object>> findMediaAndAttachedType() {
        return FileTypeEnum.enumsTranslation2Arrays(FileTypeEnum.COUNTRY);
    }

    @Override
    public List<Long> getCountryIdByKey(List<String> keys) {
        if (GeneralTool.isEmpty(keys)) {
            return new ArrayList<>();
        }
        List<AreaCountry> areaCountries = this.areaCountryMapper.selectList(Wrappers.<AreaCountry>query().lambda().in(AreaCountry::getNum, keys));
        if (GeneralTool.isEmpty(areaCountries)) {
            return new ArrayList<>();
        }
        List<Long> ids = areaCountries.stream().map(AreaCountry::getId).collect(Collectors.toList());
        /*if (ids.size() == 0) {
            ids = null;
        }  */
        return ids;
    }

    @Override
    public Long addNews(NewsDto newsDto) {
        if (GeneralTool.isEmpty(newsDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        newsDto.setFkTableName(TableEnum.INSTITUTION_COUNTRY.key);
        Long newsId = newsService.addNews(newsDto);
        return newsId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void movingOrder(List<AreaCountryDto> areaCountryDtos) {
        if (GeneralTool.isEmpty(areaCountryDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_vo_null"));
        }
        AreaCountry ro = BeanCopyUtils.objClone(areaCountryDtos.get(0), AreaCountry::new);
        Integer oneorder = ro.getViewOrder();
        AreaCountry rt = BeanCopyUtils.objClone(areaCountryDtos.get(1), AreaCountry::new);
        Integer twoorder = rt.getViewOrder();
        ro.setViewOrder(twoorder);
        utilService.updateUserInfoToEntity(ro);
        rt.setViewOrder(oneorder);
        utilService.updateUserInfoToEntity(rt);
        areaCountryMapper.updateById(ro);
        areaCountryMapper.updateById(rt);
    }

    @Override
    public List<NewsVo> getNewsList(NewsQueryDto newsVo, Page page) {
        newsVo.setFkTableName(TableEnum.INSTITUTION_COUNTRY.key);
        return newsService.datas(newsVo, page);
    }

    @Override
    public String getCountryNameById(Long id) {
        return areaCountryMapper.getCountryNameById(id);
    }

    @Override
    public String getCountryKeyById(Long id) {
        AreaCountry areaCountry = this.areaCountryMapper.selectById(id);
        return areaCountry == null ? "" : areaCountry.getNum();
    }

    @Override
    public Map<Long, String> getCountryNamesByIds(Set<Long> ids) {
        //up by Jerry 2021/07/16 10:38:00
        //批量查询改成一次性查询
        Map<Long, String> map = new HashMap<>();
        if (GeneralTool.isEmpty(ids)) {
            return map;
        }
        List<AreaCountry> areaCountries = this.areaCountryMapper.selectList(Wrappers.<AreaCountry>query().lambda().in(AreaCountry::getId, ids).orderByDesc(AreaCountry::getViewOrder));
        for (AreaCountry areaCountry : areaCountries) {
            String countryName = GeneralTool.isEmpty(areaCountry.getName()) ? "" : areaCountry.getName();
            StringBuilder builder = new StringBuilder(countryName);
//            if (GeneralTool.isNotEmpty(areaCountry.getNameChn())) {
//                builder.append("（");
//                builder.append(areaCountry.getNameChn());
//                builder.append("）");
//            }
            map.put(areaCountry.getId(), builder.toString());
        }
        return map;
    }

    /**
     * Author Cream
     * Description : //获取国家的币种
     * Date 2022/8/11 12:28
     * Params:
     * Return
     */
    @Override
    public String getCurrencyNumByCountryName(String countryName) {
        if (StringUtils.isNotBlank(countryName)) {
            return areaCountryMapper.getCurrencyNumByName(countryName);
        }
        return null;
    }

    @Override
    public List<MediaAndAttachedVo> getItemMedia(MediaAndAttachedDto attachedVo, Page page) {
        if (GeneralTool.isEmpty(attachedVo.getFkTableId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        attachedVo.setFkTableName(TableEnum.INSTITUTION_COUNTRY.key);
        return attachedService.getMediaAndAttachedDto(attachedVo, page);

    }

    @Override
    public List<MediaAndAttachedVo> addItemMedia(ValidList<MediaAndAttachedDto> mediaAttachedVos) {
        if (GeneralTool.isEmpty(mediaAttachedVos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_vo_null"));
        }
        List<MediaAndAttachedVo> mediaAndAttachedVos = new ArrayList<>();
        for (MediaAndAttachedDto mediaAndAttachedDto : mediaAttachedVos) {
            //设置插入的表
            mediaAndAttachedDto.setFkTableName(TableEnum.INSTITUTION_COUNTRY.key);
            mediaAndAttachedVos.add(attachedService.addMediaAndAttached(mediaAndAttachedDto));
        }
        return mediaAndAttachedVos;
    }

    @Override
    public String getCountryChnNameById(Long id) {
        return areaCountryMapper.getCountryNameChnById(id);
    }

    @Override
    public Map<Long, String> getCountryChnNameByIds(Set<Long> ids) {
        Map<Long, String> map = new HashMap<>();
        if (GeneralTool.isEmpty(ids)) {
            return map;
        }
        List<AreaCountry> areaCountries = this.areaCountryMapper.selectList(Wrappers.<AreaCountry>query().lambda().in(AreaCountry::getId, ids));
        if (GeneralTool.isEmpty(areaCountries)) {
            return map;
        }
        for (AreaCountry areaCountry : areaCountries) {
            map.put(areaCountry.getId(), areaCountry.getNameChn());
        }
        return map;
    }

    @Override
    public Set<Long> getAllCountryId() {
        List<AreaCountry> areaCountries = areaCountryMapper.selectList(Wrappers.<AreaCountry>query().lambda());
        return areaCountries.stream().map(AreaCountry::getId).collect(Collectors.toSet());
    }

    /**
     * feign调用 查询全部 公开首页国家id
     *
     * @Date 17:00 2021/11/16
     * <AUTHOR>
     */
    @Override
    public List<Map<String, String>> selectPublicCountryHomeNums() {
        return areaCountryMapper.selectPublicCountryHomeNums(ProjectExtraEnum.PUBLIC_COUNTRY_HOME.key);
    }

    /**
     * 根据国家id获取国家名和国家编号
     *
     * @Date 12:13 2022/1/18
     * <AUTHOR>
     */
    @Override
    public String getCountryNameAndNumById(Long id) {
        return areaCountryMapper.getCountryNameAndNumById(id);
    }

    private String validateAdd(AreaCountryDto areaCountryDto) {
        List<AreaCountry> list = getAreaCountryList(areaCountryDto);
        if (GeneralTool.isNotEmpty(list)) {
            StringBuilder sb = new StringBuilder();
            String resultMsg = null;
            for (AreaCountry areaCountry : list) {
                resultMsg = setValidMsg(areaCountryDto, sb, areaCountry);
            }
            return resultMsg;
        }
        return null;
    }

    private String validateUpdate(AreaCountryDto areaCountryDto) {
        List<AreaCountry> list = getAreaCountryList(areaCountryDto);
        if (GeneralTool.isNotEmpty(list)) {
            StringBuilder sb = new StringBuilder();
            String resultMsg = null;
            for (AreaCountry areaCountry : list) {
                if (!areaCountryDto.getId().equals(areaCountry.getId())) {
                    resultMsg = setValidMsg(areaCountryDto, sb, areaCountry);
                }
            }
            return resultMsg;
        }
        return null;
    }

    /**
     * @return java.lang.String
     * @Description :设置返回验证信息
     * @Param [areaCountryDto, sb, areaCountry]
     * <AUTHOR>
     */
    private String setValidMsg(AreaCountryDto areaCountryDto, StringBuilder sb, AreaCountry areaCountry) {
        if (areaCountry.getNum().equals(areaCountryDto.getNum())) {
            sb.append("国家编号已存在，");
        }
        if (areaCountry.getName().equals(areaCountryDto.getName())) {
            sb.append("国家名称已存在，");
        }
        if (areaCountry.getNameChn().equals(areaCountryDto.getNameChn())) {
            sb.append("国家中文名称已存在，");
        }
//        if (areaCountry.getFkCurrencyTypeNum().equals(areaCountryDto.getFkCurrencyTypeNum())) {
//            sb.append("币种编号已存在，");
//        }
        return sub(sb);
    }

    /**
     * @return java.lang.String
     * @Description :截取字符串逗号
     * @Param [sb]
     * <AUTHOR>
     */
    private String sub(StringBuilder sb) {
        if (GeneralTool.isEmpty(sb)) {
            return null;
        }
        String substring = null;
        int i = sb.lastIndexOf("，");
        if (i != -1) {
            substring = sb.substring(0, i);
        }
        return substring;
    }

    /**
     * @return java.util.List<com.get.institutioncenter.entity.AreaCountry>0
     * @Description :根据验证条件获取list
     * @Param [areaCountryDto]
     * <AUTHOR>
     */
    private List<AreaCountry> getAreaCountryList(AreaCountryDto areaCountryDto) {
        LambdaQueryWrapper<AreaCountry> wrapper = new LambdaQueryWrapper();
        wrapper.and(wrapper_ ->
                wrapper_.eq(AreaCountry::getName, areaCountryDto.getName()).or()
                        .eq(AreaCountry::getNameChn, areaCountryDto.getNameChn()));
        wrapper.eq(AreaCountry::getNum, areaCountryDto.getNum());
        wrapper.orderByDesc(AreaCountry::getViewOrder);
        return this.areaCountryMapper.selectList(wrapper);
    }


    //设置公开对象名称
    private void setPublicLevelName(AreaCountryVo areaCountryVo) {
        StringJoiner stringJoiner = new StringJoiner(",");
        if (GeneralTool.isNotEmpty(areaCountryVo.getPublicLevel())) {
            //把公开对象的序号用逗号割开
            List<String> result = Arrays.asList(areaCountryVo.getPublicLevel().split(","));
            for (String name : result) {
                String value = ProjectExtraEnum.getValueByKey(Integer.valueOf(name), ProjectExtraEnum.PUBLIC_OBJECTS);
                if(!value.equals("")&&value!=null){
                    stringJoiner.add(value);
                }
            }
            areaCountryVo.setPublicLevelName(stringJoiner.toString());
        }
    }

    @Override
    public List<AreaCountryVo> getCountryByKey(List<String> keys) {
        if (GeneralTool.isEmpty(keys)) {
            return null;
        }
        return areaCountryMapper.getCountryByKey(keys);
    }

    @Override
    public List<Long> getCountryByName(String name){
        if (GeneralTool.isEmpty(name)) {
            return null;
        }
        return areaCountryMapper.getCountryByName(name);
    }

    @Override
    public List<AreaCountryVo> getAreaCode() {
        List<AreaCountryVo> areaCodeList = areaCountryMapper.getAreaCode();

        // 中国区号+86置顶
        areaCodeList.sort(Comparator
                .nullsLast(Comparator.comparing(AreaCountryVo::getAreaCode,
                        Comparator.nullsLast(Comparator.comparing(areaCode ->
                                // 中国区号返回"0"确保排在最前面，其他返回原值
                                CHINA_AREA_CODE.equals(areaCode) ? "0" + areaCode : "1" + areaCode
                        ))
                ))
        );

        return areaCodeList;
    }

    /**
     * 根据国家ids 获取国家编号
     *
     * @Date 16:31 2022/3/5
     * <AUTHOR>
     */
    @Override
    public Map<Long, String> getCountryNumByCountryIds(Set<Long> countryIdIdSet) {
        List<AreaCountry> areaCountries = areaCountryMapper.selectBatchIds(countryIdIdSet);
        Map<Long, String> map = new HashMap<>();
        for (AreaCountry areaCountry : areaCountries) {
            map.put(areaCountry.getId(), areaCountry.getNum());
        }
        return map;
    }

    @Override
    public Map<Long, String> getCountryFullNamesByIds(Set<Long> ids) {
        Map<Long, String> map = new HashMap<>();
        if (GeneralTool.isEmpty(ids)) {
            return map;
        }
        List<AreaCountry> areaCountries = this.areaCountryMapper.selectList(Wrappers.<AreaCountry>query().lambda().in(AreaCountry::getId, ids).orderByDesc(AreaCountry::getViewOrder));
        for (AreaCountry areaCountry : areaCountries) {
            String countryName = GeneralTool.isEmpty(areaCountry.getName()) ? "" : areaCountry.getName();
            StringBuilder builder = new StringBuilder(countryName);
            if (GeneralTool.isNotEmpty(areaCountry.getNameChn())) {
                builder.append("（");
                builder.append(areaCountry.getNameChn());
                builder.append("）");
            }
            map.put(areaCountry.getId(), builder.toString());
        }
        return map;
    }

    @Override
    public Map<String, String> getCityChnNameById(Set<String> cNums) {
        Map<String, String> map = new HashMap<>();
        if (GeneralTool.isEmpty(cNums)) {
            return map;
        }
        List<AreaCountry> areaCountries = areaCountryMapper.selectList(Wrappers.<AreaCountry>lambdaQuery().in(AreaCountry::getNum, cNums));
        for (AreaCountry areaCountry : areaCountries) {
            map.put(areaCountry.getNum(),areaCountry.getNameChn());
        }
        return map;
    }

    /**
     * 获取对应公司下有申请计划的 业务国家下拉框数据
     *
     * @Date 12:28 2023/1/5
     * <AUTHOR>
     */
    @Override
    public List<BaseSelectEntity> getExistsOfferItemAreaCountryList(Long companyId) {
        return areaCountryMapper.getExistsOfferItemAreaCountryList(companyId, SecureUtil.getCountryIds());
    }

    /**
     * 获取对应公司下有申请计划的代理所在的 国家下拉框数据
     *
     * @Date 15:22 2023/1/5
     * <AUTHOR>
     */
    @Override
    public List<BaseSelectEntity> getExistsAgentOfferItemAreaCountryList(Long companyId) {
        if (GeneralTool.isEmpty(SecureUtil.getCountryIds())) {
            return null;
        }
        return areaCountryMapper.getExistsAgentOfferItemAreaCountryList(companyId, SecureUtil.getCountryIds());
    }

    /**
     * 获取对应公司下的代理所在的 国家下拉框数据
     *
     * @Date 10:40 2023/3/15
     * <AUTHOR>
     */
    @Override
    public List<BaseSelectEntity> getExistsAgentAreaCountryList(Long companyId) {
        if (GeneralTool.isEmpty(SecureUtil.getCountryIds())) {
            return null;
        }
        return areaCountryMapper.getExistsAgentAreaCountryList(companyId, SecureUtil.getCountryIds());
    }

    @Override
    public List<BaseSelectEntity> getNewsAreaCountryList(){
       return areaCountryMapper.getNewsAreaCountryList();
    }

    @Override
    public List<AreaCountryVo> getCountryByPublicLevel(Integer publicLevel) {
        return areaCountryMapper.getCountryByPublicLevel(publicLevel);
    }

    /**
     * 根据国家ids 获取国家对象Map
     *
     * @Date 17:44 2023/12/18
     * <AUTHOR>
     */
    @Override
    public Map<Long, AreaCountryVo> getCountryDtoMapByIds(Set<Long> countryIds) {
        Map<Long, AreaCountryVo> map = new HashMap<>();
        if (GeneralTool.isEmpty(countryIds)) {
            return map;
        }
        List<AreaCountry> areaCountries = this.areaCountryMapper.selectList(Wrappers.<AreaCountry>query().lambda().in(AreaCountry::getId, countryIds).orderByDesc(AreaCountry::getViewOrder));
        if (GeneralTool.isEmpty(areaCountries)) {
            return map;
        }
        return areaCountries.stream().collect(Collectors.toMap(AreaCountry::getId, areaCountry -> BeanCopyUtils.objClone(areaCountry, AreaCountryVo::new)));
    }

    @Override
    public AreaCountry getCountryById(Long id) {
        return areaCountryMapper.selectById(id);
    }

    /**
     * 根据国家key集合获取对应的id
     *
     * @param keys 国家keys
     * @return
     */
    @Override
    public Map<String, Long> getCountryIdByKeys(Set<String> keys) {
        if (GeneralTool.isEmpty(keys)) {
            return Collections.emptyMap();
        }
        Map<String, Long> res = new HashMap<>();
        List<AreaCountry> areaCountryList = areaCountryMapper.selectList(Wrappers.<AreaCountry>query().lambda().in(AreaCountry::getNum, keys));
        if (GeneralTool.isNotEmpty(areaCountryList)) {
            res = areaCountryList.stream().collect(Collectors.toMap(AreaCountry::getNum, AreaCountry::getId));
        }
        return res;
    }

    @Override
    public List<AreaCountryVo> getCommonAreaCodes() {
        return areaCountryMapper.getCommonAreaCodes();
    }

    /**
     *  获取国家名称Map
     * @return
     */
    @Override
    public Map<Long, String> getCountryNameMap() {
        Map<Long, String> map = new HashMap<>();
        List<AreaCountry> areaCountries = this.areaCountryMapper.selectList(Wrappers.query());
        for (AreaCountry areaCountry : areaCountries) {
            String countryName = GeneralTool.isEmpty(areaCountry.getName()) ? "" : areaCountry.getName();
            StringBuilder builder = new StringBuilder(countryName);

            map.put(areaCountry.getId(), builder.toString());
        }
        return map;
    }

    @Override
    public List<AreaRegionVo> getGeographicalRegions() {
        return  areaRegionMapper.getGeographicalRegions();
    }

    @Override
    public List<AreaCountryVo> getCountryByPublicLevelBySummit(Integer publicLevel) {
        return areaCountryMapper.getCountryByPublicLevelBySummit(publicLevel);
    }

}
