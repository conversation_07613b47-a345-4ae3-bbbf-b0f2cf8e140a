package com.get.financecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.financecenter.dto.SearchActivityDataDto;
import com.get.financecenter.service.ICostService;
import com.get.financecenter.vo.CostVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "费用类型管理")
@RestController
@RequestMapping("finance/cost")
public class CostController {
    @Resource
    private ICostService costService;

    @ApiOperation(value = "根据活动信息分页查询费用列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.LIST, description = "财务中心/费用类型管理/根据活动信息分页查询费用列表数据")
    @PostMapping("getCostByActivityData")
    public ResponseBo<CostVo> getCostByActivityData(@RequestBody SearchBean<SearchActivityDataDto> page) {
        List<CostVo> datas = costService.getCostByActivityData(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    //获取费用列表的总金额
    @ApiOperation(value = "获取费用列表的总金额", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.LIST, description = "财务中心/费用类型管理/获取费用列表的总金额")
    @PostMapping("getCostTotalAmount")
    public ResponseBo getCostTotalAmount(@RequestBody SearchBean<SearchActivityDataDto> page) {
        return new ResponseBo<>(costService.getCostTotalAmount(page.getData()));
    }
}
