package com.get.institutioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.mapper.GetMapper;
import com.get.institutioncenter.dto.InstitutionChannelCompanyQueryDto;
import com.get.institutioncenter.dto.InstitutionChannelCompanySearchDto;
import com.get.institutioncenter.dto.InstitutionProviderDto;
import com.get.institutioncenter.dto.query.InstitutionProviderQueryDto;
import com.get.institutioncenter.entity.InstitutionProvider;
import com.get.institutioncenter.entity.InstitutionProviderInstitution;
import com.get.institutioncenter.vo.InstitutionProviderContractReminderVo;
import com.get.institutioncenter.vo.InstitutionProviderInstitutionChannelVo;
import com.get.institutioncenter.vo.InstitutionProviderVo;
import com.get.institutioncenter.vo.InstitutionProvidersAndAreaCountryVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Set;

@Mapper
public interface InstitutionProviderMapper extends BaseMapper<InstitutionProvider>, GetMapper<InstitutionProvider> {
    @Override
    int insert(InstitutionProvider record);

    int insertSelective(InstitutionProvider record);

    /**
     * feign调用 根据输入的学校提供商名称 模糊查询对应的学校提供商id
     *
     * @param institutionProviderName
     * @return
     */
    List<Long> getInstitutionProviderIds(String institutionProviderName);

    /**
     * 根据提供商名称关键字，模糊查询提供商
     *
     * <AUTHOR>
     * @DateTime 2023/12/19 17:28
     */
    List<InstitutionProviderVo> getInstitutionProvidersByName(@Param("keyword") String keyword);

    /**
     * feign调用 根据输入的学校提供商id 查询对应的学校提供商名称
     *
     * @param id
     * @return
     */
    String getInstitutionProviderNameById(Long id);

    /**
     * feign调用 根据学校提供商id查找学校提供商类型
     *
     * @param id
     * @return
     */
    String getInstitutionProviderTypeById(Long id);

    /**
     * 获取学校下面的提供商列表
     *
     * @param institutionId
     * @return
     */
    Set<Long> getInstitutionProviderByInstitution(@Param("institutionId") Long institutionId);

    /**
     * 获取提供商列表
     *
     * @param institutionProviderDto
     * @return
     */
    List<InstitutionProviderVo> getInstitutionProviders(IPage<InstitutionProvider> pages, @Param("institutionProviderDto") InstitutionProviderDto institutionProviderDto);

    /**
     * 公司对应下得学校提供商下拉框数据
     *
     * @param companyIds
     * @param name
     * @return
     */
    List<InstitutionProviderVo> getInstitutionProviderList(@Param("companyIds") List<Long> companyIds, @Param("name") String name);

    /**
     * 国家对应下得学校提供商下拉框数据
     *
     * @param countryId
     * @return
     */
    List<BaseSelectEntity> getInstitutionProviderListByCountry(@Param("countryId") Long countryId, @Param("companyId") Long companyId);

    /**
     * 根据学校获取绑定的学校提供商下拉框数据
     *
     * @Date 16:06 2021/8/18
     * <AUTHOR>
     */
    List<BaseSelectEntity> getInstitutionProviderListByInstitution(@Param("institutionId") Long institutionId,
                                                                   @Param("studentCompanyId") Long studentCompanyId,
                                                                   @Param("companyIds") List<Long> companyIds);

    /**
     * 获取提供商列表
     *
     * @param institutionProviderDto
     * @param companyIds
     * @return
     */
    List<InstitutionProviderVo> getProviderList(IPage<InstitutionProvider> pages, @Param("institutionProviderDto") InstitutionProviderDto institutionProviderDto, @Param("companyIds") List<Long> companyIds);

    /**
     * @return java.util.List<com.get.core.mybatis.base.BaseSelectEntity>
     * @Description: 根据公司id查提供商
     * @Param [companyId]
     * <AUTHOR>
     */
    List<BaseSelectEntity> getInstitutionProviderSelect(Long companyId);

    /**
     * 根据提供商id获取业务国家下拉框数据
     *
     * @Date 10:34 2021/9/9
     * <AUTHOR>
     */
    List<BaseSelectEntity> getCountrySelectByInstitutionProvider(@Param("providerId") Long providerId);


    /**
     * 根据公司id获取提供商ids
     *
     * @param companyId
     * @return
     */
    List<Long> getInstitutionProviderIdsByCompanyId(@Param("companyId") Long companyId);

    /**
     * 根据公司ids获取提供商ids
     *
     * @param fkCompanyIds
     * @return
     */
    List<Long> getInstitutionProviderIdsByCompanyIds(@Param("fkCompanyIds") List<Long> fkCompanyIds);

    /**
     * 根据渠道名称获取提供商ids
     *
     * @param channelName
     * @return
     */
    List<Long> getInstitutionProviderIdsByChannelName(@Param("channelName") String channelName);

    /**
     * 根据集团名称获取提供商ids
     *
     * @param
     * @return
     */
    List<Long> getInstitutionProviderIdsByChannelGroup(@Param("groupId") Long groupId, @Param("channelId") Long channelId);


    /**
     * 查询合同过期的学校提供商id
     * @return
     */
    List<Long> getExpiredContractSchoolProviderIds();

    /**
     * 查询合同生效中的学校提供商id
     */
    List<Long> getActiveContractSchoolProviderIds();



    // List<InstitutionProviderContractReminderVo> getProvidersWithContractsExpiringInDays(int day, int contractStatus);

    /**
     * 根据名称和公司id模糊搜索
     *
     * @param companyIds
     * @param institutionProviderName
     * @return
     */
    List<Long> getInstitutionProviderIdsByCompanyIdAndName(@Param("companyIds") List<Long> companyIds, @Param("institutionProviderName") String institutionProviderName);

    List<InstitutionProviderInstitutionChannelVo> getInstitutionProviderByName(@Param("queryVo") InstitutionChannelCompanyQueryDto queryVo);

    List<InstitutionProviderInstitutionChannelVo> getInstitutionProviderByProviderName(@Param("queryVo") InstitutionChannelCompanySearchDto queryVo);

    List<BaseSelectEntity> getInstitutionSelectByCompanyId(@Param("companyId") Long companyId);

    List<BaseSelectEntity> getInstitutionProviderByTargetName(@Param("targetName") String targetName);

    List<BaseSelectEntity> getInstitutionChannelProviderNamesByIds(@Param("providerIds") Set<Long> providerIds);

    List<InstitutionProviderInstitution> getInstitutionByProvider(@Param("fkCompanyIds") Set<Long> fkCompanyIds,
                                                                  @Param("fkInstitutionProviderIds") Set<Long> fkInstitutionProviderIds);

    List<InstitutionProvidersAndAreaCountryVo> getInstitutionProvidersAndAreaCountryById(@Param("id") Long id);


    /**
     * 根据合同状态查找学校提供商
     * @param pages
     * @param providerQueryDto
     * @param providerIdBYCompanyIds
     * @param areaCountryProviderIds
     * @param institutionChannelIds
     * @param status
     * @return
     */
    List<InstitutionProviderVo> findByContractStatus(IPage<InstitutionProviderVo> pages,
                                                   @Param("providerVo") InstitutionProviderQueryDto providerQueryDto,
                                                   @Param("providerIdBYCompanyIds") List<Long> providerIdBYCompanyIds,
                                                   @Param("areaCountryProviderIds")List<Long> areaCountryProviderIds,
                                                   @Param("institutionChannelIds")List<Long> institutionChannelIds);



    /**
     * 根据合同id获取学校提供商
     * @param contractId
     * @return
     */
    InstitutionProviderContractReminderVo getContractExpiredByProviderId(@RequestParam("contractId") Long contractId);

    /**
     * 根据合同id获取合同涉及国家
     * @param id
     * @return
     */
    List<String> getContractApplyCountryByContractId(@Param("id") Long id);

    InstitutionProviderVo findByContractStatusById(@Param("id") Long id);
}