package com.get.financecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.financecenter.dto.SearchActivityDataDto;
import com.get.financecenter.service.IExpenseClaimFormItemService;
import com.get.financecenter.vo.ActivityFinancialSummaryVo;
import com.get.financecenter.vo.ExpenseClaimFormAndItemVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "费用报销单子项管理")
@RestController
@RequestMapping("finance/expenseClaimFormItem")
public class ExpenseClaimFormItemController {
@Resource
private IExpenseClaimFormItemService expenseClaimFormItemService;

    @ApiOperation(value = "根据活动信息查询费用所有报销单列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.LIST, description = "财务中心/费用报销单子项管理/根据活动信息查询费用所有报销单列表数据")
    @PostMapping("getAllExpenseClaimFormByActivityData")
    public ResponseBo<ExpenseClaimFormAndItemVo> getAllExpenseClaimFormByActivityData(@RequestBody SearchActivityDataDto searchActivityDataDto) {
        return new ListResponseBo<>(expenseClaimFormItemService.getAllExpenseClaimFormItemByActivityData(searchActivityDataDto));
    }

    @ApiOperation(value = "根据活动信息分页查询费用报销单列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.LIST, description = "财务中心/费用报销单子项管理/根据活动信息分页查询费用报销单列表数据")
    @PostMapping("getExpenseClaimFormByActivityData")
    public ResponseBo<ExpenseClaimFormAndItemVo> getExpenseClaimFormByActivityData(@RequestBody SearchBean<SearchActivityDataDto> page) {
        List<ExpenseClaimFormAndItemVo> datas = expenseClaimFormItemService.getExpenseClaimFormByActivityData(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    //    根据活动表名和id查询活动费用和费用报销单金额
    @ApiOperation(value = "根据活动表名和id查询活动费用和费用报销单相关金额", notes = "id为活动id")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.LIST, description = "财务中心/费用报销单子项管理/查询活动费用和费用报销单相关金额")
    @PostMapping("getActivityFinancialSummary")
    public ResponseBo<ActivityFinancialSummaryVo> getActivityFinancialSummary (@RequestBody SearchActivityDataDto searchActivityDataDto) {
        return new ResponseBo<>(expenseClaimFormItemService.getActivityFinancialSummary( searchActivityDataDto ));
    }

}
